# QT埋点集成配置说明

## 概述
本项目已集成阿里云Quick Tracking (QT) 埋点SDK，用于统计分析和用户行为追踪。

## 已完成的配置

### 1. Flutter依赖
- 已在 `pubspec.yaml` 中添加 `qt_common_sdk: ^2.0.4`

### 2. Flutter代码集成
- **主入口配置**: 在 `lib/main.dart` 中添加了QT SDK初始化
- **工具类**: 创建了 `lib/utils/qt_analytics_util.dart` 统一管理埋点
- **基础页面类**: 创建了 `lib/common/base/base_page_with_analytics.dart` 自动处理页面埋点
- **现有埋点增强**: 更新了 `lib/common/base/base_function.dart` 集成QT埋点

### 3. Android端配置
- **App类**: 创建了 `.android/app/src/main/java/com/ybm100/app/crm/flutter/host/App.java`
- **清单文件**: 更新了 `.android/app/src/main/AndroidManifest.xml` 添加App类配置
- **混淆配置**: 创建了 `.android/app/proguard-rules.pro` 防止QT SDK被混淆
- **构建配置**: 更新了 `.android/app/build.gradle` 启用混淆

## 需要完成的配置

### 1. 获取AppKey
请在阿里云QT控制台获取您的AppKey：
1. 登录阿里云控制台
2. 进入Quick Tracking产品页面
3. 在"管理->应用管理->应用列表"或"我的产品->设置->应用信息"中查看AppKey

### 2. 更新AppKey配置
需要在以下文件中替换实际的AppKey：

#### Flutter端 (lib/main.dart)
```dart
QTCommonSdk.initCommon(
  "YOUR_ANDROID_APPKEY", // 替换为您的Android AppKey
  "YOUR_IOS_APPKEY",     // 替换为您的iOS AppKey
  "default"              // 渠道标识
);
```

#### Android端 (.android/app/src/main/java/com/ybm100/app/crm/flutter/host/App.java)
```java
QtConfigure.preInit(this, "YOUR_ANDROID_APPKEY", "default");
```

### 3. iOS端配置 (如果需要)
如果您的应用需要iOS支持，还需要在iOS端添加相应配置。

### 4. 隐私政策合规
根据工信部要求，需要在《隐私政策》中添加以下条款：
"我们的产品集成SDK，SDK需要收集您的设备识别码（IDFA/IDFV/OPENUDID/GUID/）以提供统计分析服务，并通过地理位置校准报表数据准确性，提供基础反作弊能力。"

## 使用方法

### 1. 基础埋点
```dart
import 'package:XyyBeanSproutsFlutter/utils/qt_analytics_util.dart';

// 自定义事件埋点
QTAnalyticsUtil.trackEvent('button_click', properties: {
  'button_name': 'login',
  'page_name': 'login_page'
});

// 页面访问埋点
QTAnalyticsUtil.trackPageView('home_page');

// 用户登录埋点
QTAnalyticsUtil.trackUserLogin('user123');
```

### 2. 使用基础页面类
```dart
class MyPage extends BasePageWithAnalytics {
  @override
  String getPageName() => "my_page";
  
  @override
  Map<String, dynamic>? getPageProperties() => {
    'page_type': 'main',
    'version': '1.0',
  };
  
  @override
  _MyPageState createState() => _MyPageState();
}

class _MyPageState extends BasePageWithAnalyticsState<MyPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ElevatedButton(
        onPressed: () {
          // 按钮点击埋点
          trackButtonClick('submit_button');
        },
        child: Text('提交'),
      ),
    );
  }
}
```

### 3. 现有代码兼容
现有的 `track()` 方法会自动同时使用原有埋点和QT埋点，无需修改现有代码。

## 常用埋点事件

### 业务埋点
- `QTAnalyticsUtil.trackButtonClick()` - 按钮点击
- `QTAnalyticsUtil.trackSearch()` - 搜索行为
- `QTAnalyticsUtil.trackProductView()` - 商品查看
- `QTAnalyticsUtil.trackOrder()` - 订单相关操作

### 页面埋点
- `QTAnalyticsUtil.trackPageStart()` - 页面开始
- `QTAnalyticsUtil.trackPageEnd()` - 页面结束
- `QTAnalyticsUtil.trackPageView()` - 页面访问

### 用户埋点
- `QTAnalyticsUtil.trackUserLogin()` - 用户登录
- `QTAnalyticsUtil.trackUserLogout()` - 用户登出

## 调试
- 开发环境下，QT SDK日志已开启，可在控制台查看埋点日志
- 正式发布前，请将 `QTCommonSdk.setLogEnabled(false)` 关闭日志

## 注意事项
1. 请确保在用户同意隐私政策后再初始化QT SDK
2. AppKey必须与控制台配置一致
3. 正式发布前关闭调试日志
4. 定期检查埋点数据是否正常上报
