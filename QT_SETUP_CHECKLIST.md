# QT埋点集成完成检查清单

## ✅ 已完成的配置

### 1. Flutter依赖配置
- [x] 在 `pubspec.yaml` 中添加了 `qt_common_sdk: ^2.0.4`

### 2. Flutter代码集成
- [x] 在 `lib/main.dart` 中导入QT SDK
- [x] 在 `lib/main.dart` 中添加 `initQTSDK()` 初始化方法
- [x] 创建了 `lib/utils/qt_analytics_util.dart` 工具类
- [x] 创建了 `lib/common/base/base_page_with_analytics.dart` 基础页面类
- [x] 更新了 `lib/common/base/base_function.dart` 集成QT埋点
- [x] 创建了 `lib/example/qt_analytics_demo_page.dart` 演示页面
- [x] 在路由中添加了演示页面路由

### 3. Android端配置
- [x] 创建了 `.android/app/src/main/java/com/ybm100/app/crm/flutter/host/App.java`
- [x] 更新了 `.android/app/src/main/AndroidManifest.xml` 添加App类配置
- [x] 创建了 `.android/app/proguard-rules.pro` 混淆配置
- [x] 更新了 `.android/app/build.gradle` 启用混淆和proguard

### 4. 文档和示例
- [x] 创建了 `QT_ANALYTICS_SETUP.md` 详细配置说明
- [x] 创建了 `QT_SETUP_CHECKLIST.md` 检查清单
- [x] 提供了完整的使用示例

## ✅ 已完成的配置（更新）

### AppKey配置状态
- [x] **Android AppKey**: 已配置 `mn3vwmgc6uvxm0wnmz34bz19`
- [x] **iOS AppKey**: 已配置（暂时使用相同AppKey）
- [x] **编译错误**: 已修复 `WidgetsBinding.instance` 空安全问题

## ⚠️ 可选的进一步配置

### 1. iOS专用AppKey（可选）
如果您需要iOS专用的AppKey，请在阿里云QT控制台获取iOS AppKey并替换：

**Flutter端** (`lib/main.dart` 第219行):
```dart
QTCommonSdk.initCommon(
  "mn3vwmgc6uvxm0wnmz34bz19", // Android AppKey ✅ 已配置
  "YOUR_IOS_APPKEY",          // 🔴 如需iOS专用AppKey请替换
  "default"                   // 渠道标识
);
```

**Android端** (`.android/app/src/main/java/com/ybm100/app/crm/flutter/host/App.java` 第11行):
```java
QtConfigure.preInit(this, "mn3vwmgc6uvxm0wnmz34bz19", "default"); // ✅ 已配置
```

### 2. 隐私政策合规
在您的《隐私政策》中添加以下条款：
```
我们的产品集成SDK，SDK需要收集您的设备识别码（IDFA/IDFV/OPENUDID/GUID/）以提供统计分析服务，并通过地理位置校准报表数据准确性，提供基础反作弊能力。
```

### 3. 生产环境配置
发布前请确保：
- [x] 将 `QTCommonSdk.setLogEnabled(false)` 关闭调试日志
- [x] 确保AppKey配置正确
- [x] 测试埋点数据是否正常上报

## 🚀 如何测试

### 1. 访问演示页面
在您的应用中导航到演示页面：
```dart
Navigator.pushNamed(context, '/qt_analytics_demo');
```

### 2. 运行自动化测试
在演示页面中点击"运行测试"按钮，会自动执行完整的QT埋点测试。

### 3. 查看日志
开发环境下，控制台会输出QT埋点日志，格式如：
```
🚀 开始运行QT埋点完整测试...
✅ 自定义事件埋点测试通过
✅ 页面埋点测试通过
QT Track Event: button_click, Properties: {button_name: test, page_name: demo}
QT Page Start: qt_analytics_demo
```

### 4. 在QT控制台查看数据
- 登录阿里云QT控制台
- 查看实时数据和统计报表
- 验证埋点事件是否正常上报

## 📋 使用方法

### 基础埋点
```dart
import 'package:XyyBeanSproutsFlutter/utils/qt_analytics_util.dart';

// 事件埋点
QTAnalyticsUtil.trackEvent('button_click', properties: {'button_name': 'login'});

// 页面埋点
QTAnalyticsUtil.trackPageView('home_page');

// 用户行为埋点
QTAnalyticsUtil.trackUserLogin('user123');
```

### 使用基础页面类
```dart
class MyPage extends BasePageWithAnalytics {
  @override
  String getPageName() => "my_page";
  
  @override
  _MyPageState createState() => _MyPageState();
}

class _MyPageState extends BasePageWithAnalyticsState<MyPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ElevatedButton(
        onPressed: () => trackButtonClick('my_button'),
        child: Text('按钮'),
      ),
    );
  }
}
```

### 现有代码兼容
现有的 `track()` 方法会自动同时使用原有埋点和QT埋点，无需修改现有代码。

## 🔧 故障排除

### 常见问题
1. **埋点数据不上报**: 检查AppKey是否正确配置
2. **编译错误**: 确保已运行 `flutter pub get`
3. **Android构建失败**: 检查App类和AndroidManifest.xml配置
4. **日志不显示**: 确保 `setLogEnabled(true)` 已设置

### 联系支持
如果遇到问题，可以：
1. 查看阿里云QT官方文档
2. 检查控制台错误日志
3. 联系阿里云技术支持

## ✨ 下一步

1. ✅ **AppKey配置**: 已完成Android AppKey配置
2. 📱 **立即测试**: 运行演示页面验证功能
3. 📊 **监控数据**: 在QT控制台查看数据上报
4. 🔧 **可选配置**: 如需iOS专用AppKey请替换
5. 🚀 **发布应用**: 确认一切正常后发布应用

---

**当前状态**: QT埋点已配置完成，可以立即开始测试和使用！
