package com.ybm100.app.crm.contract.drugstore;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.contact.ContactBean;
import com.ybm100.app.crm.bean.drugstore.PublicCustomerDetailBean;

import java.util.List;

import io.reactivex.Observable;

/**
 * <AUTHOR>
 * @date 2019/1/7
 */
public interface PublicCustomerDetailContract {

    interface IPublicCustomerDetailModel extends IBaseModel {
        //我的药店列表
        Observable<RequestBaseBean<PublicCustomerDetailBean>> searchOpenSeaDetail(String id);

        //认领
        Observable<RequestBaseBean> receive(String id);

    }
    interface IBaseInfoPublicCustomerDetailModel extends IPublicCustomerDetailModel{
        @Override
        Observable<RequestBaseBean<PublicCustomerDetailBean>> searchOpenSeaDetail(String id);

        @Override
        Observable<RequestBaseBean> receive(String id);

        //荷叶健康基本信息联系人列表
        Observable<RequestBaseBean<List<ContactBean>>> getContactList(String poiId);
    }

    interface IPublicCustomerDetailView extends IBaseActivity {

        void searchOpenSeaDetailSuccess(RequestBaseBean<PublicCustomerDetailBean> baseBean);

        void receiveSuccess(RequestBaseBean requestBaseBean,String merchantId);

    }

    interface IBaseInfoPublicCustomerDetailView extends IPublicCustomerDetailView{
        @Override
        void searchOpenSeaDetailSuccess(RequestBaseBean<PublicCustomerDetailBean> baseBean);

        @Override
        void receiveSuccess(RequestBaseBean requestBaseBean,String merchantId);

        void getContactListSuccess(RequestBaseBean<List<ContactBean>> baseBean);
    }
}
