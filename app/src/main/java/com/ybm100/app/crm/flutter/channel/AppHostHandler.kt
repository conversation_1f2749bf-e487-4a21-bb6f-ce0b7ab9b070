package com.ybm100.app.crm.flutter.channel

import androidx.fragment.app.FragmentActivity
import com.xyy.flutter.container.container.bridge.BaseHandler
import com.ybm100.app.crm.api.ApiUrl
import com.ybm100.app.crm.net.BaseUrl
import com.ybm100.app.crm.platform.RuntimeEnv.imageHost

class AppHostHandler : BaseHandler() {
    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {
        val resultMap = mapOf("image_host" to "$imageHost/ybm/product/min/",
                "big_image_host" to imageHost,
                "small_image_host" to "$imageHost/ybm/product/min/",
                "h5_host" to "${ApiUrl.getH5BaseUrl()}pdc-mobile/#",
                "hy_h5_host" to ApiUrl.getHYH5Domain(),
                "license_image_host" to ApiUrl.CDN_URL,
                "interface" to BaseUrl.getBaseUrl().removeSuffix("/app/crm/"))
        result?.success(resultMap)
    }

}