package com.ybm100.app.crm.flutter.channel

import androidx.fragment.app.FragmentActivity
import com.xyy.flutter.container.container.bridge.BaseHandler
import com.ybm100.app.crm.utils.SharedPrefManager

class ApiEnvHandler : BaseHandler() {
    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {
        // dev、stage、beta、prod、自定义
        result?.success(SharedPrefManager.getInstance().currFlavor)
    }

}