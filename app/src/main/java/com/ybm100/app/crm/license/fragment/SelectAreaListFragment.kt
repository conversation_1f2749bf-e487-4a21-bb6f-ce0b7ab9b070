package com.ybm100.app.crm.license.fragment

import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import android.view.View
import com.xyy.common.widget.DefaultItemDecoration
import com.xyy.utilslibrary.base.fragment.BaseCompatFragment
import com.ybm100.app.crm.R
import com.ybm100.app.crm.bean.license.DeliveryAddressBean
import com.ybm100.app.crm.license.adapter.SelectAreaListAdapter
import kotlinx.android.synthetic.main.fragment_select_area_list.*

/**
 * @author: zcj
 * @time:2020/7/1.
 * Description:
 */
class SelectAreaListFragment : BaseCompatFragment() {

    private var mAdapter: SelectAreaListAdapter? = null
    private var item: List<DeliveryAddressBean>? = null
    private var mListener: SelectAreaListAdapter.ActionListener? = null
    override fun initUI(view: View?, savedInstanceState: Bundle?) {
        initRecyclerView()
    }

    override fun getLayoutId(): Int = R.layout.fragment_select_area_list


    private fun initRecyclerView() {
        rcy_select_area.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(context)
        rcy_select_area.addItemDecoration(DefaultItemDecoration(context))
        mAdapter = SelectAreaListAdapter()
        rcy_select_area.adapter = mAdapter
        if (item != null) {
            mAdapter?.setNewData(item)
            mAdapter?.setActionListener(mListener)
        }
    }

    override fun onBackPressedSupport(): Boolean {
        return false
    }
    fun setData(item: List<DeliveryAddressBean>, mListener: SelectAreaListAdapter.ActionListener) {
        this.item = item
        this.mListener = mListener
        mAdapter?.setNewData(item)
        mAdapter?.setActionListener(mListener)
    }

    fun refreshData() {
        if (mAdapter != null) mAdapter!!.notifyDataSetChanged()
    }

}