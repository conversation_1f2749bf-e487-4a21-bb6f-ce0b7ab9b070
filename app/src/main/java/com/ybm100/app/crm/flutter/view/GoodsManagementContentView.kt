package com.ybm100.app.crm.flutter.view

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.graphics.Color
import android.graphics.Rect
import android.os.Bundle
import android.os.Handler
import android.text.TextUtils
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.GravityCompat
import androidx.drawerlayout.widget.DrawerLayout
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.fold.recyclyerview.flexibledivider.HorizontalDividerItemDecoration
import com.scwang.smartrefresh.layout.SmartRefreshLayout
import com.scwang.smartrefresh.layout.api.RefreshLayout
import com.scwang.smartrefresh.layout.listener.OnRefreshLoadMoreListener
import com.xyy.common.util.ConvertUtils
import com.xyy.common.util.ScreenUtils
import com.xyy.common.util.ToastUtils
import com.xyy.common.widget.RoundTextView
import com.xyy.userbehaviortracking.utils.UserBehaviorTrackingUtils
import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.activity.BaseCompatActivity
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.widgets.WaitProgressDialog
import com.ybm100.app.crm.R
import com.ybm100.app.crm.constant.Constants
import com.ybm100.app.crm.goods.bean.CategoryList
import com.ybm100.app.crm.goods.ui.GoodsCategorySelectionPopup
import com.ybm100.app.crm.goods.ui.GoodsDetailActivity
import com.ybm100.app.crm.goodsmanagement.activity.BaseSearchActivity
import com.ybm100.app.crm.goodsmanagement.activity.BaseSearchResultActivity
import com.ybm100.app.crm.goodsmanagement.activity.GoodsManagementActivity
import com.ybm100.app.crm.goodsmanagement.activity.GoodsManagementSelectGoodsActivity
import com.ybm100.app.crm.goodsmanagement.adapter.GoodsManagementAdapter
import com.ybm100.app.crm.goodsmanagement.bean.*
import com.ybm100.app.crm.goodsmanagement.contract.GoodsManagementContract
import com.ybm100.app.crm.goodsmanagement.fragment.CustomerGoodsManagementFilterFragment
import com.ybm100.app.crm.goodsmanagement.fragment.GoodsManagementFragment
import com.ybm100.app.crm.goodsmanagement.presenter.GoodsManagementPresenter
import com.ybm100.app.crm.task.activity.SelectCustomersActivity
import com.ybm100.app.crm.task.bean.ShareConfirm
import com.ybm100.app.crm.utils.ShareHelper
import com.ybm100.app.crm.utils.toInt
import com.ybm100.app.crm.widget.BadgeView
import com.ybm100.app.crm.widget.CustomBottomSheetDialog
import com.ybm100.app.crm.widget.ShareGoodsDialog
import kotlinx.android.synthetic.main.activity_base_drawer_v2.view.*
import kotlinx.android.synthetic.main.bottom_cart.view.*
import kotlinx.android.synthetic.main.fragment_goods_management.view.*
import kotlinx.android.synthetic.main.fragment_message_root.view.*
import me.yokeyword.fragmentation.SupportFragment

/**
 * prd 概念为区域筛选，后台实际是分公司，每个区域对应一家公司
 * areaCode = ecCode
 */
class GoodsManagementContentView(
    val mMerchantID: String,
    val mType: Int,
    var mSearchKeyword: String = "",
    var mAreaCode: String = "",
    context: Context
) :
    FrameLayout(context, null, 0),
    GoodsManagementContract.IGoodsManagementView, View.OnClickListener, OnRefreshLoadMoreListener,
    CustomerGoodsManagementFilterFragment.CustomerDrawerListener {

    lateinit var mPresenter: GoodsManagementPresenter


    private var mAdapter: GoodsManagementAdapter? = null
    private var mIsRefresh = true
    private var mDividerLineMarginStart = 15f
    private var mSortTypeSales = GoodsManagementFragment.SortType.NORMAL
    private var mSortTypePrice = GoodsManagementFragment.SortType.NORMAL
    private var isOnSale = false
    private var mCartLayout: View? = null
    private var mCartDialog: CustomBottomSheetDialog? = null
    private var mCartAdapter: GoodsManagementAdapter? = null
    private var shareType: ShareHelper.Companion.SocialMedia? = null
    private val mMaxCartHeight =
        ConvertUtils.px2dp((ScreenUtils.getScreenHeight() * 0.9).toFloat()).toFloat()
    private var mVarietyPopup: GoodsCategorySelectionPopup? = null
    private var mAreaList: List<CategoryList?>? = null
    private var mOrgCode: String = ""

    /**
     * 埋点相关
     */
    private var mSnowGroundKeySales: String = ""
    private var mSnowGroundKeyPrice: String = ""
    private var mSnowGroundKeyCategory: String = ""
    private var mSnowGroundKeyPromotion: String = ""
    private var mSnowGroundKeyFilter: String = ""
    private var mSnowGroundKeyCollect: String = ""
    private var mSnowGroundKeyUncollect: String = ""
    private var mSnowGroundKeyConfirmRecommendation: String = ""
    private var mSnowGroundKeyShareToYBM: String = ""
    private var mSnowGroundKeyShareToWeChat: String = ""
    private var mSnowGroundKeyShareToWeChatCircle: String = ""


    private var mShouldRequestVariety = true

    private var isInit = false

    var refreshLayout
            : SmartRefreshLayout? = null//主要在P层使用，p层根据返回成功或者失败来恢复原位，如调用finishRefresh()

    /**
     * 是否需要请求预估到手价接口
     */
    private var mShouldFetchEstimatedPrices = false
    private var mEstimatedPriceList: List<EstimatedPriceListBean.Row?>? = null

    enum class SortType {
        NORMAL, ASC, DESC
    }


    private var mDrawerFragments: List<Fragment> = listOf()
    private var drawerLayout: CustomerGoodsManagementFilterView? = null
    private lateinit var mCustomerDrawerFragment: Fragment


    fun getLayoutId(): Int {
        return R.layout.activity_base_drawer_v2
    }

    fun initUI() {
        registerListener()

        initSmartRefreshLayout()

        initRecyclerView()

        initCartDialog()

        initViewAndQueryMap()

        /**
         * 测滑菜单初始化
         */
        initDrawLayout()

        mPresenter.getGoodsList(true)

    }

    private fun initDrawLayout() {
        if (drawerLayout == null) {
            drawerLayout = CustomerGoodsManagementFilterView(mMerchantID, context).also {
                it.layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
                it.setCustomerDrawerListener(this)
            }
            drawer_fragment_container.addView(drawerLayout)
        }
        drawer_layout.apply {
            setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED)
            //TODO 菜单关闭的时候 可以优化
            addDrawerListener(object : DrawerLayout.SimpleDrawerListener() {
                override fun onDrawerOpened(drawerView: View) {
                    super.onDrawerOpened(drawerView)
                    setDrawerLockMode(DrawerLayout.LOCK_MODE_UNLOCKED)
                }

                override fun onDrawerClosed(drawerView: View) {
                    super.onDrawerClosed(drawerView)
                    setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED)
                }
            })
        }
    }

    fun openDrawer(position: Int = 0) {
        drawer_layout.openDrawer(GravityCompat.END)
    }

    fun closeDrawer() {
        drawer_layout.closeDrawer(GravityCompat.END)
    }

    fun syncCustomerDrawerFilterData(YBMFilterPos: Int) {
        drawerLayout?.syncCustomerDrawerFilterData(YBMFilterPos)
    }


    fun getDrawerFragments(): List<Fragment> {
        mCustomerDrawerFragment =
            CustomerGoodsManagementFilterFragment.newInstance(mMerchantID).apply {
                setCustomerDrawerListener(this@GoodsManagementContentView)
            }
        return listOf(mCustomerDrawerFragment)
    }

    override fun onCustomerConfirmPressed(
        YBMFilterPos: Int,
        collectionStatusFilterPos: Int,
        selectedZone: Zone?
    ) {
        closeDrawer()
        onCustomerDrawerConfirmPressed(
            YBMFilterPos,
            collectionStatusFilterPos,
            selectedZone
        )
    }


    override fun initPresenter(): BasePresenter<*, *> {
        var isCustomerGoodsManagement = false
        mDividerLineMarginStart =
            if (mType == Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_RECOMMENDATION ||
                mType == Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_RECOMMENDATION_SEARCH ||
                mType == Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION_RECOMMENDATION ||
                mType == Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION_RECOMMENDATION_SEARCH
            ) {
                35f
            } else {
                15f
            }
        isCustomerGoodsManagement =
            mType == Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS ||
                    mType == Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_SEARCH ||
                    mType == Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_RECOMMENDATION ||
                    mType == Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_RECOMMENDATION_SEARCH
        return GoodsManagementPresenter(isCustomerGoodsManagement)
    }

    init {
        mPresenter = initPresenter() as GoodsManagementPresenter
        setBackgroundColor(Color.RED)
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        Log.e("guan2", "onAttachedToWindow")
        mPresenter.attachMV(this)
        mWaitProgressDialog = WaitProgressDialog(context)
        if (!isInit) {
            LayoutInflater.from(getActivity()).inflate(getLayoutId(), this, true)
            initUI()
            isInit = true
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        Log.e("guan2", "onDetachedFromWindow")
        mPresenter.detachMV()
        if (mWaitProgressDialog != null && mWaitProgressDialog!!.isShowing) {
            mWaitProgressDialog!!.dismiss()
        }
    }


    override fun onLoadMore(refreshLayout: RefreshLayout) {
        mPresenter.getGoodsList(false)
    }

    override fun onRefresh(refreshLayout: RefreshLayout) {
        refresh()
    }

    override fun onGetGoodsManagementGoodsListSuccess(
        data: RequestBaseBean<GoodsManagementListBean?>?,
        isRefresh: Boolean,
        isLastPage: Boolean
    ) {
        mIsRefresh = isRefresh

        if (mType == Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION || mType == Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION_SEARCH) {
            rtv_recommend.visibility = View.VISIBLE
        }

        data?.data?.rows?.let {

            if (mOrgCode.isEmpty()) {
                mOrgCode = it.getOrNull(0)?.orgCode ?: ""
            }

            if (mAreaCode.isEmpty()) {
                mAreaCode = it.getOrNull(0)?.branchCode ?: ""
            }

            if (isLastPage) {
                srl.finishLoadMoreWithNoMoreData()
            }
            updateListSelectedStatus(it)

            if (isRefresh) {
                mAdapter?.setNewData(it)
                if (it.isEmpty()) {
                    if (mType == Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION) {
                        rtv_recommend.visibility = View.GONE
                        svl.showEmpty("收藏全部商品中的商品，可进行批量推荐至客户，\n还可编辑自定义的卖点哦~")
                    } else {
                        svl.showEmpty()
                    }
                } else {
                    svl.showContent()
                }
            } else {
                mAdapter?.addData(it)
            }

            /**
             * TODO 逻辑傻逼待整合
             */
            if (it.isNotEmpty()) {
                if (mShouldFetchEstimatedPrices) {
                    val sbGoods = StringBuilder()
                    it.forEachIndexed { index, item ->
                        sbGoods.append("${item?.id ?: "-1"},")
                    }
                    mPresenter.getEstimatedPrices(sbGoods.toString(), mMerchantID)
                }
            }
        }
    }

    override fun onGetGoodsManagementGoodsListFail() {

    }

    private fun getActivity(): FragmentActivity? {
        var viewContext = context
        while (viewContext is ContextWrapper) {
            if (viewContext is FragmentActivity) {
                return viewContext
            }
            viewContext = viewContext.baseContext
        }
        return null
    }

    override fun onGetVarietyListSuccess(data: RequestBaseBean<VarietyListBean?>?) {
        if (data?.data?.rows.isNullOrEmpty()) {
            return
        }
        mAreaList = data?.data?.rows
        mShouldRequestVariety = false
        context?.let {
            mVarietyPopup = GoodsCategorySelectionPopup(context, data?.data?.rows)
            mVarietyPopup?.setOnPopItemClickListener(object :
                GoodsCategorySelectionPopup.OnPopItemClickListener1 {
                override fun onItemClick(
                    firstCode: String?,
                    secondCode: String?,
                    thirdCode: String?
                ) {
                    mPresenter.mQueryMap["categoryFirstId"] = firstCode ?: ""
                    mPresenter.mQueryMap["categorySecondId"] = secondCode ?: ""
                    mPresenter.mQueryMap["categoryThirdId"] = thirdCode ?: ""

                    tv_variety.setTextColor(
                        ContextCompat.getColor(
                            it,
                            R.color.color_292933
                        )
                    )

                    if (firstCode.isNullOrEmpty() && secondCode.isNullOrEmpty() && thirdCode.isNullOrEmpty()) {
                        tv_variety.setTextColor(
                            ContextCompat.getColor(
                                it,
                                R.color.color_676773
                            )
                        )
                    }

                    refresh()
                }
            })
            showPopup()
        }
    }

    override fun onGetVarietyListFail() {

    }

    override fun onCollectSuccess(data: RequestBaseBean<Any?>?, position: Int) {
        if (mType == Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION || mType == Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION_SEARCH) {
            mAdapter?.data?.removeAt(position)
            mAdapter?.notifyItemRemoved(position)
            if (mAdapter?.data?.isEmpty() == true) {
                svl.showEmpty("收藏全部商品中的商品，可进行批量推荐至客户，\n还可编辑自定义的卖点哦~")
                rtv_recommend.visibility = View.GONE
            }
        } else {
            mAdapter?.data?.getOrNull(position)?.run {
                collect = 1 - (collect ?: 0)
            }
            mAdapter?.notifyItemChanged(position)
        }
    }

    override fun onCollectFail() {

    }

    override fun onRequestShareConfirmSuccess(data: RequestBaseBean<ShareConfirm?>?) {
        if (shareType != null && getActivity() != null) {
            ShareHelper.shareUrl(getActivity()!!,
                shareType!!,
                data?.data?.shareUrl,
                data?.data?.appName,
                data?.data?.content,
                data?.data?.appLogo,
                object : ShareHelper.ShareCallback {
                    override fun onCancel(platform: String?) {
                    }

                    override fun onCompleted(platform: String?) {
                    }

                    override fun onError(platform: String?, errMsg: String?) {
                    }

                    override fun onStart(platform: String?) {
                    }

                })
        }
    }

    override fun onRequestShareConfirmFail() {

    }

    override fun onGetEstimatedPricesSuccess(data: RequestBaseBean<EstimatedPriceListBean?>?) {
        mEstimatedPriceList = data?.data?.rows
        updateListEstimatedPrices()
    }

    private fun updateListEstimatedPrices() {
        mEstimatedPriceList?.forEachIndexed { i, itemEstimatedPrice ->
            mAdapter?.data?.forEachIndexed { j, itemGood ->
                if (itemGood?.id == itemEstimatedPrice?.id) {
                    itemGood?.discountPrice = itemEstimatedPrice?.discountPrice ?: ""
                }
            }
        }

        mEstimatedPriceList = null

        mAdapter?.notifyDataSetChanged()
    }

    override fun onGetEstimatedPricesFail() {

    }

    override fun showNetError() {
        if (mIsRefresh) {
            svl.showError()
        }
    }

    private fun registerListener() {
        iv_back.setOnClickListener(this)
        rtv_search.setOnClickListener(this)
        tv_sales.setOnClickListener(this)
        tv_price.setOnClickListener(this)
        tv_variety.setOnClickListener(this)
        tv_on_sale.setOnClickListener(this)
        tv_filter.setOnClickListener(this)
        rtv_recommend.setOnClickListener(this)
        iv_customer_cart.setOnClickListener(this)
        rtv_confirm_recommendation.setOnClickListener(this)

        svl.setOnRetryListener {
            mPresenter.getGoodsList(true)
        }

    }

    fun isFromFlutter(): Boolean {
        return mType == Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS
    }


    /**
     * 发现-商品管理-区域筛选回掉
     */
    fun onDrawerConfirmPressed(itemArea: GoodsManagementAreaListBean.Row?, selectedZone: Zone?) {
        // 专区参数
        if (selectedZone != null && selectedZone.zoneId != "0") {
            mPresenter.mQueryMap["zoneId"] = selectedZone.zoneId ?: ""
            mPresenter.mQueryMap["zoneName"] = selectedZone.zoneName ?: ""
        } else {
            mPresenter.mQueryMap.remove("zoneId")
            mPresenter.mQueryMap.remove("zoneName")
        }

        //其他参数
        mPresenter.mQueryMap["branchCode"] = itemArea?.ecCode ?: ""
        mPresenter.mQueryMap["categoryFirstId"] = ""
        mPresenter.mQueryMap["categorySecondId"] = ""
        mPresenter.mQueryMap["categoryThirdId"] = ""
        refresh()
        mAreaCode = itemArea?.ecCode ?: ""
        mOrgCode = itemArea?.orgCode ?: ""
        mShouldRequestVariety = true

        /**
         * 切换区域之后，清空购物车。
         */
        GoodsManagementSelectGoodsActivity.sGoodsCartList.clear()
        mCartAdapter?.notifyDataSetChanged()
        updateBottomCart()
    }

    /**
     * 客户-商品管理-筛选回掉
     */
    fun onCustomerDrawerConfirmPressed(
        YBMFilterPos: Int,
        collectionStatusFilterPos: Int,
        selectedZone: Zone?
    ) {
        if (YBMFilterPos == -1) {
            mPresenter.mQueryMap["hasStock"] = ""
            mPresenter.mQueryMap["isPromotion"] = ""
            tv_on_sale.setTextColor(ContextCompat.getColor(context, R.color.color_676773))
            isOnSale = false
        } else {
            if (YBMFilterPos == 1) {
                isOnSale = true
                tv_on_sale.setTextColor(ContextCompat.getColor(context, R.color.color_292933))

                mPresenter.mQueryMap["isPromotion"] = "1"
                mPresenter.mQueryMap["hasStock"] = ""
            } else {
                isOnSale = false
                tv_on_sale.setTextColor(ContextCompat.getColor(context, R.color.color_676773))

                mPresenter.mQueryMap["isPromotion"] = ""
                mPresenter.mQueryMap["hasStock"] = "1"
            }
        }

        if (collectionStatusFilterPos == -1) {
            mPresenter.mQueryMap["collect"] = ""
        } else {
            mPresenter.mQueryMap["collect"] = "${(collectionStatusFilterPos == 1).toInt()}"
        }

        // 专区参数
        if (selectedZone != null && selectedZone.zoneId != "0") {
            mPresenter.mQueryMap["zoneId"] = selectedZone.zoneId ?: ""
            mPresenter.mQueryMap["zoneName"] = selectedZone.zoneName ?: ""
        } else {
            mPresenter.mQueryMap.remove("zoneId")
            mPresenter.mQueryMap.remove("zoneName")
        }


        refresh()
    }


    private fun initSmartRefreshLayout() {
        refreshLayout = srl
        //TODO 选择商品的时候可以禁止刷新
        srl.setEnableRefresh(true)
        srl.setEnableLoadMore(true)
        srl.setOnRefreshLoadMoreListener(this)
    }

    private fun initRecyclerView() {
        recycler_view.apply {
            layoutManager = LinearLayoutManager(context)
            addItemDecoration(
                HorizontalDividerItemDecoration.Builder(context)
                    .size(ConvertUtils.dp2px(1f))
                    .margin(ConvertUtils.dp2px(mDividerLineMarginStart), ConvertUtils.dp2px(0f))
                    .color(ContextCompat.getColor(context, R.color.text_color_F6F6F6))
                    .build()
            )
            mAdapter = GoodsManagementAdapter(mType)
            adapter = mAdapter
        }
        mAdapter?.setOnItemChildClickListener { adapter, view, position ->
            val item = mAdapter?.data?.getOrNull(position)
            if (getActivity() == null) {
                return@setOnItemChildClickListener
            }
            when (view?.id) {
                R.id.cl_content -> {
                    /**
                     * item 点击事件
                     */
                    when (mType) {
                        Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION -> {
                            GoodsDetailActivity.start(
                                getActivity(),
                                item?.id,
                                branchCode = item?.branchCode
                            )

                            UserBehaviorTrackingUtils.track("mc-myreserve-productdetail")
                        }
                        Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION_SEARCH -> {
                            GoodsDetailActivity.start(
                                getActivity(),
                                item?.id,
                                branchCode = item?.branchCode
                            )

                            UserBehaviorTrackingUtils.track("mc-myreserve-productdetail")
                        }
                        Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION_RECOMMENDATION,
                        Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION_RECOMMENDATION_SEARCH,
                        Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_RECOMMENDATION,
                        Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_RECOMMENDATION_SEARCH -> {
                            item?.isSelected = !(item?.isSelected
                                ?: false)
                            mAdapter?.notifyItemChanged(position)

                            if (item?.isSelected == true) {
                                GoodsManagementSelectGoodsActivity.sGoodsCartList.add(item)
                            } else {
                                repeat(GoodsManagementSelectGoodsActivity.sGoodsCartList.size) {
                                    if (GoodsManagementSelectGoodsActivity.sGoodsCartList.getOrNull(
                                            it
                                        )?.id == item?.id
                                    ) {
                                        GoodsManagementSelectGoodsActivity.sGoodsCartList.removeAt(
                                            it
                                        )
                                    }
                                }
                            }
                            mCartAdapter?.setNewData(GoodsManagementSelectGoodsActivity.sGoodsCartList)

                            updateBottomCart()
                        }
                        Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_ALL_GOODS -> {
                            GoodsDetailActivity.start(
                                getActivity(),
                                item?.id,
                                branchCode = item?.branchCode
                            )

                            UserBehaviorTrackingUtils.track("mc-allproduct-productdetail")
                        }
                        Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_ALL_GOODS_SEARCH -> {
                            GoodsDetailActivity.start(
                                getActivity(),
                                item?.id,
                                branchCode = item?.branchCode
                            )

                            UserBehaviorTrackingUtils.track("mc-allproduct-productdetail")
                        }
                        Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS -> {
                            //TODO merchantID 越界
                            GoodsDetailActivity.start(
                                getActivity(),
                                item?.id,
                                branchCode = item?.branchCode,
                                merchantId = mMerchantID.toLong()
                            )

                            UserBehaviorTrackingUtils.track("mc-productmgt-productdetail")
                        }
                        Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_SEARCH -> {
                            GoodsDetailActivity.start(
                                getActivity(),
                                item?.id,
                                branchCode = item?.branchCode,
                                merchantId = mMerchantID.toLong()
                            )

                            UserBehaviorTrackingUtils.track("mc-productmgt-productdetail")
                        }

                        Constants.GoodsManagement.CONSTANT_ADAPTER_CART -> {

                        }
                    }
                }
                R.id.tv_collect -> {
                    /**
                     * 收藏
                     */
                    mAdapter?.data?.getOrNull(position)?.run {
                        mPresenter.collect(
                            mapOf(
                                "productName" to "${showName ?: ""}",
                                "branchCode" to "${branchCode ?: ""}",
                                "skuId" to "${id ?: ""}",
                                "type" to "${1 - (collect ?: 0)}"
                            ), position
                        )
                    }
                    if (mType == Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_ALL_GOODS) {
                        (getActivity() as GoodsManagementActivity).refresh(0)
                    }

                    if (mAdapter?.data?.getOrNull(position)?.collect == 1) {
                        UserBehaviorTrackingUtils.track(mSnowGroundKeyUncollect)
                    } else {
                        UserBehaviorTrackingUtils.track(mSnowGroundKeyCollect)
                    }

                }
                R.id.tv_delete -> {
                    /**
                     * 测滑删除
                     */
                    mAdapter?.data?.getOrNull(position)?.run {
                        mPresenter.collect(
                            mapOf(
                                "productName" to "${showName ?: ""}",
                                "branchCode" to "${branchCode ?: ""}",
                                "skuId" to "${id ?: ""}",
                                "type" to "${1 - (collect ?: 1)}"
                            ), position
                        )
                    }
                    if (mType == Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION) {
                        (getActivity() as GoodsManagementActivity).refresh(1)
                    }

                    UserBehaviorTrackingUtils.track("mc-myreserve-unmark")
                }
            }
        }

    }

    private fun initViewAndQueryMap() {
        when (mType) {
            Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION -> {
                group_toolbar.visibility = View.GONE
                rtv_recommend.visibility = View.VISIBLE
                mPresenter.mQueryMap["sort"] = ""
                mPresenter.mQueryMap["type"] = "1"

                mSnowGroundKeySales = "mc-myreserve-sale"
                mSnowGroundKeyPrice = "mc-myreserve-price"
                mSnowGroundKeyCategory = "mc-myreserve-category"
                mSnowGroundKeyPromotion = "mc-myreserve-promotion"
                mSnowGroundKeyFilter = "mc-myreserve-filter"
            }
            Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION_SEARCH -> {
                group_toolbar.visibility = View.GONE
                rtv_recommend.visibility = View.VISIBLE
                mPresenter.mQueryMap["sort"] = ""
                mPresenter.mQueryMap["type"] = "1"
                mPresenter.mQueryMap["keyword"] = mSearchKeyword
                mPresenter.mQueryMap["branchCode"] = mAreaCode

                mSnowGroundKeySales = "mc-myreserve-sale"
                mSnowGroundKeyPrice = "mc-myreserve-price"
                mSnowGroundKeyCategory = "mc-myreserve-category"
                mSnowGroundKeyPromotion = "mc-myreserve-promotion"
                mSnowGroundKeyFilter = "mc-myreserve-filter"
            }
            Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION_RECOMMENDATION -> {
                group_toolbar.visibility = View.GONE
                rtv_recommend.visibility = View.GONE
                bottom_cart.visibility = View.VISIBLE
                mPresenter.mQueryMap["sort"] = ""
                mPresenter.mQueryMap["type"] = "1"
                mPresenter.mQueryMap["branchCode"] = mAreaCode

                updateBottomCart()
                mCartAdapter?.setNewData(GoodsManagementSelectGoodsActivity.sGoodsCartList)

                mSnowGroundKeySales = "mc-myreserve-sale"
                mSnowGroundKeyPrice = "mc-myreserve-price"
                mSnowGroundKeyCategory = "mc-myreserve-category"
                mSnowGroundKeyPromotion = "mc-myreserve-promotion"
                mSnowGroundKeyFilter = "mc-myreserve-filter"
                mSnowGroundKeyConfirmRecommendation = "mc-myreserve-recommendwithproduct"
                mSnowGroundKeyShareToYBM = "mc-myreserve-recommendtype1"
                mSnowGroundKeyShareToWeChat = "mc-myreserve-recommendtype2"
                mSnowGroundKeyShareToWeChatCircle = "mc-myreserve-recommendtype3"
            }
            Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION_RECOMMENDATION_SEARCH -> {
                group_toolbar.visibility = View.GONE
                rtv_recommend.visibility = View.GONE
                bottom_cart.visibility = View.VISIBLE

                mPresenter.mQueryMap["sort"] = ""
                mPresenter.mQueryMap["type"] = "1"
                mPresenter.mQueryMap["keyword"] = mSearchKeyword
                mPresenter.mQueryMap["branchCode"] = mAreaCode

                updateBottomCart()
                mCartAdapter?.setNewData(GoodsManagementSelectGoodsActivity.sGoodsCartList)

                mSnowGroundKeySales = "mc-myreserve-sale"
                mSnowGroundKeyPrice = "mc-myreserve-price"
                mSnowGroundKeyCategory = "mc-myreserve-category"
                mSnowGroundKeyPromotion = "mc-myreserve-promotion"
                mSnowGroundKeyFilter = "mc-myreserve-filter"
                mSnowGroundKeyConfirmRecommendation = "mc-myreserve-recommendwithproduct"
                mSnowGroundKeyShareToYBM = "mc-myreserve-recommendtype1"
                mSnowGroundKeyShareToWeChat = "mc-myreserve-recommendtype2"
                mSnowGroundKeyShareToWeChatCircle = "mc-myreserve-recommendtype3"
            }
            Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_ALL_GOODS -> {
                group_toolbar.visibility = View.GONE
                svl.layoutParams = (svl.layoutParams as ConstraintLayout.LayoutParams).apply {
                    setMargins(0, 0, 0, 0)
                }

                mSortTypeSales = GoodsManagementFragment.SortType.DESC
                setSortArrow(tv_sales, mSortTypeSales)
                mPresenter.mQueryMap["sort"] = "1"
                mPresenter.mQueryMap["type"] = "2"

                mSnowGroundKeySales = "mc-allproduct-sale"
                mSnowGroundKeyPrice = "mc-allproduct-price"
                mSnowGroundKeyCategory = "mc-allproduct-category"
                mSnowGroundKeyPromotion = "mc-allproduct-promotion"
                mSnowGroundKeyFilter = "mc-allproduct-filter"
                mSnowGroundKeyCollect = "mc-allproduct-mark"
                mSnowGroundKeyUncollect = "mc-allproduct-unmark"
            }
            Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_ALL_GOODS_SEARCH -> {
                group_toolbar.visibility = View.GONE
                svl.layoutParams = (svl.layoutParams as ConstraintLayout.LayoutParams).apply {
                    setMargins(0, 0, 0, 0)
                }

                mSortTypeSales = GoodsManagementFragment.SortType.DESC
                setSortArrow(tv_sales, mSortTypeSales)
                mPresenter.mQueryMap["sort"] = "1"
                mPresenter.mQueryMap["type"] = "2"
                mPresenter.mQueryMap["keyword"] = mSearchKeyword
                mPresenter.mQueryMap["branchCode"] = mAreaCode

                mSnowGroundKeySales = "mc-allproduct-sale"
                mSnowGroundKeyPrice = "mc-allproduct-price"
                mSnowGroundKeyCategory = "mc-allproduct-category"
                mSnowGroundKeyPromotion = "mc-allproduct-promotion"
                mSnowGroundKeyFilter = "mc-allproduct-filter"
                mSnowGroundKeyCollect = "mc-allproduct-mark"
                mSnowGroundKeyUncollect = "mc-allproduct-unmark"
            }
            Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS -> {
                group_toolbar.visibility = View.GONE
                rtv_recommend.visibility = View.VISIBLE

                mSortTypeSales = GoodsManagementFragment.SortType.DESC
                setSortArrow(tv_sales, mSortTypeSales)
                mPresenter.mQueryMap["sort"] = "1"
                mPresenter.mQueryMap["merchantId"] = mMerchantID

                mSnowGroundKeySales = "mc-productmgt-sale"
                mSnowGroundKeyPrice = "mc-productmgt-price"
                mSnowGroundKeyCategory = "mc-productmgt-category"
                mSnowGroundKeyPromotion = "mc-productmgt-promotion"
                mSnowGroundKeyFilter = "mc-productmgt-filter"
                mSnowGroundKeyCollect = "mc-productmgt-mark"
                mSnowGroundKeyUncollect = "mc-productmgt-unmark"

                mShouldFetchEstimatedPrices = true
            }
            Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_SEARCH -> {
                group_toolbar.visibility = View.GONE
                rtv_recommend.visibility = View.VISIBLE

                mSortTypeSales = GoodsManagementFragment.SortType.DESC
                setSortArrow(tv_sales, mSortTypeSales)
                mPresenter.mQueryMap["sort"] = "1"
                mPresenter.mQueryMap["merchantId"] = mMerchantID
                mPresenter.mQueryMap["keyword"] = mSearchKeyword

                mSnowGroundKeySales = "mc-productmgt-sale"
                mSnowGroundKeyPrice = "mc-productmgt-price"
                mSnowGroundKeyCategory = "mc-productmgt-category"
                mSnowGroundKeyPromotion = "mc-productmgt-promotion"
                mSnowGroundKeyFilter = "mc-productmgt-filter"
                mSnowGroundKeyCollect = "mc-productmgt-mark"
                mSnowGroundKeyUncollect = "mc-productmgt-unmark"

                mShouldFetchEstimatedPrices = true
            }
            Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_RECOMMENDATION -> {
                group_toolbar.visibility = View.GONE
                rtv_recommend.visibility = View.GONE
                bottom_cart.visibility = View.VISIBLE

                mSortTypeSales = GoodsManagementFragment.SortType.DESC
                setSortArrow(tv_sales, mSortTypeSales)
                mPresenter.mQueryMap["sort"] = "1"
                mPresenter.mQueryMap["merchantId"] = mMerchantID

                updateBottomCart()
                mCartAdapter?.setNewData(GoodsManagementSelectGoodsActivity.sGoodsCartList)

                mSnowGroundKeySales = "mc-productmgt-sale"
                mSnowGroundKeyPrice = "mc-productmgt-price"
                mSnowGroundKeyCategory = "mc-productmgt-category"
                mSnowGroundKeyPromotion = "mc-productmgt-promotion"
                mSnowGroundKeyFilter = "mc-productmgt-filter"
                mSnowGroundKeyConfirmRecommendation = "mc-productmgt-recommendwithproduct"
                mSnowGroundKeyShareToYBM = "mc-productmgt-recommendtype1"
                mSnowGroundKeyShareToWeChat = "mc-productmgt-recommendtype2"
                mSnowGroundKeyShareToWeChatCircle = "mc-productmgt-recommendtype3"
            }
            Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_RECOMMENDATION_SEARCH -> {
                group_toolbar.visibility = View.GONE
                rtv_recommend.visibility = View.GONE
                bottom_cart.visibility = View.VISIBLE

                mSortTypeSales = GoodsManagementFragment.SortType.DESC
                setSortArrow(tv_sales, mSortTypeSales)
                mPresenter.mQueryMap["sort"] = "1"
                mPresenter.mQueryMap["merchantId"] = mMerchantID
                mPresenter.mQueryMap["keyword"] = mSearchKeyword

                updateBottomCart()
                mCartAdapter?.setNewData(GoodsManagementSelectGoodsActivity.sGoodsCartList)

                mSnowGroundKeySales = "mc-productmgt-sale"
                mSnowGroundKeyPrice = "mc-productmgt-price"
                mSnowGroundKeyCategory = "mc-productmgt-category"
                mSnowGroundKeyPromotion = "mc-productmgt-promotion"
                mSnowGroundKeyFilter = "mc-productmgt-filter"
                mSnowGroundKeyConfirmRecommendation = "mc-productmgt-recommendwithproduct"
                mSnowGroundKeyShareToYBM = "mc-productmgt-recommendtype1"
                mSnowGroundKeyShareToWeChat = "mc-productmgt-recommendtype2"
                mSnowGroundKeyShareToWeChatCircle = "mc-productmgt-recommendtype3"
            }

        }
    }

    fun refresh() {
        try {
            srl?.setEnableLoadMore(true)
            srl?.setNoMoreData(false)
            recycler_view?.smoothScrollToPosition(0)
            mPresenter.getGoodsList(true)
        } catch (e: Exception) {
        }
    }

    private fun setSortArrow(targetView: TextView, sortType: GoodsManagementFragment.SortType) {
        targetView.apply {
            when (sortType) {
                GoodsManagementFragment.SortType.NORMAL -> {
                    setTextColor(ContextCompat.getColor(context, R.color.color_676773))
                    setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_sort_normal, 0)
                }
                GoodsManagementFragment.SortType.ASC -> {
                    setTextColor(ContextCompat.getColor(context, R.color.color_292933))
                    setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_sort_asc, 0)
                }
                GoodsManagementFragment.SortType.DESC -> {
                    setTextColor(ContextCompat.getColor(context, R.color.color_292933))
                    setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_sort_desc, 0)
                }
            }
        }
    }

    private fun initCartDialog() {
        mCartDialog = CustomBottomSheetDialog(
            context,
            ConvertUtils.dp2px(GoodsManagementFragment.INITIAL_HEIGHT + GoodsManagementFragment.ITEM_HEIGHT),
            ConvertUtils.dp2px(GoodsManagementFragment.INITIAL_HEIGHT + GoodsManagementFragment.ITEM_HEIGHT)
        )

        mCartLayout = LayoutInflater.from(context).inflate(R.layout.dialog_cart, null)

        mCartDialog?.setContentView(mCartLayout!!)

        mCartLayout?.findViewById<ImageView>(R.id.iv_customer_cart)
            ?.setImageDrawable(ContextCompat.getDrawable(context, R.drawable.ic_goods_cart))
        mCartLayout?.findViewById<TextView>(R.id.tv_clear)
            ?.setOnClickListener(this)
        mCartLayout?.findViewById<RoundTextView>(R.id.rtv_confirm_recommendation)
            ?.setOnClickListener(this)

        val recyclerView = mCartLayout?.findViewById<RecyclerView>(R.id.recycler_view)
        recyclerView?.apply {
            layoutManager = LinearLayoutManager(context)
            mCartAdapter = GoodsManagementAdapter(Constants.GoodsManagement.CONSTANT_ADAPTER_CART)
            adapter = mCartAdapter
        }

        mCartAdapter?.setOnItemChildClickListener { adapter, view, position ->
            when (view?.id) {
                R.id.tv_delete -> {
                    mAdapter?.data?.forEachIndexed { index, item ->
                        if (item?.id == GoodsManagementSelectGoodsActivity.sGoodsCartList[position].id) {
                            mAdapter?.data?.getOrNull(index)?.isSelected = false
                            mAdapter?.notifyDataSetChanged()
                        }
                    }

                    GoodsManagementSelectGoodsActivity.sGoodsCartList.removeAt(position)
                    mCartAdapter?.notifyItemRemoved(position)

                    updateCartHeight()

                    updateBottomCart()

                    if (GoodsManagementSelectGoodsActivity.sGoodsCartList.size < 1) {
                        mCartDialog?.hide()
                    }
                }
            }
        }
    }

    private fun updateCartHeight() {
        if (GoodsManagementSelectGoodsActivity.sGoodsCartList.size > 0) {

            var height =
                GoodsManagementFragment.INITIAL_HEIGHT + GoodsManagementFragment.ITEM_HEIGHT * GoodsManagementSelectGoodsActivity.sGoodsCartList.size
            if (height > mMaxCartHeight) height = mMaxCartHeight.toFloat()

            mCartDialog?.setPeekHeight(ConvertUtils.dp2px(height))
            mCartDialog?.setMaxHeight(ConvertUtils.dp2px(height))
        }
    }

    private fun updateBottomCart() {
        val tvTitle = mCartLayout?.findViewById<TextView>(R.id.tv_title)
        val tvDescription = mCartLayout?.findViewById<TextView>(R.id.tv_description)
        val rtvConfirmRecommendation =
            mCartLayout?.findViewById<RoundTextView>(R.id.rtv_confirm_recommendation)
        val badgeView = mCartLayout?.findViewById<BadgeView>(R.id.bv_count)

        tvTitle?.text = "已选${GoodsManagementSelectGoodsActivity.sGoodsCartList.size}个商品"

        tv_description?.text = "已选${GoodsManagementSelectGoodsActivity.sGoodsCartList.size}个商品"
        tvDescription?.text = "已选${GoodsManagementSelectGoodsActivity.sGoodsCartList.size}个商品"

        bv_count?.text = "${GoodsManagementSelectGoodsActivity.sGoodsCartList.size}"
        badgeView?.text = "${GoodsManagementSelectGoodsActivity.sGoodsCartList.size}"

        if (GoodsManagementSelectGoodsActivity.sGoodsCartList.size > 0) {
            rtv_confirm_recommendation?.visibility = View.VISIBLE
            rtvConfirmRecommendation?.visibility = View.VISIBLE
        } else {
            rtv_confirm_recommendation?.visibility = View.GONE
            rtvConfirmRecommendation?.visibility = View.GONE
        }
    }

    private fun updateListSelectedStatus(
        recyclerViewDataList: List<GoodsManagementListBean.Row?>?,
        shouldNotifyDataSetChange: Boolean = false
    ) {
        if (recyclerViewDataList == null) {
            return
        }

        if (shouldNotifyDataSetChange) {
            recyclerViewDataList.forEachIndexed { index, item ->
                item?.isSelected = false
            }
        }

        GoodsManagementSelectGoodsActivity.sGoodsCartList.forEachIndexed { index, itemCart ->
            recyclerViewDataList.forEachIndexed { index, item ->
                if (itemCart.id == item?.id) {
                    item?.isSelected = true
                }
            }
        }
        if (shouldNotifyDataSetChange) {
            mAdapter?.notifyDataSetChanged()
            updateBottomCart()
        }
    }

    fun search(keyword: String) {
        mPresenter.mQueryMap["keyword"] = keyword
        refresh()
    }

    private fun getGoodsStr(): String {
        val sbGoods = StringBuilder()
        GoodsManagementSelectGoodsActivity.sGoodsCartList.forEachIndexed { index, item ->
            sbGoods.append("${item.id},")
        }
        return sbGoods.toString()
    }

    fun showPopup() {
        val rect = Rect()
        tv_variety.getGlobalVisibleRect(rect)
        Log.e("guan", "getGlobalVisibleRect:${rect}")
        mVarietyPopup?.showPopupWindow(
            0,
            ConvertUtils.dp2px(rect.bottom.toFloat()) + tv_variety.height
        )
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.rtv_search -> {
                /**
                 * 点击跳转搜索页面
                 */
                when (mType) {
                    Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION_RECOMMENDATION -> {
                        BaseSearchActivity.startActivity(
                            getActivity(),
                            Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION_RECOMMENDATION_SEARCH,
                            areaCode = mAreaCode
                        )
                    }
                    Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_RECOMMENDATION -> {
                        BaseSearchActivity.startActivity(
                            getActivity(),
                            Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_RECOMMENDATION_SEARCH,
                            mMerchantID
                        )
                    }
                }
            }
            R.id.tv_sales -> {
                /**
                 * 销量排序
                 */
                setSortArrow(tv_price, GoodsManagementFragment.SortType.NORMAL)

                mSortTypeSales = when (mSortTypeSales) {
                    GoodsManagementFragment.SortType.NORMAL -> {
                        mPresenter.mQueryMap["sort"] = "1"
                        GoodsManagementFragment.SortType.DESC
                    }
                    GoodsManagementFragment.SortType.ASC -> {
                        mPresenter.mQueryMap["sort"] = "1"
                        GoodsManagementFragment.SortType.DESC
                    }
                    GoodsManagementFragment.SortType.DESC -> {
                        mPresenter.mQueryMap["sort"] = "2"
                        GoodsManagementFragment.SortType.ASC
                    }
                }

                setSortArrow(tv_sales, mSortTypeSales)

                refresh()

                UserBehaviorTrackingUtils.track(mSnowGroundKeySales)
            }
            R.id.tv_price -> {
                /**
                 * 价格排序
                 */
                setSortArrow(tv_sales, GoodsManagementFragment.SortType.NORMAL)

                mSortTypePrice = when (mSortTypePrice) {
                    GoodsManagementFragment.SortType.NORMAL -> {
                        mPresenter.mQueryMap["sort"] = "3"
                        GoodsManagementFragment.SortType.DESC
                    }
                    GoodsManagementFragment.SortType.ASC -> {
                        mPresenter.mQueryMap["sort"] = "3"
                        GoodsManagementFragment.SortType.DESC
                    }
                    GoodsManagementFragment.SortType.DESC -> {
                        mPresenter.mQueryMap["sort"] = "4"
                        GoodsManagementFragment.SortType.ASC
                    }
                }

                setSortArrow(tv_price, mSortTypePrice)

                refresh()

                UserBehaviorTrackingUtils.track(mSnowGroundKeyPrice)
            }
            R.id.tv_variety -> {
                /**
                 * 品类
                 */
                if (mShouldRequestVariety) {
                    mPresenter.getVarietyList(mAreaCode)
                } else {
                    showPopup()
                }

                UserBehaviorTrackingUtils.track(mSnowGroundKeyCategory)
            }
            R.id.tv_on_sale -> {
                /**
                 * 有促销
                 */
                isOnSale = !isOnSale
                if (isOnSale) {
                    tv_on_sale.setTextColor(ContextCompat.getColor(context, R.color.color_292933))
                    mPresenter.mQueryMap["isPromotion"] = "${isOnSale.toInt()}"
                    mPresenter.mQueryMap.remove("hasStock")
                } else {
                    tv_on_sale.setTextColor(ContextCompat.getColor(context, R.color.color_676773))
                    mPresenter.mQueryMap.remove("isPromotion")
                }

                when (mType) {
                    Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS -> {
                        // TODO guanchong
                        syncCustomerDrawerFilterData(
                            isOnSale.toInt()
                        )
                    }
                    Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_SEARCH, Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_RECOMMENDATION_SEARCH -> {
                        (getActivity() as BaseSearchResultActivity).syncCustomerDrawerFilterData(
                            isOnSale.toInt()
                        )
                    }
                    Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_RECOMMENDATION -> {
                        (getActivity() as GoodsManagementSelectGoodsActivity).syncCustomerDrawerFilterData(
                            isOnSale.toInt()
                        )
                    }
                }

                refresh()

                UserBehaviorTrackingUtils.track(mSnowGroundKeyPromotion)
            }
            R.id.tv_filter -> {
                /**
                 * 筛选
                 */
                // TODO guanchong
                openDrawer(0)

                UserBehaviorTrackingUtils.track(mSnowGroundKeyFilter)
            }
            R.id.rtv_recommend -> {
                /**
                 * 推荐
                 */
                when (mType) {
                    Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION -> {
                        GoodsManagementSelectGoodsActivity.startActivity(
                            getActivity(),
                            Bundle().apply {
                                putInt(
                                    Constants.GoodsManagement.ARG_FRAGMENT_TYPE,
                                    Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION_RECOMMENDATION
                                )
                                putString(
                                    Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_AREA_CODE,
                                    mAreaCode
                                )
                                putString(
                                    Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_MERCHANT_ID,
                                    mMerchantID
                                )
                            })

                        UserBehaviorTrackingUtils.track("mc-productmgt-recommend")
                    }
                    Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION_SEARCH -> {
                        GoodsManagementSelectGoodsActivity.startActivity(
                            getActivity(),
                            Bundle().apply {
                                putInt(
                                    Constants.GoodsManagement.ARG_FRAGMENT_TYPE,
                                    Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION_RECOMMENDATION
                                )
                                putString(
                                    Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_AREA_CODE,
                                    mAreaCode
                                )
                                putString(
                                    Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_MERCHANT_ID,
                                    mMerchantID
                                )
                            })

                        UserBehaviorTrackingUtils.track("mc-productmgt-recommend")
                    }
                    Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS -> {
                        GoodsManagementSelectGoodsActivity.startActivity(
                            getActivity(),
                            Bundle().apply {
                                putInt(
                                    Constants.GoodsManagement.ARG_FRAGMENT_TYPE,
                                    Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_RECOMMENDATION
                                )
                                putString(
                                    Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_MERCHANT_ID,
                                    mMerchantID
                                )
                            })

                        UserBehaviorTrackingUtils.track("mc-myreserve-recommend")
                    }
                    Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_SEARCH -> {
                        GoodsManagementSelectGoodsActivity.startActivity(
                            getActivity(),
                            Bundle().apply {
                                putInt(
                                    Constants.GoodsManagement.ARG_FRAGMENT_TYPE,
                                    Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_RECOMMENDATION
                                )
                                putString(
                                    Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_MERCHANT_ID,
                                    mMerchantID
                                )
                            })

                        UserBehaviorTrackingUtils.track("mc-myreserve-recommend")
                    }
                }
            }
            R.id.iv_customer_cart -> {
                updateCartHeight()
                if (GoodsManagementSelectGoodsActivity.sGoodsCartList.size > 0) {
                    mCartDialog?.show()
                } else {

                }
            }
            R.id.tv_clear -> {
                GoodsManagementSelectGoodsActivity.sGoodsCartList.clear()
                mCartAdapter?.setNewData(GoodsManagementSelectGoodsActivity.sGoodsCartList)
                mAdapter?.data?.forEachIndexed { index, item ->
                    if (item?.isSelected == true) {
                        item.isSelected = false
                    }
                }
                mAdapter?.notifyDataSetChanged()
                updateBottomCart()
                mCartDialog?.hide()
            }
            R.id.rtv_confirm_recommendation -> {

                UserBehaviorTrackingUtils.track(mSnowGroundKeyConfirmRecommendation)

                val dialog = ShareGoodsDialog(context).also { shareDialog ->
                    shareDialog.onClickCallBack = {
                        shareType = null
                        when (it) {
                            1 -> {
                                shareType = ShareHelper.Companion.SocialMedia.PLATFORM_WECHAT
                                mPresenter.requestShareConfirm(getGoodsStr())

                                UserBehaviorTrackingUtils.track(mSnowGroundKeyShareToWeChat)
                            }
                            2 -> {
                                shareType = ShareHelper.Companion.SocialMedia.PLATFORM_WECHAT_CIRCLE
                                mPresenter.requestShareConfirm(getGoodsStr())

                                UserBehaviorTrackingUtils.track(mSnowGroundKeyShareToWeChatCircle)
                            }
                            3 -> {
                                SelectCustomersActivity.startActivity(
                                    getActivity(),
                                    "",
                                    getGoodsStr(),
                                    mType,
                                    branchCode = mAreaCode
                                )

                                UserBehaviorTrackingUtils.track(mSnowGroundKeyShareToYBM)
                            }
                        }
                    }
                }
                dialog.show()
            }
        }
    }

    override fun showWaitDialog(msg: String?) {
        var msg = msg
        if (TextUtils.isEmpty(msg) && container != null) {
            msg = context.getResources().getString(R.string.loading)
        }
        showProgressDialog(msg)
    }

    protected var mWaitProgressDialog: WaitProgressDialog? = null

    /**
     * 显示提示框
     *
     * @param msg 提示框内容字符串
     */
    protected fun showProgressDialog(msg: String?) {
        try {
            if (mWaitProgressDialog?.isShowing() == true) {
                mWaitProgressDialog?.dismiss()
            }
            mWaitProgressDialog?.setMessage(msg)
            mWaitProgressDialog?.show()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 隐藏提示框
     */
    protected fun hideProgressDialog() {
        if (mWaitProgressDialog != null && mWaitProgressDialog?.isShowing() == true) {
            //修改bugly上的bug---（未验证try{}catch{}是否有效）
            try {
                mWaitProgressDialog?.dismiss()
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
            }
        }
    }

    override fun hideWaitDialog() {
        Handler().postDelayed({ hideProgressDialog() }, 200)
        refreshLayout?.finishRefresh(true)
        refreshLayout?.finishLoadMore(true)
    }

    override fun showToast(msg: String?) {
        ToastUtils.showShortSafe(msg)
    }

    override fun back() {
    }

    fun startNewFragment(supportFragment: SupportFragment) {
    }

    fun startNewFragmentWithPop(supportFragment: SupportFragment) {
    }

    fun startNewFragmentForResult(supportFragment: SupportFragment, requestCode: Int) {
    }

    fun popToFragment(targetFragmentClass: Class<*>?, includeTargetFragment: Boolean) {
    }

    override fun hideKeyboard() {
    }

    fun setOnFragmentResult(ResultCode: Int, data: Bundle?) {

    }

    override fun startNewActivity(clz: Class<*>) {
        (getActivity() as BaseCompatActivity).startActivity(clz)
    }

    override fun startNewActivity(clz: Class<*>, bundle: Bundle?) {
        (getActivity() as BaseCompatActivity).startActivity(clz, bundle)
    }

    override fun startNewActivityForResult(clz: Class<*>, bundle: Bundle?, requestCode: Int) {
        (getActivity() as BaseCompatActivity).startActivityForResult(clz, bundle, requestCode)
    }

    fun isVisiable(): Boolean {
        return true
    }

    fun getBindActivity(): Activity? {
        return getActivity()
    }
}