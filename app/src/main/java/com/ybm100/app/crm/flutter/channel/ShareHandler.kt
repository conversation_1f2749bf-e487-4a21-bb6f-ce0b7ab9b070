package com.ybm100.app.crm.flutter.channel

import android.graphics.BitmapFactory
import android.text.TextUtils
import androidx.fragment.app.FragmentActivity
import com.xyy.flutter.container.container.bridge.BaseHandler
import com.ybm100.app.crm.utils.ShareHelper

/**
 * 分享
 */
class ShareHandler: BaseHandler() {
    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {
        //分享的平台
        val platformType = when(params["sharePlatform"]) {
            1 -> ShareHelper.Companion.SocialMedia.PLATFORM_WECHAT
            2 -> ShareHelper.Companion.SocialMedia.PLATFORM_WECHAT_CIRCLE
            3 -> ShareHelper.Companion.SocialMedia.PLATFORM_WX_WORK
            else -> null
        }
        //分享的类型，目前只支持图片
        val shareType = params["shareType"]
        val imagePath = params["imgPath"] as String?

        if (platformType == null) {
            error("100", "当前不支持分享到该平台")
            return
        }
        if (shareType == null) {
            error("101", "参数错误")
            return
        }
        if (shareType == "1" && TextUtils.isEmpty(imagePath)) {
            error("102", "分享图片路径错误")
            return
        }
        val bitmap = BitmapFactory.decodeFile(imagePath)
        if (bitmap == null) {
            error("103", "分享图片错误")
            return
        }
        ShareHelper.shareImage(activity, platformType, bitmap, bitmap, null)
        success("")
    }
}