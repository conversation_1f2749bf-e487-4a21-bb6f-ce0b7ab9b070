package com.ybm100.app.crm.license.adapter

import androidx.core.content.ContextCompat
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.ybm100.app.crm.R
import com.ybm100.app.crm.bean.license.DeliveryAddressBean

/**
 * @author: zcj
 * @time:2020/7/1.
 * Description:
 */
class SelectAreaListAdapter : BaseQuickAdapter<DeliveryAddressBean, BaseViewHolder>(R.layout.item_select_area_list) {
    private var mActionListener: ActionListener? = null

    interface ActionListener {
        fun onItemClick(item: DeliveryAddressBean)
    }

    fun setActionListener(actionListener: ActionListener?) {
        mActionListener = actionListener
    }

    override fun convert(helper: BaseViewHolder?, item: DeliveryAddressBean) {
        helper?.setText(R.id.tv_name, item.areaName)
        if (!item.selected) {
            helper?.setTextColor(R.id.tv_name, ContextCompat.getColor(mContext, R.color.color_292933))
            helper?.setGone(R.id.iv_go, false)
        } else {
            helper?.setTextColor(R.id.tv_name, ContextCompat.getColor(mContext, R.color.color_00B377))
            helper?.setGone(R.id.iv_go, true)
        }
        helper?.itemView?.setOnClickListener {
            check(item)
            mActionListener?.onItemClick(item)
        }
    }

    private fun check(item: DeliveryAddressBean) {
        data.forEach {
            it.selected = it.id == item.id
        }
        notifyDataSetChanged()
    }
}