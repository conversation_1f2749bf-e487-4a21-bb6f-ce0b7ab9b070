package com.ybm100.app.crm.presenter.lbs;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.contract.lbs.LBSSelectContract;
import com.ybm100.app.crm.model.lbs.LBSSelectModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

import java.util.HashMap;

/**
 * Created by XyyMvpSportTemplate on 12/22/2018 16:53
 */
public class LBSSelectPresenter extends BasePresenter<LBSSelectContract.ILBSSelectModel, LBSSelectContract.ILBSSelectView> {

    public static LBSSelectPresenter newInstance() {
        return new LBSSelectPresenter();
    }

    @Override
    protected LBSSelectModel getModel() {
        return LBSSelectModel.newInstance();
    }

    public void addMerchantMap(HashMap<String, String> map) {
        if (mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.addMerchantMap(map).subscribe(new SimpleSuccessConsumer<RequestBaseBean>(mIView) {
            @Override
            public void onSuccess(RequestBaseBean baseBean) {
                if (mIView == null) return;
                mIView.addMerchantMapSuccess(baseBean);
            }
        }, new SimpleErrorConsumer(mIView)));

    }

    public void openSeaEdit(HashMap<String, String> map) {
        if (mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.openSeaEdit(map).subscribe(new SimpleSuccessConsumer<RequestBaseBean>(mIView) {
            @Override
            public void onSuccess(RequestBaseBean baseBean) {
                if (mIView == null) return;
                mIView.openSeaEditSuccess(baseBean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }
}
