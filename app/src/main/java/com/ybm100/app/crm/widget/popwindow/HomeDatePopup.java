package com.ybm100.app.crm.widget.popwindow;

import android.content.Context;
import android.graphics.Color;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.Gravity;
import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.xyy.common.widget.DefaultItemDecoration;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.home.PopDateBean;
import com.ybm100.app.crm.ui.adapter.popwindow.PopDateAdapter;

import java.util.List;

import razerdp.basepopup.BasePopupWindow;

/**
 * 首页日期切换pop
 */
public class HomeDatePopup extends BasePopupWindow {
    private List<PopDateBean> popList;
    private PopDateAdapter popDateAdapter;
    private int mBackgroundDrawable;

    public HomeDatePopup(Context context) {
        super(context);
        initRecyclerView();
        setAlignBackground(false);//背景是否对齐到PopupWindow
        setBackground(Color.TRANSPARENT);// 取消默认的背景颜色
        setOutSideDismiss(true);// 点击popupwindow背景部分不隐藏
    }

    public HomeDatePopup(Context context, List<PopDateBean> popList) {
        super(context);
        this.popList = popList;
        initRecyclerView();
        setAlignBackground(false);//背景是否对齐到PopupWindow
        setBackground(Color.TRANSPARENT);// 取消默认的背景颜色
        setOutSideDismiss(true);// 点击popupwindow背景部分不隐藏
        setPopupGravity(Gravity.BOTTOM | Gravity.START);
    }

    public void setPopList(List<PopDateBean> popList) {
        this.popList = popList;
        initRecyclerView();
    }

    public void setBackgroundDrawable(int backgroundDrawable) {
        this.mBackgroundDrawable = backgroundDrawable;
        initRecyclerView();
    }

    private void initRecyclerView() {
        RecyclerView rvPopDate = findViewById(R.id.rv_pop_date);
        if (mBackgroundDrawable != 0) {
            rvPopDate.setBackgroundResource(mBackgroundDrawable);
        }
        rvPopDate.setLayoutManager(new LinearLayoutManager(getContext()));
        rvPopDate.addItemDecoration(new DefaultItemDecoration(getContext()));
        popDateAdapter = new PopDateAdapter(R.layout.item_pop_date, popList);
        popDateAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                popDateAdapter.selectItem(position);
                clickListener.onItemClick(position);
                dismiss();
            }
        });
        rvPopDate.setAdapter(popDateAdapter);
    }

    public PopDateBean getItem(int position) {
        return popList.get(position);
    }

    @Override
    public View onCreateContentView() {
        return createPopupById(R.layout.list_popup_date);
    }

    @Override
    public void showPopupWindow(View v) {
        super.showPopupWindow(v);
    }

    private OnPopItemClickListener clickListener;

    public void setOnPopItemClickListener(OnPopItemClickListener clickListener) {
        this.clickListener = clickListener;
    }

    public interface OnPopItemClickListener {
        void onItemClick(int position);
    }

    /**
     * 设置新的数据源
     *
     * @param popList
     */
    public void setNewData(List<PopDateBean> popList) {
        this.popList.clear();
        this.popList.addAll(popList);
        if (popDateAdapter != null) {
            popDateAdapter.setNewData(this.popList);
        }
    }
}
