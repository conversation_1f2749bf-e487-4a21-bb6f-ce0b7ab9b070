package com.ybm100.app.crm.presenter.drugstore.minedrug;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.AptitudesBean;
import com.ybm100.app.crm.contract.drugstore.minedrug.DrugstoreAptitudesContract;
import com.ybm100.app.crm.model.drugstore.minedrug.DrugstoreAptitudesModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;
import com.ybm100.app.crm.order.bean.AptitudeInitBean;
import com.ybm100.app.crm.order.bean.InvoiceHasBean;

import java.util.HashMap;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:08
 */
public class DrugstoreAptitudesPresenter extends BasePresenter<DrugstoreAptitudesContract.IDrugstoreAptitudesModel, DrugstoreAptitudesContract.IDrugstoreAptitudesView> {

    public static DrugstoreAptitudesPresenter newInstance() {
        return new DrugstoreAptitudesPresenter();
    }

    @Override
    protected DrugstoreAptitudesModel getModel() {
        return DrugstoreAptitudesModel.newInstance();
    }

    /**
     * 资质详情列表
     *
     * @param merchantId
     */
    public void getAptitudeDetailList(String merchantId) {

        if (mIView == null || mIModel == null) return;

        mRxManager.register(mIModel.getAptitudeDetailList(merchantId).subscribe(new SimpleSuccessConsumer<RequestBaseBean<AptitudesBean>>(mIView) {
            @Override
            public void onSuccess(RequestBaseBean<AptitudesBean> requestBaseBean) {

                mIView.getAptitudeDetailListSuccess(requestBaseBean);
            }

            @Override
            public void onFailure(int errorCode) {
                super.onFailure(errorCode);
                mIView.showNetError();
            }
        }, new SimpleErrorConsumer(mIView) {
            @Override
            protected void onError(Throwable throwable, String msg) {
                super.onError(throwable, msg);
                if (mIView == null) return;
                mIView.showNetError();
            }
        }));
    }

    /**
     * 验证药店是否存在发票类型
     *
     * @param merchantId
     */
    public void isHaveType(String merchantId) {
        if (mIView == null || mIModel == null) {
            return;
        }
        mRxManager.register(mIModel.isHaveType(merchantId).subscribe(new SimpleSuccessConsumer<RequestBaseBean<InvoiceHasBean>>(mIView,"") {
            @Override
            public void onSuccess(RequestBaseBean<InvoiceHasBean> integerRequestBaseBean) {
                if (mIView != null) {
                    mIView.isHaveTypeSuccess(integerRequestBaseBean);
                }
            }

        }, new SimpleErrorConsumer(mIView) {
        }));
    }

    public void initLicenseAuditDetail(String merchantId) {
        if (mIView == null || mIModel == null) return;
        HashMap<String, String> map = new HashMap<>();
        map.put("merchantId", merchantId);
        mRxManager.register(mIModel.initLicenseAuditDetail(map).subscribe(new SimpleSuccessConsumer<RequestBaseBean<AptitudeInitBean>>(mIView,"正在加载中...") {
            @Override
            public void onSuccess(RequestBaseBean<AptitudeInitBean> requestBaseBean) {
                mIView.initLicenseAuditDetailSuccess(requestBaseBean);
            }
        }, new SimpleErrorConsumer(mIView) {
        }));
    }

}
