package com.ybm100.app.crm.contract.login;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.ybm100.app.crm.bean.CaptchaBean;
import com.ybm100.app.crm.bean.user.UserInfoBean;

import io.reactivex.Observable;

public interface LoginContract {

    interface ILoginModel extends IBaseModel {

        /**
         * 请求登录接口
         *
         * @param mobile
         * @param pwd
         * @return
         */
        Observable<UserInfoBean> loginRequest(String mobile, String pwd, String deviceId, String vcode,String captchaId);

        /**
         * 获取图形验证码
         * @return
         */
        Observable<CaptchaBean> getVerifyCode();
    }

    interface ILoginView extends IBaseActivity {

        /**
         * 登录成功
         */
        void loginSuccess(UserInfoBean userInfoBean);

        void loginFailure(String errorMsg);

        void getVerifyCodeSuccess(CaptchaBean captchaBean);
        void getVerifyCodeFailure();
    }

}
