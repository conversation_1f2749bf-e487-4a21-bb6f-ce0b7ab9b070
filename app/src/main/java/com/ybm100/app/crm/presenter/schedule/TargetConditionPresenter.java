package com.ybm100.app.crm.presenter.schedule;

import android.content.Context;

import com.xyy.utilslibrary.base.BasePresenter;
import com.ybm100.app.crm.contract.schedule.TargetConditionContract;
import com.ybm100.app.crm.model.schedule.TargetConditionModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;

/**
 * Created by XyyMvpSportTemplate on 06/21/2019 15:00
 */
public class TargetConditionPresenter extends BasePresenter<TargetConditionContract.ITargetConditionModel, TargetConditionContract.ITargetConditionView> {

    public static TargetConditionPresenter newInstance() {
        return new TargetConditionPresenter();
    }

    @Override
    protected TargetConditionModel getModel() {
        return TargetConditionModel.newInstance();
    }

    public void merchantEnums(Context context) {
        if (mIModel == null || mIView == null) return;
        mRxManager.register(mIModel.merchantEnums(context)
                .subscribe(merchantEnumsBean -> {
                    if (mIView == null) return;
                    mIView.merchantEnumsSuccess(merchantEnumsBean);
                }, new SimpleErrorConsumer(mIView)));
    }
}
