package com.ybm100.app.crm.goodsmanagement.presenter

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.ybm100.app.crm.goodsmanagement.bean.AvailableZone
import com.ybm100.app.crm.goodsmanagement.bean.GoodsManagementAreaListBean
import com.ybm100.app.crm.goodsmanagement.contract.GoodsManagementAreaContract
import com.ybm100.app.crm.goodsmanagement.model.GoodsManagementAreaModel
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer

class GoodsManagementAreaPresenter : BasePresenter<GoodsManagementAreaContract.IGoodsManagementAreaModel, GoodsManagementAreaContract.IGoodsManagementAreaView>() {


    override fun getModel(): GoodsManagementAreaContract.IGoodsManagementAreaModel {
        return GoodsManagementAreaModel()
    }

    fun getAreaList() {
        mRxManager.register(mIModel.getGoodsManagementAreaList()
                .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<GoodsManagementAreaListBean?>?>(mIView, "") {
                    override fun onSuccess(requestBaseBean: RequestBaseBean<GoodsManagementAreaListBean?>?) {
                        mIView.onGetGoodsManagementAreaListSuccess(requestBaseBean)
                    }

                }, SimpleErrorConsumer(mIView)))
    }


    fun getAvailableZone(params: HashMap<String, String>) {
        mRxManager.register(mIModel.getAvailableZone(params).subscribe(object : SimpleSuccessConsumer<RequestBaseBean<AvailableZone?>?>(mIView,"加载中...") {
            override fun onSuccess(t: RequestBaseBean<AvailableZone?>?) {
                mIView.onGetAvailableZoneSuccess(t)
            }

            override fun onFailure(errorCode: Int) {
            }
        }, object : SimpleErrorConsumer(mIView) {
            override fun onError(throwable: Throwable?, msg: String?) {
                mIView.onGetAvailableZoneFail()
            }
        }))
    }


}