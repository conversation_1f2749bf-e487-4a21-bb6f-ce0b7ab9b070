package com.ybm100.app.crm.model.drugstore;


import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiDrugstoreService;
import com.ybm100.app.crm.bean.drugstore.PrivateListBean;
import com.ybm100.app.crm.bean.drugstore.PrivateListFilterBean;
import com.ybm100.app.crm.task.bean.TaskAndMerchantBean;
import com.ybm100.app.crm.contract.drugstore.CustomPrivateContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import java.util.HashMap;

import io.reactivex.Observable;

/**
 * <AUTHOR>
 * Created by XyyMvpSportTemplate on 12/20/2018 10:43
 * 我的药店
 */
public class CustomPrivateModel extends BaseModel implements CustomPrivateContract.ICustomPrivateModel {

    public static CustomPrivateModel newInstance() {
        return new CustomPrivateModel();
    }

    @Override
    public Observable<RequestBaseBean<PrivateListBean>> getPrivateListData(HashMap<String, String> map) {
        return RetrofitCreateHelper.createApi(ApiDrugstoreService.class).getPrivateListData(map)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean<TaskAndMerchantBean>> toAddVisit(String merchantId, String customerType) {
        return RetrofitCreateHelper.createApi(ApiDrugstoreService.class).toAddVisit(merchantId, customerType)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean<PrivateListFilterBean>> getFilterItems() {
        return RetrofitCreateHelper.createApi(ApiDrugstoreService.class).getFilterItems()
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean> distributeToBD(String bindUserId, String customerId, String selectedSkuCollectListStr) {
        return RetrofitCreateHelper.createApi(ApiDrugstoreService.class).distributeToBD(bindUserId, customerId, selectedSkuCollectListStr)
                .compose(RxHelper.rxSchedulerHelper());
    }
}