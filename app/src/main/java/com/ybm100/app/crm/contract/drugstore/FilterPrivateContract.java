package com.ybm100.app.crm.contract.drugstore;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.FilterPrivateBean;

import io.reactivex.Observable;

/**
 * @author: zcj
 * @time:2020/4/2. Description:
 */
public interface FilterPrivateContract {
    interface IFilterPrivateModel extends IBaseModel {
        Observable<RequestBaseBean<FilterPrivateBean>> getOtherFilterItems();
    }

    interface IFilterPrivateView extends IBaseActivity {
        void getOtherFilterItemsSuccess(RequestBaseBean<FilterPrivateBean> baseBean);
    }
}
