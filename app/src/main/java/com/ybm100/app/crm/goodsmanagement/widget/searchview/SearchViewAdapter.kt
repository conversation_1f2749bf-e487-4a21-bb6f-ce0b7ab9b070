package com.ybm100.app.crm.goodsmanagement.widget.searchview

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.ybm100.app.crm.R

/**
 * @author: zcj
 * @time:2019/10/19.
 * projectName: XyyBeanSproutsChannel.
 * Description:
 */
class SearchViewAdapter : BaseQuickAdapter<SearchViewItemBean, BaseViewHolder>(R.layout.item_search_view) {
    override fun convert(helper: BaseViewHolder?, item: SearchViewItemBean?) {
        helper?.setText(R.id.tv_tagName, item?.name)
        helper?.addOnClickListener(R.id.iv_delete)
    }
}