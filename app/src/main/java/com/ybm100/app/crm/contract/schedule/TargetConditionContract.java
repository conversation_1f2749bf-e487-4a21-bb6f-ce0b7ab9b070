package com.ybm100.app.crm.contract.schedule;

import android.content.Context;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.ybm100.app.crm.bean.schedule.MerchantEnumsBean;

import io.reactivex.Observable;


/**
 * Created by XyyMvpSportTemplate on 06/21/2019 15:00
 */
public interface TargetConditionContract {

    interface ITargetConditionModel extends IBaseModel {
        Observable<MerchantEnumsBean> merchantEnums(Context context);
    }

    interface ITargetConditionView extends IBaseActivity {
        void merchantEnumsSuccess(MerchantEnumsBean baseBean);
    }

}
