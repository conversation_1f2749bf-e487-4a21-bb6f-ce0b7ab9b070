package com.ybm100.app.crm.presenter.drugstore.minedrug;

import com.xyy.utilslibrary.base.BasePresenter;
import com.ybm100.app.crm.contract.drugstore.minedrug.ContactContract;
import com.ybm100.app.crm.model.drugstore.minedrug.ContactModel;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:06
 */
public class ContactPresenter extends BasePresenter<ContactContract.IContactModel, ContactContract.IContactView> {

    public static ContactPresenter newInstance() {
        return new ContactPresenter();
    }

    @Override
    protected ContactModel getModel() {
        return ContactModel.newInstance();
    }

}
