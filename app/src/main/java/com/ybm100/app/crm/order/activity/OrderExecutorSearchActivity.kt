package com.ybm100.app.crm.order.activity

import android.app.Activity
import android.content.Intent
import com.xyy.utilslibrary.base.BasePresenter
import com.ybm100.app.crm.constant.Constants
import com.ybm100.app.crm.constant.ExtraConstants
import com.ybm100.app.crm.order.bean.OrderExecutorSearchBean
import com.ybm100.app.crm.order.presenter.OrderExecutorSearchPresenter
import com.ybm100.app.crm.ui.activity.BaseSearchActivity
import com.ybm100.app.crm.task.adapter.BaseSearchAdapter

/**
 * Created by dengmingjia on 2018/12/26
 * 选择执行人页面
 */
class OrderExecutorSearchActivity : BaseSearchActivity<OrderExecutorSearchPresenter, OrderExecutorSearchBean>() {
//    var mSelected: OrderExecutorSearchBean? = null

    override fun getCanPickPerson(): Boolean {
        return intent?.getBooleanExtra("canPickPerson", true)?: true
    }

    override fun getOnSelectedListener(): BaseSearchAdapter.OnSelectedListener<OrderExecutorSearchBean> {
        return object : BaseSearchAdapter.OnSelectedListener<OrderExecutorSearchBean> {
            override fun isSelected(item: OrderExecutorSearchBean): Boolean {
//                return mSelected == item
                return false
            }

            override fun onSelected(item: OrderExecutorSearchBean) {
//                mSelected = item;
//                mAdapter.notifyDataSetChanged()
                var intent = Intent()
                intent.putExtra(ExtraConstants.NAME, item.name)
                intent.putExtra(ExtraConstants.ID, item.id)
                intent.putExtra(ExtraConstants.IS_GROUP, !item.isPerson)
                setResult(Activity.RESULT_OK, intent)
                finish()
            }

            override fun selectStatus(t: OrderExecutorSearchBean?): BaseSearchAdapter.SelectStatus {
                return if (t?.isPerson == true) {
                    BaseSearchAdapter.SelectStatus.unselected
                } else {
                    BaseSearchAdapter.SelectStatus.normal
                }
            }
        }
    }

    override fun onStartSearch() {
//        mSelected == null
    }

    override fun onSubmit() {

    }

    override fun initPresenter(): BasePresenter<*, *> {
        val taskID = intent?.extras?.getString(Constants.Task.ARG_TASK_ID, "") ?: ""
        //TODO 后台缺少接口
        /*return if (taskID.isNotEmpty()){
            OrderExecutorPresenter(true, mTaskID)
        }else{
            OrderExecutorPresenter()
        }*/
        return OrderExecutorSearchPresenter(intent.getBooleanExtra("isPop", false))
    }

    override fun showNetError() {

    }
}
