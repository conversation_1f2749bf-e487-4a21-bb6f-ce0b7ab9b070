package com.ybm100.app.crm.schedule.model

import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep

@Keep
data class CallRecord(
        var phoneNum: String,
        var startTime: Long = -1L,
        var originStartTime: Long = -1L,
        var duration: Long = -1L,
        var customerId: String = "",
        var customerName: String = "",
        var failCount: Int = 0,
        var addSource: String = "",
        var handleSource: String = "",
        var callLogList: String = "",
        var exception: String = "",
        var originCallLogList: String = "",
        var originException: String = "",
        var hasReportNotConnect: Boolean
) : Parcelable {
    constructor(source: Parcel) : this(
            source.readString(),
            source.readLong(),
            source.readLong(),
            source.readLong(),
            source.readString(),
            source.readString(),
            source.readInt(),
            source.readString(),
            source.readString(),
            source.readString(),
            source.readString(),
            source.readString(),
            source.readString(),
            1 == source.readInt()
    )

    override fun describeContents() = 0

    override fun writeToParcel(dest: Parcel, flags: Int) = with(dest) {
        writeString(phoneNum)
        writeLong(startTime)
        writeLong(originStartTime)
        writeLong(duration)
        writeString(customerId)
        writeString(customerName)
        writeInt(failCount)
        writeString(addSource)
        writeString(handleSource)
        writeString(callLogList)
        writeString(exception)
        writeString(originCallLogList)
        writeString(originException)
        writeInt((if (hasReportNotConnect) 1 else 0))
    }

    companion object {
        @JvmField
        val CREATOR: Parcelable.Creator<CallRecord> = object : Parcelable.Creator<CallRecord> {
            override fun createFromParcel(source: Parcel): CallRecord = CallRecord(source)
            override fun newArray(size: Int): Array<CallRecord?> = arrayOfNulls(size)
        }
    }
}