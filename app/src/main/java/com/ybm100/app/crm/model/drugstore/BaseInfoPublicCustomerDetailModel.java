package com.ybm100.app.crm.model.drugstore;

import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.utilslibrary.helper.datafactory.MyConverterFactory;
import com.ybm100.app.crm.api.ApiDrugstoreService;
import com.ybm100.app.crm.api.HyApiService;
import com.ybm100.app.crm.api.HyBaseUrl;
import com.ybm100.app.crm.bean.contact.ContactBean;
import com.ybm100.app.crm.bean.drugstore.PublicCustomerDetailBean;
import com.ybm100.app.crm.contract.drugstore.PublicCustomerDetailContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import java.util.List;

import io.reactivex.Observable;

public class BaseInfoPublicCustomerDetailModel implements PublicCustomerDetailContract.IBaseInfoPublicCustomerDetailModel {
    public static BaseInfoPublicCustomerDetailModel newInstance() {
        return new BaseInfoPublicCustomerDetailModel();
    }


    @Override
    public Observable<RequestBaseBean<PublicCustomerDetailBean>> searchOpenSeaDetail(String id) {
        return RetrofitCreateHelper.createApi(ApiDrugstoreService.class).searchOpenSeaDetail(id)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean> receive(String id) {
        // do nothing
        return RetrofitCreateHelper.createApi(ApiDrugstoreService.class).receive(id, "")
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean<List<ContactBean>>> getContactList(String poiId) {
        return RetrofitCreateHelper.createApi(HyApiService.class, HyBaseUrl.getBaseUrl(), MyConverterFactory.create()).getContactList(poiId)
                .compose(RxHelper.rxSchedulerHelper());
    }
}