package com.ybm100.app.crm.contract.hycustomer;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.hycustomer.HyBDFollowUpInfoBean;

import java.util.HashMap;
import java.util.List;

import io.reactivex.Observable;

/**
 * 荷叶健康bd跟进信息接口
 */
public interface HyBDFollowUpInfoContract {

    interface IHyBDFollowUpInfoModel extends IBaseModel {
        Observable<RequestBaseBean<List<HyBDFollowUpInfoBean>>> getBDFollowUpInfoList(HashMap<String, String> map);
    }

    interface IHyBDFollowUpInfoView extends IBaseActivity {
        void getBDFollowUpInfoListSuccess(boolean refresh, RequestBaseBean<List<HyBDFollowUpInfoBean>> bean);

        void getBDFollowUpInfoListFail();

        void enableLoadMore(boolean refresh);

        void loadMoreComplete();
    }

}
