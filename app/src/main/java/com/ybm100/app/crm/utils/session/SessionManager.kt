package com.ybmmarket20.xyyreport.session

import android.text.TextUtils
import java.util.UUID

/**
 * 管理session
 */
class SessionManager private constructor(){

    companion object {

        private var mSession: String = ""

        private var instance: SessionManager? = null
            get() {
                if (field == null) {
                    field = SessionManager()
                }
                return field
            }

        @JvmStatic
        @Synchronized
        fun get(): SessionManager {
            return instance!!
        }
    }

    private var chars: Array<String> = arrayOf(
        "a", "b", "c", "d", "e", "f",
        "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s",
        "t", "u", "v", "w", "x", "y", "z", "0", "1", "2", "3", "4", "5",
        "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "G", "H", "I",
        "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V",
        "W", "X", "Y", "Z"
    )

    private fun generateShortUuid(length: Int): String {
        val shortBuffer = StringBuffer()
        val uuid = UUID.randomUUID().toString().replace("-", "")
        try {
            for (i in 0 until length) {
                val str = uuid.substring(i * 4, i * 4 + 4)
                val x = str.toInt(16)
                shortBuffer.append(chars[x % 0x3E])
            }
            return shortBuffer.toString()
        } catch (e: Exception) {
            e.printStackTrace()
            return uuid.substring(0, length)
        }
    }

    /**
     * 获取Session
     */
    fun getSession(): String {
        if (TextUtils.isEmpty(mSession)) {
            mSession = generateShortUuid(8)
        }
        return mSession
    }

    /**
     * 获取页面编码
     */
    fun newPageSession(): String = generateShortUuid(6)

    /**
     * 获取scm6位随机码
     */
    fun newGoodsScmRandom(): String = generateShortUuid(6)

    /**
     * 模块或子模块
     */
    fun newModuleSession(): String = generateShortUuid(14)

    /**
     * 生成Session
     */
    fun newSession(): String {
        mSession = generateShortUuid(8)
        return mSession
    }

    fun generateScmE(): String = ""
}