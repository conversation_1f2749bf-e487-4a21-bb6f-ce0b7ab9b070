package com.ybm100.app.crm.utils.deviceInfo.collector

import android.content.Context
import android.util.Log
import com.meituan.android.walle.WalleChannelReader
import com.xyy.common.util.ShellUtils
import com.xyy.utilslibrary.utils.DateUtils
import com.xyyio.analysis.util.DeviceInfoUtils
import com.ybm100.app.crm.utils.SharedPrefManager
import java.util.*


class CatchRatCollector : BaseCollector() {

    override fun internalCollect(context: Context): String? {
        val channelInfo = WalleChannelReader.getChannelInfo(context.applicationContext)
        return channelInfo?.let {
            val extra = it.extraInfo?.get("crm_extra") ?: "empty"
            if ("ffffffff-b0b6-b719-ffff-ffffef8ef800" == DeviceInfoUtils.getDeviceId(context)
                    || ("ffffffff-9e5b-fe30-ffff-ffff9de109f0" == DeviceInfoUtils.getDeviceId(context)&& SharedPrefManager.getInstance().userInfo.realName == "xt_zhongtai_21")) {
                sendWXMessage(context, it.channel, extra)
            }
            it.channel + "@" + extra
        }
    }

    private fun sendWXMessage(context: Context, channel: String?, extra: String?) {
        val msgContent = "# ️<font color=\\\\\"warning\\\\\">内鬼已上线</font>️\n" +
                "> <font color=\\\\\"comment\\\\\">channel:${channel}</font>\n" +
                "> <font color=\\\\\"info\\\\\">extra：${extra}</font>\n" +
                "> <font color=\\\\\"comment\\\\\">time：${DateUtils.date2str(Date())}</font>"
        val webHook = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=de3ce815-6dde-4070-8182-14a56ac250da&debug=1"
        val shellCmd = "curl \"${webHook}\" -H \"Content-Type: application/json\" -d \"{\\\"markdown\\\": {\\\"content\\\": \\\"${msgContent}\\\"},\\\"msgtype\\\": \\\"markdown\\\"}\""
        Log.e("collector", shellCmd)
        val execCmd = ShellUtils.execCmd(shellCmd, false)
        Log.e("collector", execCmd.result.toString())
        Log.e("collector", execCmd.errorMsg)
        Log.e("collector", execCmd.successMsg)
    }


}
