package com.ybm100.app.crm.widget.navigation;

import android.content.Context;
import androidx.annotation.Nullable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import com.ybm100.app.crm.R;


/**
 * 自定义底部导航
 */
public class CustomBottomNavigationView extends LinearLayout implements View.OnClickListener {

    private BottomNavigationSimpleItemView bottom_nav1;
    private BottomNavigationSimpleItemView bottom_nav2;
    private BottomNavigationSimpleItemView bottom_nav3;
    private BottomNavigationSimpleItemView bottom_nav4;
    private BottomNavigationSimpleItemView bottom_nav5;
    private int currentPosition = 0;

    public CustomBottomNavigationView(Context context) {
        this(context, null);
    }

    public CustomBottomNavigationView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public CustomBottomNavigationView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs, defStyleAttr);
    }

    private void init(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        LayoutInflater.from(context).inflate(R.layout.layout_custom_bottom_navigation_view, this, true);
        bottom_nav1 = findViewById(R.id.bottom_nav1);
        bottom_nav2 = findViewById(R.id.bottom_nav2);
        bottom_nav3 = findViewById(R.id.bottom_nav3);
        bottom_nav4 = findViewById(R.id.bottom_nav4);
        bottom_nav5 = findViewById(R.id.bottom_nav5);

        bottom_nav1.setText(context.getString(R.string.home_page));
        bottom_nav1.setIcon(context, R.drawable.selector_icon_nav1, 0);

        bottom_nav2.setText(context.getString(R.string.drug_store));
        bottom_nav2.setIcon(context, R.drawable.selector_icon_nav2, 1);

        bottom_nav3.setText("");
        bottom_nav3.setIcon(context, R.drawable.selector_icon_nav3, 2);

        bottom_nav4.setText(context.getString(R.string.message));
        bottom_nav4.setIcon(context, R.drawable.selector_icon_nav4, 3);

        bottom_nav5.setText(context.getString(R.string.mine));
        bottom_nav5.setIcon(context, R.drawable.selector_icon_nav5, 4);

        bottom_nav1.setOnClickListener(this);
        bottom_nav2.setOnClickListener(this);
        bottom_nav3.setOnClickListener(this);
        bottom_nav4.setOnClickListener(this);
        bottom_nav5.setOnClickListener(this);
    }

    public void setMyDotNum(int num) {
        bottom_nav5.setDotNum(num);
    }

    public void setMsgDotNum(int num) {
        bottom_nav4.setDotNum(num);
    }

    public void setSelectedPosition(int position) {
        currentPosition = position;
        if (position == 0) {
            bottom_nav1.setSelected(true);
            changeOtherState(bottom_nav1);
        } else if (position == 1) {
            bottom_nav2.setSelected(true);
            changeOtherState(bottom_nav2);
        } else if (position == 2) {
            bottom_nav3.setSelected(true);
//            changeOtherState(bottom_nav3);
        } else if (position == 3) {
            bottom_nav4.setSelected(true);
            changeOtherState(bottom_nav4);
        } else if (position == 4) {
            bottom_nav5.setSelected(true);
            changeOtherState(bottom_nav5);
        }
    }

    private void changeOtherState(BottomNavigationSimpleItemView currentItem) {
        //实现互斥 让同级的其他的BottomNavigationSimpleItemView控件setSelected(false)
        ViewGroup parentView = (ViewGroup) currentItem.getParent();
        if (parentView != null && parentView.getChildCount() > 0) {
            for (int i = 0; i < parentView.getChildCount(); i++) {
                View brother = parentView.getChildAt(i);
                if (brother instanceof BottomNavigationSimpleItemView) {
                    BottomNavigationSimpleItemView itemView = (BottomNavigationSimpleItemView) brother;
                    if (itemView != currentItem) {
                        itemView.tv_text.setSelected(false);
                        itemView.iv_icon.setSelected(false);
                    }
                }
            }
        }
    }

    public int getCurrentItemPosition() {
        return currentPosition;
    }

    @Override
    public void onClick(View v) {
        if (selectedListener == null) {
            return;
        }
        switch (v.getId()) {
            case R.id.bottom_nav1:
                selectedListener.onItemSelected(0);
                setSelectedPosition(0);
                break;
            case R.id.bottom_nav2:
                selectedListener.onItemSelected(1);
                setSelectedPosition(1);
                break;
            case R.id.bottom_nav3:
                selectedListener.onItemSelected(2);
                setSelectedPosition(2);
                break;
            case R.id.bottom_nav4:
                selectedListener.onItemSelected(3);
                setSelectedPosition(3);
                break;
            case R.id.bottom_nav5:
                selectedListener.onItemSelected(4);
                setSelectedPosition(4);
                break;
        }
    }

    private OnTabSelectedListener selectedListener;

    public void setOnNavigationItemSelectedListener(OnTabSelectedListener selectedListener) {
        this.selectedListener = selectedListener;
    }

    public interface OnTabSelectedListener {
        void onItemSelected(int position);
    }

}
