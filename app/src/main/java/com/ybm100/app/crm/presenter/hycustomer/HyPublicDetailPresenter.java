package com.ybm100.app.crm.presenter.hycustomer;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.hycustomer.HyPublicDetailBean;
import com.ybm100.app.crm.contract.hycustomer.HyPublicDetailContract;
import com.ybm100.app.crm.model.hycustomer.HyPublicDetailModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

import io.reactivex.disposables.Disposable;

/**
 * 荷叶健康公海客户详情Presenter
 */
public class HyPublicDetailPresenter extends BasePresenter<HyPublicDetailContract.IHyPublicDetailModel, HyPublicDetailContract.IHyPublicDetailView> {
    public static HyPublicDetailPresenter newInstance() {
        return new HyPublicDetailPresenter();
    }

    @Override
    protected HyPublicDetailContract.IHyPublicDetailModel getModel() {
        return HyPublicDetailModel.newInstance();
    }

    /**
     * 公海详情
     *
     * @param id 客户ID
     */
    public void searchOpenSeaDetail(String id) {
        if (mIView == null || mIModel == null) return;
        Disposable subscribe = mIModel.searchOpenSeaDetail(id)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean<HyPublicDetailBean>>(mIView, "") {
                    @Override
                    public void onSuccess(RequestBaseBean<HyPublicDetailBean> baseBean) {
                        if (mIView == null) return;
                        mIView.searchOpenSeaDetailSuccess(baseBean);
                    }
                }, new SimpleErrorConsumer(mIView));
        mRxManager.register(subscribe);
    }

    /**
     * 认领
     *
     * @param id 客户ID
     */
    public void receive(String id) {
        if (mIView == null || mIModel == null) return;
        Disposable subscribe = mIModel.receive(id)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean>(mIView, "") {
                    @Override
                    public void accept(RequestBaseBean baseBean) throws Exception {
                        if (mIView == null) return;
                        mIView.hideWaitDialog();
                        if (baseBean.isSuccess()) {
                            mIView.receiveSuccess(baseBean);
                        } else if (!baseBean.isSuccess() && baseBean.getCode() == 405){
                            mIView.receiveSuccess(baseBean);
                        }else {
                            mIView.showToast(baseBean.getErrorMsg());
                        }
                    }

                    @Override
                    public void onSuccess(RequestBaseBean baseBean) {

                    }
                }, new SimpleErrorConsumer(mIView));
        mRxManager.register(subscribe);
    }

}
