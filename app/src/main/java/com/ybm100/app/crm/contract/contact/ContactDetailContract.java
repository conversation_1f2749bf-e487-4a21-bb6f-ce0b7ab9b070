package com.ybm100.app.crm.contract.contact;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.contact.ContactBean;
import com.ybm100.app.crm.bean.contact.WrapCallLogBean;

import io.reactivex.Observable;


/**
 * Created by XyyMvpSportTemplate on 12/25/2018 14:02
 */
public interface ContactDetailContract {

    interface IContactDetailView extends IBaseActivity {

        void renderContactDetail(ContactBean data);

        void renderContactCallRecord(WrapCallLogBean callLogBeans);

        void updateTags(ContactBean contact);
    }

    interface IContactDetailModel extends IBaseModel {

        Observable<RequestBaseBean> addContactTag(String id, String trim);
    }

}
