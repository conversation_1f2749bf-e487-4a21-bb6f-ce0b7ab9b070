package com.ybm100.app.crm.presenter.hycustomer;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.PrivateListFilterBean;
import com.ybm100.app.crm.bean.hycustomer.HyPrivateListBean;
import com.ybm100.app.crm.contract.hycustomer.HyPrivateListContract;
import com.ybm100.app.crm.model.hycustomer.HyPrivateListModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;
import com.ybm100.app.crm.task.bean.TaskAndMerchantBean;

import java.util.HashMap;

/**
 * 荷叶健康私海客户presenter
 */
public class HyPrivateListPresenter extends BasePresenter<HyPrivateListContract.IHyPrivateListModel, HyPrivateListContract.IHyPrivateListView> {
    int pageSize = 10;
    int pageNo = 0;

    public static HyPrivateListPresenter newInstance() {
        return new HyPrivateListPresenter();
    }

    @Override
    protected HyPrivateListModel getModel() {
        return HyPrivateListModel.newInstance();
    }

    //获取我的药店列表
    public void getListData(final boolean refresh, HashMap<String, String> map) {
        if (mIView == null || mIModel == null) return;
        if (refresh) {
            pageNo = 0;
            mIView.enableLoadMore(true);
        }
        map.put("offset", String.valueOf(pageNo));
        map.put("limit", String.valueOf(pageSize));
        mRxManager.register(mIModel.getPrivateListData(map)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean<HyPrivateListBean>>(mIView, true) {
                    @Override
                    public void onSuccess(RequestBaseBean<HyPrivateListBean> listRequestBaseBean) {
                        if (mIView == null) return;
                        if (listRequestBaseBean.getData() == null || listRequestBaseBean.getData().getPageData() == null) {
                            mIView.showEmpty();
                            return;
                        }
                        HyPrivateListBean result = listRequestBaseBean.getData();
                        if (result != null) {
                            if (listRequestBaseBean.getData().getPageData().isLastPage()) {
                                mIView.loadMoreComplete();//超出一页没有更多的数据
                            }
                            mIView.getListDataSuccess(refresh, listRequestBaseBean);
                        }
                        pageNo++;
                    }
                }, new SimpleErrorConsumer(mIView)));
    }

    public void toAddVisit(String merchantId, String customerType) {
        if (mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.toAddVisit(merchantId, customerType)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean<TaskAndMerchantBean>>(mIView, "") {
                    @Override
                    public void onSuccess(RequestBaseBean<TaskAndMerchantBean> listRequestBaseBean) {
                        if (mIView == null) return;
                        mIView.toAddVisit(listRequestBaseBean);
                    }

                    @Override
                    public void onFailure(int errorCode) {

                    }
                }, new SimpleErrorConsumer(mIView) {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        mIView.showToast(throwable.getMessage());
                    }
                }));
    }

    public void distributeToBD(String bindUserId, String customerId) {
        if (mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.distributeToBD(bindUserId, customerId)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean>(mIView, "") {
                    @Override
                    public void onSuccess(RequestBaseBean listRequestBaseBean) {
                        if (mIView == null) return;
                        mIView.distributeToBDSuccess(listRequestBaseBean);
                    }

                    @Override
                    public void onFailure(int errorCode) {

                    }
                }, new SimpleErrorConsumer(mIView) {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        mIView.showToast(throwable.getMessage());
                    }
                }));
    }

    public void getFilterItems() {
        if (mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.getFilterItems()
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean<PrivateListFilterBean>>(mIView, true) {
                    @Override
                    public void onSuccess(RequestBaseBean<PrivateListFilterBean> listRequestBaseBean) {
                        if (mIView == null) return;
                        mIView.getFilterItemsSuccess(listRequestBaseBean);
                    }
                }, new SimpleErrorConsumer(mIView) {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        mIView.showToast(throwable.getMessage());
                    }
                }));
    }
}

