package com.ybm100.app.crm.utils.deviceInfo.collector

import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.content.pm.ResolveInfo
import android.text.TextUtils
import java.io.BufferedReader
import java.io.InputStreamReader


class AppListCollector : BaseCollector() {

    override fun internalCollect(context: Context): String? {
        var pkgList: List<String>? = null
        var prefix = "pkgm filter,"
        pkgList = queryFilterAppInfo(context)
        if (pkgList.isNullOrEmpty()) {
            pkgList = getPkgListNew(context)
            prefix = "pkgm all,"
        }
        if (pkgList.isNullOrEmpty()) {
            pkgList = getPkgList()
            prefix = "shell,"
        }
        return pkgList?.joinToString(separator = ",", prefix = prefix) ?: "empty"
    }

    private fun getPkgList(): List<String>? {
        val packages: MutableList<String> = ArrayList()
        try {
            val p = Runtime.getRuntime().exec("pm list packages")
            val isr = InputStreamReader(p.inputStream, "utf-8")
            val br = BufferedReader(isr)
            var line: String = br.readLine()
            while (line != null) {
                line = line.trim { it <= ' ' }
                if (line.length > 8) {
                    val prefix = line.substring(0, 8)
                    if (prefix.equals("package:", ignoreCase = true)) {
                        line = line.substring(8).trim { it <= ' ' }
                        if (!TextUtils.isEmpty(line)) {
                            packages.add(line)
                        }
                    }
                }
                line = br.readLine()
            }
            br.close()
            p.destroy()
        } catch (ignore: Throwable) {

        }
        return packages
    }

    private fun getPkgListNew(context: Context): List<String>? {
        val packages: MutableList<String> = ArrayList()
        try {
            val packageInfos: List<PackageInfo> = context.packageManager.getInstalledPackages(PackageManager.GET_ACTIVITIES or
                    PackageManager.GET_SERVICES)
            for (info in packageInfos) {
                val pkg = info.packageName
                packages.add(pkg)
            }
        } catch (t: Throwable) {
            t.printStackTrace()
        }
        return packages
    }

    private fun queryFilterAppInfo(context: Context) :List<String>? {
        val pkgList = arrayListOf<String>()
        val pm: PackageManager = context.packageManager
        // 查询所有已经安装的应用程序
        val appInfos = pm.getInstalledApplications(PackageManager.GET_UNINSTALLED_PACKAGES) // GET_UNINSTALLED_PACKAGES代表已删除，但还有安装目录的

        // 创建一个类别为CATEGORY_LAUNCHER的该包名的Intent
        val resolveIntent = Intent(Intent.ACTION_MAIN, null)
        resolveIntent.addCategory(Intent.CATEGORY_LAUNCHER)

        // 通过getPackageManager()的queryIntentActivities方法遍历,得到所有能打开的app的packageName
        val resolveinfoList: List<ResolveInfo> = context.packageManager
                .queryIntentActivities(resolveIntent, 0)
        val allowPackages: MutableSet<String> = HashSet()
        for (resolveInfo in resolveinfoList) {
            allowPackages.add(resolveInfo.activityInfo.packageName)
        }
        for (app in appInfos) {
//            if((app.flags & ApplicationInfo.FLAG_SYSTEM) <= 0)//通过flag排除系统应用，会将电话、短信也排除掉
//            {
//                applicationInfos.add(app);
//            }
//            if(app.uid > 10000){//通过uid排除系统应用，在一些手机上效果不好
//                applicationInfos.add(app);
//            }
            if (allowPackages.contains(app.packageName)) {
                pkgList.add("${app.packageName}")
            }
        }
        return pkgList
    }


}
