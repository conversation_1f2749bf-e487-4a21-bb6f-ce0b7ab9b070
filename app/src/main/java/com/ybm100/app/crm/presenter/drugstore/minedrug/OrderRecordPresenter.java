package com.ybm100.app.crm.presenter.drugstore.minedrug;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.DrugstoreOrderBean;
import com.ybm100.app.crm.contract.drugstore.minedrug.OrderRecordContract;
import com.ybm100.app.crm.model.drugstore.minedrug.OrderRecordModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:09
 */
public class OrderRecordPresenter extends BasePresenter<OrderRecordContract.IOrderRecordModel, OrderRecordContract.IOrderRecordView> {

    public static OrderRecordPresenter newInstance() {
        return new OrderRecordPresenter();
    }

    @Override
    protected OrderRecordModel getModel() {
        return OrderRecordModel.newInstance();
    }

    /**
     * 获取订单记录数据
     *
     * @param limit
     * @param merchantId
     * @param offset
     */
    public void getOrderListData(int limit, String merchantId, int offset) {

        if (mIView == null || mIModel == null) return;

        mRxManager.register(mIModel.getOrderListData(limit, merchantId, offset).subscribe(new SimpleSuccessConsumer<RequestBaseBean<DrugstoreOrderBean>>(mIView) {
            @Override
            public void onSuccess(RequestBaseBean<DrugstoreOrderBean> requestBaseBean) {

                mIView.getOrderListDataSuccess(requestBaseBean);
            }
        }, new SimpleErrorConsumer(mIView) {
            @Override
            protected void onError(Throwable throwable, String msg) {
                super.onError(throwable, msg);
                if (mIView == null) return;
                mIView.showNetError();
            }
        }));

    }

}
