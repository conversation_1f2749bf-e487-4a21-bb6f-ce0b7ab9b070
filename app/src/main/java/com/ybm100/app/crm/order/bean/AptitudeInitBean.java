package com.ybm100.app.crm.order.bean;

import android.os.Parcel;
import android.os.Parcelable;

import com.xyy.utilslibrary.base.bean.RequestBaseBean;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @file AptitudeInitBean.java
 * @brief
 * @date 2019/1/11
 * Copyright (c) 2019, 北京小药药
 * All rights reserved.
 */
public class AptitudeInitBean extends RequestBaseBean {


    public List<ImageItem> licenseAuditImgList;
    public List<Licence> necessaryLicenceList;
    public List<Licence> optionalLicenceList;
    public List<qualificationInfo> qualificationInfoDTOS;
    public LicenceItem customer;

    public static class ImageItem {
        public int auditId;
        public String licenseCategoryCode;//资质的分类
        public String name;
        public String statusName;
        public List<String> licenseImgUrls;
        public String xyyEntrusCode;//小药药委托书编号
        public long xyyEntrusValidateTime;//小药药委托书有效期


//        public String getImageUrlWithPosition(int position) {
//            try {
//                String[] split = licenseImgUrls.split(",");
//                return split[position];
//            } catch (Exception exception) {
//                return "";
//            }
//
//        }


    }

    public String getRemark() {
        if (customer == null) {
            return "";
        } else {
            return customer.remark;
        }
    }

    public static class qualificationInfo implements Parcelable {
        public String name;
        public String remark;
        public List<String> imgUrl;


        protected qualificationInfo(Parcel in) {
            name = in.readString();
            remark = in.readString();
            imgUrl = in.createStringArrayList();
        }

        public static final Creator<qualificationInfo> CREATOR = new Creator<qualificationInfo>() {
            @Override
            public qualificationInfo createFromParcel(Parcel in) {
                return new qualificationInfo(in);
            }

            @Override
            public qualificationInfo[] newArray(int size) {
                return new qualificationInfo[size];
            }
        };

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeString(name);
            dest.writeString(remark);
            dest.writeStringList(imgUrl);
        }
    }

    public static class LicenceItem implements Parcelable {
        public int id;
        public int audit1Status;
        public String remark;
        public int customerType;
        public int provinceCode;
        public String provinceName;
        public int cityCode;
        public String cityName;
        public int areaCode;
        public String area;
        public String street;
        public int streetCode;
        public String address;
        public String realName;
        public String applicationNumber;
        public String qualificationNo;

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeInt(this.id);
            dest.writeInt(this.audit1Status);
            dest.writeString(this.remark);
            dest.writeInt(this.customerType);
            dest.writeInt(this.provinceCode);
            dest.writeString(this.provinceName);
            dest.writeInt(this.cityCode);
            dest.writeString(this.cityName);
            dest.writeInt(this.areaCode);
            dest.writeString(this.area);
            dest.writeString(this.street);
            dest.writeInt(this.streetCode);
            dest.writeString(this.address);
            dest.writeString(this.realName);
            dest.writeString(this.applicationNumber);
            dest.writeString(this.qualificationNo);
        }

        public void readFromParcel(Parcel source) {
            this.id = source.readInt();
            this.audit1Status = source.readInt();
            this.remark = source.readString();
            this.customerType = source.readInt();
            this.provinceCode = source.readInt();
            this.provinceName = source.readString();
            this.cityCode = source.readInt();
            this.cityName = source.readString();
            this.areaCode = source.readInt();
            this.area = source.readString();
            this.street = source.readString();
            this.streetCode = source.readInt();
            this.address = source.readString();
            this.realName = source.readString();
            this.applicationNumber = source.readString();
            this.qualificationNo = source.readString();
        }

        public LicenceItem() {
        }

        protected LicenceItem(Parcel in) {
            this.id = in.readInt();
            this.audit1Status = in.readInt();
            this.remark = in.readString();
            this.customerType = in.readInt();
            this.provinceCode = in.readInt();
            this.provinceName = in.readString();
            this.cityCode = in.readInt();
            this.cityName = in.readString();
            this.areaCode = in.readInt();
            this.area = in.readString();
            this.street = in.readString();
            this.streetCode = in.readInt();
            this.address = in.readString();
            this.realName = in.readString();
            this.applicationNumber = in.readString();
            this.qualificationNo = in.readString();
        }

        public static final Creator<LicenceItem> CREATOR = new Creator<LicenceItem>() {
            @Override
            public LicenceItem createFromParcel(Parcel source) {
                return new LicenceItem(source);
            }

            @Override
            public LicenceItem[] newArray(int size) {
                return new LicenceItem[size];
            }
        };
    }

    public static class Licence implements Parcelable {
        public String name;
        public String code;
        public int isRequired;
        public int validateFlag;//0:正常,1临期,2过期
        public List<String> enclosureList;


        protected Licence(Parcel in) {
            name = in.readString();
            code = in.readString();
            isRequired = in.readInt();
            validateFlag = in.readInt();
            enclosureList = in.createStringArrayList();
        }

        public static final Creator<Licence> CREATOR = new Creator<Licence>() {
            @Override
            public Licence createFromParcel(Parcel in) {
                return new Licence(in);
            }

            @Override
            public Licence[] newArray(int size) {
                return new Licence[size];
            }
        };

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeString(name);
            dest.writeString(code);
            dest.writeInt(isRequired);
            dest.writeInt(validateFlag);
            dest.writeStringList(enclosureList);
        }
    }
}
