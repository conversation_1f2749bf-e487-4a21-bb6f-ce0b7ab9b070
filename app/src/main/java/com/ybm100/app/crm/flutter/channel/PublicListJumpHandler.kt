package com.ybm100.app.crm.flutter.channel

import android.os.Bundle
import android.os.Handler
import androidx.fragment.app.FragmentActivity
import com.xyy.flutter.container.container.bridge.BaseHandler
import com.xyy.utilslibrary.rxbus.RxBus
import com.ybm100.app.crm.constant.RxBusCode

class PublicListJumpHandler : BaseHandler() {

    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {
        activity.finish()
        Handler().postDelayed({
            val bundle = Bundle()
            //跳转公海列表
            bundle.putInt("index", 1)
            bundle.putInt("position", 1)

            RxBus.get().send(RxBusCode.RX_BUS_ENTER_CLIENT_WITH_CONDITIONS, bundle)
        }, 200)


    }


}
