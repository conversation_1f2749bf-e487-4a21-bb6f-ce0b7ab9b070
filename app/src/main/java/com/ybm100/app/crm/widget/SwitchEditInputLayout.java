package com.ybm100.app.crm.widget;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.AnimationDrawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.tbruyelle.rxpermissions2.Permission;
import com.tbruyelle.rxpermissions2.RxPermissions;
import com.xyy.common.util.ToastUtils;
import com.xyy.utilslibrary.utils.LogUtils;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.function.speech.SpeechManager;
import com.ybm100.app.crm.permission.PermissionUtil;

import io.reactivex.functions.Consumer;

import static com.xyy.utilslibrary.utils.ResourcesUtils.getString;

/**
 * Author ： LoveNewsweetheart
 * Date:2019/1/7
 */
public class SwitchEditInputLayout extends LinearLayout implements View.OnClickListener, View.OnFocusChangeListener, SpeechManager.OnSpeechCallback {

    private TextView tv_voice;
    private TextView tv_text;
    private ViewGroup layout_voice;
    private ViewGroup layout_text;
    private ViewGroup layout_icon_input;//语音输入按钮布局
    private ImageView iv_speech;
    private ImageView iv_voicing_anim;

    private SpeechManager speechManager;
    private InputMethodManager mInputManager;
    private EditText targetEditText;
    private RxPermissions rxPermissions;
    private Context mContext;
    private AnimationDrawable animationDrawableVoicing;
    private OnVoiceShowListener listener;

    public SwitchEditInputLayout(Context context) {
        super(context);
        init(context);
    }

    public SwitchEditInputLayout(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public SwitchEditInputLayout(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        LayoutInflater.from(context).inflate(R.layout.layout_switch_edit_input, this, true);

        mContext = context;
        mInputManager = (InputMethodManager) context.getSystemService(Context.INPUT_METHOD_SERVICE);
        speechManager = new SpeechManager(context, this);
        rxPermissions = new RxPermissions((Activity) context);

        iv_voicing_anim = findViewById(R.id.iv_voicing_anim);
        tv_voice = findViewById(R.id.tv_voice);
        tv_text = findViewById(R.id.tv_text);
        layout_icon_input = findViewById(R.id.ll_icon_input);
        layout_text = findViewById(R.id.layout_text);
        layout_voice = findViewById(R.id.layout_voice);
        iv_speech = findViewById(R.id.iv_speech);

        layout_voice.setOnClickListener(this);
        layout_text.setOnClickListener(this);
        iv_speech.setClickable(true);
        iv_speech.setOnTouchListener(new SpeechTouchListener());

        animationDrawableVoicing = (AnimationDrawable) iv_voicing_anim.getDrawable();

        tv_text.setSelected(true);

        setVisibility(GONE);
    }


    public void setTargetEditText(EditText targetEditText) {
        this.targetEditText = targetEditText;
        targetEditText.setOnFocusChangeListener(this);
        targetEditText.setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.layout_voice:
                hideKeyboard();
                tv_voice.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        tv_text.setSelected(false);
                        tv_voice.setSelected(true);
                        layout_icon_input.setVisibility(VISIBLE);
                        if (listener != null) {
                            listener.onVoiceShow(true);
                        }
                    }
                }, 90);
                break;
            case R.id.layout_text:
                tv_text.setSelected(true);
                tv_voice.setSelected(false);
                layout_icon_input.setVisibility(GONE);
                if (listener != null) {
                    listener.onVoiceShow(false);
                }
                showKeyboard();
                break;
        }
        if (v.getId() == targetEditText.getId()) {
            layout_text.performClick();
        }
    }

    public boolean isShowVoice() {
        return layout_icon_input.getVisibility() == View.VISIBLE;
    }

    public void setOnVoiceLayoutShowListener(OnVoiceShowListener listener) {
        this.listener = listener;
    }


    public void switchText() {
        layout_text.performClick();
    }


    private void hideKeyboard() {
        mInputManager.hideSoftInputFromWindow(targetEditText.getWindowToken(), 0);
    }

    private void showKeyboard() {
        mInputManager.showSoftInput(targetEditText, InputMethodManager.SHOW_FORCED);
    }

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        if (hasFocus) {
            setVisibility(VISIBLE);
        } else {
            setVisibility(GONE);
        }
    }

    @Override
    public void onSpeechConvertResult(String content, boolean isLast) {
//        if (!isLast) return;
        String oldContent = targetEditText.getText().toString();//输入框中的内容
        LogUtils.e("content = " + content);
        if (!TextUtils.isEmpty(oldContent) && oldContent.length() > 0 && oldContent.substring(oldContent.length() - 1).equals(content)) {
            return;
        }

        int startIndex = targetEditText.getSelectionStart();//输入框中的光标位置
        String newContent;
        if (startIndex < oldContent.length()) {
            newContent = oldContent.substring(0, startIndex) + content + oldContent.substring(startIndex);
        } else {
            newContent = oldContent + content;
        }
        targetEditText.setText(newContent);
        int newIndex = startIndex + content.length();
        if (targetEditText.getText().length() <= newIndex) {
            newIndex = targetEditText.getText().toString().length();
        }
        targetEditText.setSelection(newIndex);

        //由于产品是傻逼，设计上就出现了问题，不让输入非法字符，只能输入汉字和一些普通的标点符号，不能输入空格，也不知道产品的脑袋什么时候被驴踢了，但是科大讯飞的语音识别测试和产品人员却告诉我无法识别英文，
        //傻逼产品认为是可以输入英文的，你说多傻逼，你见过不带空格的英文语句吗？例如how old are you必须写成howoldareyouo。因为不带空格啊？
        //有一种情况是，输入框限制禁止输入非法字符，而当输入带有英文时，英文这里就属于非法字符，那么可能造成setText后editText没有字体显示，getText()返回的是""，造成以前输入的那些
        //文字全部都消失，为了避免造成这样的情况，因此做如下处理
        if (!TextUtils.isEmpty(content) && content.length() > 0) {//说明这次输入的有内容
            if (oldContent.length() > targetEditText.getText().toString().length()) {
                //本来如果不是非法字符的情况下，该if语句不成立的，那么成立了说明上述的情况发生了
                targetEditText.setText(oldContent);
                targetEditText.setSelection(oldContent.length());
                ToastUtils.showShort("有非法字符，请注意语言");
            }
        }
    }

    @Override
    public void onVolumeChanged(int volume) {

    }

    @Override
    public void onBeginOfSpeech() {
        if (!animationDrawableVoicing.isRunning()) {
            animationDrawableVoicing.start();
        }
        iv_voicing_anim.setVisibility(VISIBLE);
    }

    @Override
    public void onError(int code, String hintMsg) {
        ToastUtils.showShort(hintMsg);
        if (animationDrawableVoicing.isRunning()) {
            animationDrawableVoicing.stop();
        }
        iv_voicing_anim.setVisibility(INVISIBLE);
    }

    @Override
    public void onEndOfSpeech() {
        if (animationDrawableVoicing.isRunning()) {
            animationDrawableVoicing.stop();
        }
        iv_voicing_anim.setVisibility(INVISIBLE);
    }

    private class SpeechTouchListener implements OnTouchListener {

        @Override
        public boolean onTouch(View v, MotionEvent event) {
            if (event.getAction() == MotionEvent.ACTION_DOWN) {
                isActionDown = true;
                checkVoicePermission();
            } else if (event.getAction() == MotionEvent.ACTION_UP) {
                isActionDown = false;
                speechManager.stopSpeech();
                LogUtils.e(" ----------------------------up-------------------------");
            }
            return false;
        }
    }

    private boolean isActionDown = false;

    @SuppressLint("CheckResult")
    private void checkVoicePermission() {
        rxPermissions.requestEach(Manifest.permission.RECORD_AUDIO, Manifest.permission.WRITE_EXTERNAL_STORAGE).subscribe(new Consumer<Permission>() {
            @Override
            public void accept(Permission permission) throws Exception {
                if (permission.granted) {
                    if (isActionDown) {
                        speechManager.startSpeech();
                    }
                } else if (permission.shouldShowRequestPermissionRationale) {
                    ToastUtils.showShort(getString(R.string.please_open_storage_permission));
                } else {
                    PermissionUtil.showPermissionDialog(mContext, getString(R.string.storage_permission_name), false);
                }
            }
        });
    }


    public interface OnVoiceShowListener {
        void onVoiceShow(boolean isShow);
    }
}
