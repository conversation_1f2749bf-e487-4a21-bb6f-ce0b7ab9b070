package com.ybm100.app.crm.utils.module.normal

import android.content.Context
import android.util.Log
import android.view.ViewGroup
import androidx.lifecycle.LifecycleObserver
import androidx.recyclerview.widget.RecyclerView
import com.ybm100.app.crm.utils.module.IModuleItem
import com.ybm100.app.crm.utils.module.factory.IModuleFactory
import com.ybm100.app.crm.utils.module.module.BaseModule

class DefaultModulesAdapter(private val context: Context,
                            private val moduleFactory: IModuleFactory<out BaseModule<*>>)
    : RecyclerView.Adapter<DefaultModulesAdapter.HomeModuleHolder>(), LifecycleObserver {

    private var moduleItems: MutableList<IModuleItem> = mutableListOf()


    fun setModulesData(moduleItems: List<IModuleItem>) {
        this.moduleItems.clear()
        this.moduleItems.addAll(moduleItems)
        notifyDataSetChanged()
    }

    fun getCurrentItems(): List<IModuleItem> {
        return moduleItems
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): HomeModuleHolder {
        return HomeModuleHolder(moduleFactory.build(viewType, 0).also {
            it.fixLayoutParams()
        })
    }

    override fun getItemCount(): Int {
        return moduleItems.size
    }

    override fun getItemViewType(position: Int): Int {
        return moduleItems[position].getLocalTypeId()
    }

    override fun onBindViewHolder(holder: HomeModuleHolder, position: Int) {
        holder.bind(position)
    }

    override fun onViewDetachedFromWindow(holder: HomeModuleHolder) {
        super.onViewDetachedFromWindow(holder)
        holder.destroy()
    }

    class HomeModuleHolder(private val baseModule: BaseModule<*>) : RecyclerView.ViewHolder(baseModule) {
        fun bind(position: Int) {
            baseModule.position = position
            baseModule.init()
        }

        fun refresh() {
            baseModule.onRefresh()
        }


        fun destroy() {
            baseModule.onDestroy()
        }

    }
}