package com.ybm100.app.crm.constant;

import com.ybm100.app.crm.bean.user.UserInfoBean;
import com.ybm100.app.crm.utils.LzRoleInfoManager;
import com.ybm100.app.crm.utils.SharedPrefManager;

/**
 * <AUTHOR>
 * @date 2019/1/23
 */
public class RoleTypeConfig {
    /**
     * 角色类型 0 :BD 1:BDM 2:跟进人 3:跟进人BDM
     */
    public static final int TYPE_BD = 0;
    public static final int TYPE_BDM = 1;
    public static final int TYPE_GJR = 2;
    public static final int TYPE_GJR_BDM = 3;

    /**
     * @return 是否是跟进人或者跟进人bdm
     */
    public static boolean isGJRGrop() {
        UserInfoBean userInfoBean = SharedPrefManager.getInstance().getUserInfo();
        if (userInfoBean != null) {
            int roleType = userInfoBean.getRoleType();
            return TYPE_GJR == roleType || TYPE_GJR_BDM == roleType;
        }
        return false;
    }

    /**
     * @return 是否是跟进人或者跟进人bdm
     */
    public static boolean isBDMOrGJRBDM() {
        UserInfoBean userInfoBean = SharedPrefManager.getInstance().getUserInfo();
        if (userInfoBean != null) {
            int roleType = userInfoBean.getRoleType();
            return TYPE_BDM == roleType || TYPE_GJR_BDM == roleType;
        }
        return false;
    }

    /**
     * @return 是否是跟进人或者跟进人bdm
     */
    public static boolean isBDOrGJR() {
        UserInfoBean userInfoBean = SharedPrefManager.getInstance().getUserInfo();
        if (userInfoBean != null) {
            int roleType = userInfoBean.getRoleType();
            return TYPE_BD == roleType || TYPE_GJR == roleType;
        }
        return false;
    }

    public static boolean isLzType() {
        return LzRoleInfoManager.INSTANCE.isOnlyLzType();
    }
    public static boolean isHyType() {
        return LzRoleInfoManager.INSTANCE.isOnlyHyType();
    }
}
