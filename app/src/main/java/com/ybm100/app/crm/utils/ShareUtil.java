package com.ybm100.app.crm.utils;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;

import androidx.core.content.FileProvider;

import com.xyy.common.util.ToastUtils;

import java.io.File;
import java.util.List;

public class ShareUtil {

    /**
     * 通过系统分享pdf文件到微信好友
     */
    public static void sharePdfFileWechatFriend(Context context, File file) {
        Uri uri;
        int currentapiVersion = android.os.Build.VERSION.SDK_INT;
        if (currentapiVersion >= Build.VERSION_CODES.N) {//若SDK大于等于24  获取uri采用共享文件模式
            uri = FileProvider.getUriForFile(context, context.getPackageName() + ".fileProvider", file);
        } else {
            uri = Uri.fromFile(file);
        }
        Intent share = new Intent(Intent.ACTION_SEND);
        share.putExtra(Intent.EXTRA_STREAM, uri);
        share.setType("application/pdf");//此处可发送多种文件
        share.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        share.addCategory(Intent.CATEGORY_DEFAULT);
        share.setPackage("com.tencent.mm");
        if (isWeChatInstall(context)){
            context.startActivity(Intent.createChooser(share, "分享"));
        }else {
            ToastUtils.showShort("您需要安装微信客户端");
        }
    }

    public static boolean isWeChatInstall(Context context) {
        PackageManager manager = context.getPackageManager();
        if (manager == null || manager.getInstalledPackages(0) == null) {
            ToastUtils.showShort("微信未安装");
            return false;
        }
        List<PackageInfo> infoList = manager.getInstalledPackages(0);
        for (PackageInfo info :
                infoList) {
            if ("com.tencent.mm".equals(info.packageName)) {
                return true;
            }
        }
        ToastUtils.showShort("微信未安装");
        return false;
    }

}
