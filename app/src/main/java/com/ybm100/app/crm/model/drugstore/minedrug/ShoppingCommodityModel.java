package com.ybm100.app.crm.model.drugstore.minedrug;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiDrugstoreInfo;
import com.ybm100.app.crm.api.ApiService;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.CartBean;
import com.ybm100.app.crm.contract.drugstore.minedrug.ShoppingCommodityContract;
import com.ybm100.app.crm.goodsmanagement.bean.EstimatedPriceListBean;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import java.util.HashMap;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:20
 */
public class ShoppingCommodityModel extends BaseModel implements ShoppingCommodityContract.IShoppingCommodityModel {

    public static ShoppingCommodityModel newInstance() {
        return new ShoppingCommodityModel();
    }

    @Override
    public Observable<RequestBaseBean<CartBean>> getCartListRequest(String shopId) {
        return RetrofitCreateHelper.createApi(ApiDrugstoreInfo.class).getCartListRequest(shopId)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean<EstimatedPriceListBean>> getEstimatedPrices(HashMap<String, String> queryMap) {
        return RetrofitCreateHelper.createApi(ApiService.class).getEstimatedPrices(queryMap)
                .compose(RxHelper.rxSchedulerHelper());
    }
}