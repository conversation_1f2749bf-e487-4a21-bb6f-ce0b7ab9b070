package com.ybm100.app.crm.order.bean

/**
 * @author: yuh<PERSON><PERSON>
 * @time: 2018/12/27 下午2:49.
 * projectName: XyyBeanSprouts.
 * Description: 审批流程的model
 */
class LogisticsBean {


    /**
     * orderDelivery : {"createDate":1555493585763,"deliveryType":0,"deliveryTypeStr":"待分配","isSign":1,"orderDeliveryLogisticsDetailList":[{"deliveryTime":1555481087000,"description":"订单已完成。感谢您在小药药购物，欢迎再次光临！"},{"deliveryTime":1555481046000,"description":"您的订单已打包完成，等待发货"},{"deliveryTime":1555481042000,"description":"您提交了订单，请等待系统确认"}],"orderNo":"YBM20190417140224100022"}
     * orderDeliveryMessageList : [{"createDate":1555493585000,"deliveryType":0,"deliveryTypeStr":"待分配","isSign":1,"orderDeliveryLogisticsDetailList":[{"deliveryTime":1555481087000,"description":"订单已完成。感谢您在小药药购物，欢迎再次光临！"},{"deliveryTime":1555481046000,"description":"您的订单已打包完成，等待发货"},{"deliveryTime":1555481042000,"description":"您提交了订单，请等待系统确认"}],"orderNo":"YBM20190417140224100022"}]
     */

    var orderDelivery: OrderDeliveryBean? = null

    var orderDeliveryMessageList: List<OrderDeliveryMessageListBean>? = null

    class OrderDeliveryBean {
        /**
         * createDate : 1555493585763
         * deliveryType : 0
         * deliveryTypeStr : 待分配
         * isSign : 1
         * orderDeliveryLogisticsDetailList : [{"deliveryTime":1555481087000,"description":"订单已完成。感谢您在小药药购物，欢迎再次光临！"},{"deliveryTime":1555481046000,"description":"您的订单已打包完成，等待发货"},{"deliveryTime":1555481042000,"description":"您提交了订单，请等待系统确认"}]
         * orderNo : YBM20190417140224100022
         */

        var createDate: Long = 0
        var deliveryType: Int = 0
        var deliveryTypeStr: String? = null
        var isSign: Int = 0
        var bagNum: String? = null
        var orderNo: String? = null
        var orderDeliveryLogisticsDetailList: List<OrderDeliveryLogisticsDetailListBean>? = null

        class OrderDeliveryLogisticsDetailListBean {
            /**
             * deliveryTime : 1555481087000
             * description : 订单已完成。感谢您在小药药购物，欢迎再次光临！
             */

            var deliveryTime: Long = 0
            var description: String? = null
        }
    }

    class OrderDeliveryMessageListBean {
        var createDate: Long = 0
        var deliveryType: Int = 0
        var deliveryTypeStr: String? = null
        var isSign: Int = 0
        var orderNo: String? = null
        var erpNo: String? = null
        var waybillNo: String? = null
        var orderDeliveryLogisticsDetailList: List<OrderDeliveryBean.OrderDeliveryLogisticsDetailListBean>? = null

    }

}
