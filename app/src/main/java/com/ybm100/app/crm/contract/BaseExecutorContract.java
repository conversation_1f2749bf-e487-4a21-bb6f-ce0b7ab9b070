package com.ybm100.app.crm.contract;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.order.bean.DeptHaveAdminBean;
import com.ybm100.app.crm.task.bean.ExecutorLevelItem;

import io.reactivex.Observable;

/**
 * Created by dengmingjia on 2019/1/4
 */
public interface BaseExecutorContract {
    interface IModel<T> extends IBaseModel {
        Observable<RequestBaseBean<T>> getData();

        // 获取用户级别
        Observable<RequestBaseBean<Boolean>> getUserLevel(String oaid);
    }

    interface IView extends IBaseActivity {
        void onGetSuccess(ExecutorLevelItem executor);

        void onGetFail(String msg);

        void onGetDeptSuccess(ExecutorLevelItem item, DeptHaveAdminBean deptHaveAdmin);

        void onGetDeptFail(String msg);

        void onGetUserLevelSuccess(Boolean bean);

        void onGetUserLevelFail(String msg);
    }
}
