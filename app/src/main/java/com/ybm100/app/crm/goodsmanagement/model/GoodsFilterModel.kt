package com.ybm100.app.crm.goodsmanagement.model

import com.xyy.utilslibrary.base.BaseModel
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.helper.RxHelper
import com.ybm100.app.crm.api.ApiService
import com.ybm100.app.crm.goods.api.GoodsApiService
import com.ybm100.app.crm.goodsmanagement.bean.AvailableZone
import com.ybm100.app.crm.goodsmanagement.bean.EstimatedPriceListBean
import com.ybm100.app.crm.goodsmanagement.bean.GoodsManagementListBean
import com.ybm100.app.crm.goodsmanagement.bean.VarietyListBean
import com.ybm100.app.crm.goodsmanagement.contract.GoodsFilterContract
import com.ybm100.app.crm.goodsmanagement.contract.GoodsManagementContract
import com.ybm100.app.crm.net.RetrofitCreateHelper
import com.ybm100.app.crm.task.bean.ShareConfirm
import io.reactivex.Observable
import java.util.*

class GoodsFilterModel : BaseModel(), GoodsFilterContract.IGoodsFilterModel {

    override fun getAvailableZone(queryMap: Map<String, String>): Observable<RequestBaseBean<AvailableZone?>?> {
        return RetrofitCreateHelper.createApi(ApiService::class.java).getAvailableZone(queryMap as HashMap<String, String>?)
                .compose(RxHelper.rxSchedulerHelper<RequestBaseBean<AvailableZone?>?>())
    }
}