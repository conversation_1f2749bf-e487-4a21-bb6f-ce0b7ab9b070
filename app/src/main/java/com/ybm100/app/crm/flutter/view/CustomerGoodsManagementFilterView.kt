package com.ybm100.app.crm.flutter.view

import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.RadioButton
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.RecyclerView
import com.google.android.flexbox.FlexboxLayoutManager
import com.xyy.common.util.ToastUtils
import com.xyy.userbehaviortracking.utils.UserBehaviorTrackingUtils
import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.activity.BaseCompatActivity
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.widgets.WaitProgressDialog
import com.ybm100.app.crm.R
import com.ybm100.app.crm.goodsmanagement.adapter.GoodsZoneFilterAdapter
import com.ybm100.app.crm.goodsmanagement.bean.AvailableZone
import com.ybm100.app.crm.goodsmanagement.bean.Zone
import com.ybm100.app.crm.goodsmanagement.contract.GoodsFilterContract
import com.ybm100.app.crm.goodsmanagement.fragment.CustomerGoodsManagementFilterFragment
import com.ybm100.app.crm.goodsmanagement.presenter.GoodsFilterPresenter
import kotlinx.android.synthetic.main.fragment_customer_goods_management_filter.view.*
import kotlinx.android.synthetic.main.fragment_message_root.view.*
import me.yokeyword.fragmentation.SupportFragment

class CustomerGoodsManagementFilterView(val mMerchantId: String, context: Context) :
    FrameLayout(context, null, 0), View.OnClickListener, GoodsFilterContract.IGoodsFilterView {

    lateinit var mPresenter: GoodsFilterPresenter


    private var mYBMFilterPos = -1
    private var mCollectionStatusFilterPos = -1
    private lateinit var mCustomerDrawerListener: CustomerGoodsManagementFilterFragment.CustomerDrawerListener
    private var mZoneListView: RecyclerView? = null
    private var mZoneLabelView: View? = null
    private var mAdapter: GoodsZoneFilterAdapter? = null


    init {
        mPresenter = initPresenter() as GoodsFilterPresenter
        if (mPresenter != null) {
            mPresenter.attachMV(this)
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        LayoutInflater.from(context).inflate(getLayoutId(), this, true)
        initUI()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        mPresenter.detachMV()
        if (mWaitProgressDialog != null && mWaitProgressDialog!!.isShowing) {
            mWaitProgressDialog!!.dismiss()
        }
    }

    private fun getActivity(): FragmentActivity? {
        var viewContext = context
        while (viewContext is ContextWrapper) {
            if (viewContext is FragmentActivity) {
                return viewContext
            }
            viewContext = viewContext.baseContext
        }
        return null
    }

    fun getLayoutId(): Int {
        return R.layout.fragment_customer_goods_management_filter
    }

    override fun showNetError() {
    }

    override fun onGetAvailableZoneSuccess(data: RequestBaseBean<AvailableZone?>?) {
        data?.data?.rows?.let { it ->
            if (it.isNotEmpty()) {
                setZoneGroupVisible(true)
                mZoneListView?.layoutManager = FlexboxLayoutManager(context)
                mZoneListView?.adapter = GoodsZoneFilterAdapter().also { adapter ->
                    it.find {
                        it?.zoneId == "0"
                    }?.isSelect = true
                    adapter.setNewData(it)
                    mAdapter = adapter
                    adapter.setOnItemClickListener { adapter, view, position ->
                        if (adapter is GoodsZoneFilterAdapter) {
                            UserBehaviorTrackingUtils.track("mc-productmgt-zonesoft")
                            val preIndex = adapter.data.indexOfFirst { zone ->
                                if (zone?.isSelect == true) {
                                    zone.isSelect = false
                                    true
                                } else {
                                    false
                                }
                            }
                            adapter.data.forEach { item ->
                                item?.isSelect = false
                            }
                            adapter.data.getOrNull(position)?.isSelect = true
                            if (preIndex != position) {
                                adapter.notifyItemChanged(preIndex)
                                adapter.notifyItemChanged(position)
                            }
                        }
                    }
                }
            }
        }
    }

    override fun onGetAvailableZoneFail() {
        setZoneGroupVisible(false)
    }

    override fun initPresenter(): BasePresenter<*, *> {
        return GoodsFilterPresenter()
    }

    fun initUI() {
        registerListener()


        if (mYBMFilterPos == 1) {
            (rg_ybm?.getChildAt(mYBMFilterPos) as? RadioButton)?.isChecked = true
        }
        mZoneListView = findViewById(R.id.rv_zone_list)
        mZoneLabelView = findViewById(R.id.tv_zone_list_label)
        if (mMerchantId.isNotEmpty()) {
            requestZoneList()
        }
    }

    private fun setZoneGroupVisible(isVisible: Boolean) {
        (if (isVisible) View.VISIBLE else View.GONE).let {
            mZoneLabelView?.visibility = it
            mZoneListView?.visibility = it
        }
    }


    private fun requestZoneList() {
        val map = HashMap<String, String>().also {
            it["merchantId"] = mMerchantId
        }
        mPresenter.getAvailableZone(map)
    }

    private fun registerListener() {
        rtv_reset.setOnClickListener(this)
        rtv_confirm.setOnClickListener(this)
        rg_ybm.setOnCheckedChangeListener { group, checkedId ->
            when (checkedId) {
                R.id.rb_in_stock -> {
                    mYBMFilterPos = 0

                    UserBehaviorTrackingUtils.track("mc-productmgt-instock")
                }
                R.id.rb_promotional -> {
                    mYBMFilterPos = 1
                }
            }
        }
        rg_collection_status.setOnCheckedChangeListener { group, checkedId ->
            when (checkedId) {
                R.id.rb_uncollected -> {
                    mCollectionStatusFilterPos = 0

                    UserBehaviorTrackingUtils.track("mc-productmgt-markfilter")
                }
                R.id.rb_collected -> {
                    mCollectionStatusFilterPos = 1

                    UserBehaviorTrackingUtils.track("mc-productmgt-unmarkfilter")
                }
            }
        }
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.rtv_reset -> {
                rg_ybm.clearCheck()
                rg_collection_status.clearCheck()
                mAdapter?.let {
                    it.getSelectedZone()?.isSelect = false
                    it.data.firstOrNull()?.isSelect = true
                    it.notifyDataSetChanged()
                }
                mYBMFilterPos = -1
                mCollectionStatusFilterPos = -1
            }
            R.id.rtv_confirm -> {
                val selectedZone = mAdapter?.getSelectedZone()
                Log.e("guan", "selectedZone:${selectedZone}")
                mCustomerDrawerListener.onCustomerConfirmPressed(
                    mYBMFilterPos,
                    mCollectionStatusFilterPos,
                    selectedZone
                )
            }
        }
    }

    fun syncCustomerDrawerFilterData(YBMFilterPos: Int) {
        if (YBMFilterPos == 1) {
            mYBMFilterPos = YBMFilterPos
            (rg_ybm?.getChildAt(mYBMFilterPos) as? RadioButton)?.isChecked = true
        } else {
            mYBMFilterPos = -1
            rg_ybm?.clearCheck()
        }
    }

    fun setCustomerDrawerListener(listener: CustomerGoodsManagementFilterFragment.CustomerDrawerListener) {
        mCustomerDrawerListener = listener
    }


    override fun showWaitDialog(msg: String?) {
        var msg = msg
        if (TextUtils.isEmpty(msg) && container != null) {
            msg = context.getResources().getString(R.string.loading)
        }
        showProgressDialog(msg)
    }

    protected var mWaitProgressDialog: WaitProgressDialog? = null

    /**
     * 显示提示框
     *
     * @param msg 提示框内容字符串
     */
    protected fun showProgressDialog(msg: String?) {
        try {
            if (mWaitProgressDialog?.isShowing() == true) {
                mWaitProgressDialog?.dismiss()
            }
            mWaitProgressDialog?.setMessage(msg)
            mWaitProgressDialog?.show()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 隐藏提示框
     */
    protected fun hideProgressDialog() {
        if (mWaitProgressDialog != null && mWaitProgressDialog?.isShowing() == true) {
            //修改bugly上的bug---（未验证try{}catch{}是否有效）
            try {
                mWaitProgressDialog?.dismiss()
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
            }
        }
    }

    override fun hideWaitDialog() {
    }

    override fun showToast(msg: String?) {
        ToastUtils.showShortSafe(msg)
    }

    override fun back() {
    }

    fun startNewFragment(supportFragment: SupportFragment) {
    }

    fun startNewFragmentWithPop(supportFragment: SupportFragment) {
    }

    fun startNewFragmentForResult(supportFragment: SupportFragment, requestCode: Int) {
    }

    fun popToFragment(targetFragmentClass: Class<*>?, includeTargetFragment: Boolean) {
    }

    override fun hideKeyboard() {
    }

    fun setOnFragmentResult(ResultCode: Int, data: Bundle?) {

    }

    override fun startNewActivity(clz: Class<*>) {
        (getActivity() as BaseCompatActivity).startActivity(clz)
    }

    override fun startNewActivity(clz: Class<*>, bundle: Bundle?) {
        (getActivity() as BaseCompatActivity).startActivity(clz, bundle)
    }

    override fun startNewActivityForResult(clz: Class<*>, bundle: Bundle?, requestCode: Int) {
        (getActivity() as BaseCompatActivity).startActivityForResult(clz, bundle, requestCode)
    }

    fun isVisiable(): Boolean {
        return true
    }

    fun getBindActivity(): Activity? {
        return getActivity()
    }

}

interface CustomerDrawerListener {
    fun onCustomerConfirmPressed(
        YBMFilterPos: Int,
        collectionStatusFilterPos: Int,
        selectedZone: Zone?
    )
}