package com.ybm100.app.crm.goodsmanagement.model

import com.xyy.utilslibrary.base.BaseModel
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.helper.RxHelper
import com.ybm100.app.crm.api.ApiService
import com.ybm100.app.crm.goods.api.GoodsApiService
import com.ybm100.app.crm.goodsmanagement.bean.EstimatedPriceListBean
import com.ybm100.app.crm.goodsmanagement.bean.GoodsManagementListBean
import com.ybm100.app.crm.goodsmanagement.bean.VarietyListBean
import com.ybm100.app.crm.goodsmanagement.contract.GoodsManagementContract
import com.ybm100.app.crm.net.RetrofitCreateHelper
import com.ybm100.app.crm.task.bean.ShareConfirm
import io.reactivex.Observable
import java.util.*

class GoodsManagementModel(private val isCustomerGoodsManagement: Boolean) : BaseModel(), GoodsManagementContract.IGoodsManagementModel {

    override fun getGoodsManagementGoodsList(queryMap: Map<String, String>): Observable<RequestBaseBean<GoodsManagementListBean?>?> {
        /**
         * 发现、客户 - 商品管理
         */
        return if (isCustomerGoodsManagement) {
            RetrofitCreateHelper.createApi(ApiService::class.java).getCustomerGoodsManagementGoodsList(queryMap as HashMap<String, String>?)
                    .compose(RxHelper.rxSchedulerHelper<RequestBaseBean<GoodsManagementListBean?>?>())
        } else {
            RetrofitCreateHelper.createApi(ApiService::class.java).getGoodsManagementGoodsList(queryMap as HashMap<String, String>?)
                    .compose(RxHelper.rxSchedulerHelper<RequestBaseBean<GoodsManagementListBean?>?>())
        }

    }

    override fun getVarietyList(branchCode: String): Observable<RequestBaseBean<VarietyListBean?>?> {
        return RetrofitCreateHelper.createApi(ApiService::class.java).getVarietyList(branchCode)
                .compose(RxHelper.rxSchedulerHelper<RequestBaseBean<VarietyListBean?>?>())
    }

    override fun collect(queryMap: Map<String, String>): Observable<RequestBaseBean<Any?>?> {
        return RetrofitCreateHelper.createApi(GoodsApiService::class.java).skuCollection(queryMap as HashMap<String, String>)
                .compose(RxHelper.rxSchedulerHelper<RequestBaseBean<Any?>?>())
    }

    override fun requestShareConfirm(queryMap: Map<String, String>): Observable<RequestBaseBean<ShareConfirm?>?> {
        return RetrofitCreateHelper.createApi(ApiService::class.java).shareConfirm(queryMap as HashMap<String, String>?)
                .compose(RxHelper.rxSchedulerHelper<RequestBaseBean<ShareConfirm?>?>())
    }

    override fun getEstimatedPrices(queryMap: Map<String, String>): Observable<RequestBaseBean<EstimatedPriceListBean?>?> {
        return RetrofitCreateHelper.createApi(ApiService::class.java).getEstimatedPrices(queryMap as HashMap<String, String>?)
                .compose(RxHelper.rxSchedulerHelper<RequestBaseBean<EstimatedPriceListBean?>?>())
    }
}