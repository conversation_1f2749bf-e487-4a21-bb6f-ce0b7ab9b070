package com.ybm100.app.crm.presenter.drugstore;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.CustomerPublicBean;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.RegisterParams;
import com.ybm100.app.crm.contract.drugstore.CustomerPublicContract;
import com.ybm100.app.crm.model.drugstore.PublicCustomerModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * Created by XyyMvpSportTemplate on 12/29/2018 16:48
 * 公海客户
 */
public class PublicCustomerPresenter extends BasePresenter<CustomerPublicContract.ICustomerPublicModel, CustomerPublicContract.ICustomerPublicView> {
    int pageSize = 10;
    int pageNo = 0;

    public static PublicCustomerPresenter newInstance() {
        return new PublicCustomerPresenter();
    }

    @Override
    protected PublicCustomerModel getModel() {
        return PublicCustomerModel.newInstance();
    }

    /**
     * 获取注册状态筛选参数
     */
    public void getRegisterParams() {
        mRxManager.register(mIModel.getRegisterParams()
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean<List<RegisterParams>>>(mIView) {
                    @Override
                    public void onSuccess(RequestBaseBean<List<RegisterParams>> resultBean) {
                        if (mIView == null || resultBean.getData() == null) {
                            return;
                        }
                        mIView.getRegisterParamsSuccess(resultBean.getData());
                    }

                    @Override
                    public void onFailure(int errorCode) {
                        super.onFailure(errorCode);
                    }
                }, new SimpleErrorConsumer(mIView) {
                    @Override
                    protected void onError(Throwable throwable, String msg) {
                        super.onError(throwable, msg);
                    }
                }));
    }

    /**
     * 获取未认领药店列表
     */
    public void searchOpenSea(final boolean refresh, HashMap<String, String> map) {
        if (mIView == null || mIModel == null) return;
        if (refresh) {
            pageNo = 0;
            mIView.enableLoadMore(true);
        }
        if (map == null) {
            map = new HashMap<>();
        }
        map.put("offset", String.valueOf(pageNo));
        map.put("limit", String.valueOf(pageSize));
        mRxManager.register(mIModel.searchOpenSea(map)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean<CustomerPublicBean>>(mIView, true) {
                    @Override
                    public void onSuccess(RequestBaseBean<CustomerPublicBean> listRequestBaseBean) {
                        if (mIView == null) return;
                        if (listRequestBaseBean.getData() == null || listRequestBaseBean.getData().getRows() == null) {
                            mIView.showEmpty();
                            return;
                        }
                        List<CustomerPublicBean.RowBean> result = listRequestBaseBean.getData().getRows();
                        if (result != null) {
                            if (listRequestBaseBean.getData().isLastPage()) {
                                mIView.loadMoreComplete();//超出一页没有更多的数据
                            }
                            mIView.searchOpenSeaSuccess(refresh, listRequestBaseBean);
                        }
                        pageNo++;
                    }

                    @Override
                    public void onFailure(int errorCode) {
                        super.onFailure(errorCode);
                        if (mIView == null) return;
                        mIView.showNetError();
                    }
                }, new SimpleErrorConsumer(mIView) {
                    @Override
                    protected void onError(Throwable throwable, String msg) {
                        super.onError(throwable, msg);
                        if (mIView == null) return;
                        mIView.showNetError();
                    }
                }));
    }

    /**
     * 认领药店
     */
    public void receive(String merchantId, String skuCollectCode) {
        if (mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.receive(merchantId, skuCollectCode)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean>(mIView, "") {
                    @Override
                    public void accept(RequestBaseBean baseBean) throws Exception {
                        if (mIView == null) return;
                        mIView.hideWaitDialog();
                        if (baseBean.isSuccess()) {
                            mIView.receiveSuccess(baseBean, merchantId, skuCollectCode);
                        } else if (!baseBean.isSuccess() && baseBean.getCode() == 405) {
                            mIView.receiveSuccess(baseBean, merchantId, skuCollectCode);
                        } else {
                            mIView.showToast(baseBean.getErrorMsg());
                        }
                    }

                    @Override
                    public void onSuccess(RequestBaseBean listRequestBaseBean) {
                    }
                }, new SimpleErrorConsumer(mIView)));
    }

}
