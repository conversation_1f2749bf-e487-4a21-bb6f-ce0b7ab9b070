package com.ybm100.app.crm.constant;

/**
 * Created by zcj on 2018/12/20 17
 */
public class DrugstoreConstants {

    /**
     * fragment_tab
     */
    //我的药店
    public static final int TAB_PUBLIC = 1;
    //未认领药店
    public static final int TAB_PRIVATE = 2;

    /**
     * intent_key
     */
    public static final String INTENT_KEY_TYPE = "type";
    //纬度
    public static final String INTENT_KEY_LAT = "lat";
    //经度
    public static final String INTENT_KEY_LNG = "lng";
    //位置信息
    public static final String INTENT_KEY_POI_INFO = "poi_info";
    //街道
    public static final String INTENT_KEY_STREET = "street";
    //药店id
    public static final String INTENT_KEY_MERCHANTID = "merchantId";
    //门店id
    public static final String INTENT_KEY_POI_ID = "poiId";
    //荷叶门店id
    public static final String INTENT_KEY_HY_ID = "hyId";
    //是否能修改地图
    public static final String INTENT_KEY_CHANGE = "change";
    //是否修改POI地址
    public static final String INTENT_KEY_CHANGE_POI = "change_poi";
    //拜访id
    public static final String INTENT_KEY_VISITID = "visitId";
    //公海ID
    public static final String INTENT_KEY_OPENSEAID = "openSeaId";
    //药店名称
    public static final String INTENT_KEY_DRUGSTORE_NAME = "drugstore_name";
    //私海线索是否可分配
    public static final String INTENT_KEY_DRUGSTORE_DISTRIBUTABLE = "distributable";
    public static final String INTENT_KEY_DRUGSTORE_REGISTER = "register";
    //药店地址
    public static final String INTENT_KEY_DRUGSTORE_ADDRESS = "drugstore_address";
    //药店详细地址
    public static final String INTENT_KEY_DRUGSTORE_DETAIL_ADDRESS = "drugstore_detail_address";
    //地址实体类
    public static final String INTENT_KEY_ADDRESS_INFO = "addressInfo";
    //是否展示搜索
    public static final String INTENT_KEY_SHOW_DATA = "show_search";
    //是否展示路线
    public static final String INTENT_KEY_SHOW_WAY = "show_way";
    //搜索关键字
    public static final String INTENT_KEY_SEARCH_KEY = "search_key";
    //线索信息
    public static final String INTENT_KEY_CLUE_INFO = "clue_info";
    //新建线索model
    public static final String INTENT_KEY_NEW_CLUE_MODEL = "new_clue_model";
    //药店信息
    public static final String INTENT_KEY_DRUGSTORE_INFO = "drugstore_info";
    //药店信息保存成功
    public static final String INTENT_ACTION_LOCATION_SAVE_SUCC = "intent_action_location_save_succ";

    //荷叶健康页面
    public static final String INTENT_ACTION_FROM_HY = "intent_action_from_hy";

    //跳转药店筛选
    public static final int REQUEST_FILTER = 101;
    //未认领筛选
    public static final int REQUEST_FILTER_UNCLAIMED = 1011;
    //跳转释放原因
    public static final int REQUEST_RELEASE_REASON = 102;
    //修改定位
    public static final int REQUEST_CHANGE_MAP = 103;
    //药店搜索
    public static final int REQUEST_SEARCH = 104;
    //药店详情
    public static final int REQUEST_DETAIL_INFO = 105;
    //区域选择
    public static final int REQUEST_SELECT_CITY = 106;
    //药店搜索
    public static final int REQUEST_SEARCH_LZ = 107;
    //药店搜索荷叶健康
    public static final int REQUEST_SEARCH_HY = 108;
    /**
     * 我的药店筛选条件
     */
    //全部
    public static final String BUTTON_TYPE_ALL = "0";
    //未激活药店
    public static final String BUTTON_TYPE_NOT_ACTIVE = "2";
    //未下首单
    public static final String BUTTON_TYPE_NONE_FIRST = "5";
    //90天未下单
    public static final String BUTTON_TYPE_NONE_BUY = "6";
    //自定义检索
    public static final String BUTTON_TYPE_SEARCH = "4";

    /**
     * 公海私海线索状态
     */
    public static final String STATUS_UNCLAIM = "0";//未认领
    public static final String STATUS_CLAIM = "1";//被认领
    public static final String STATUS_ALL = "2";//全部数据

    public static int NEW_CLUE_TYPE_VISIT = 0;//陌生拜访

    public static int NEW_CLUE_TYPE_DRUGSTORE = 1;//药店新建线索

    public static int type = TAB_PRIVATE;
    public static int select = 0;

    public static int ELECTRONIC_NOT_SIGNED = 0;//未签署
    public static int ELECTRONIC_SIGNED = 1;//已签署

    /**
     * 通知栏信息类型
     */
    public static final int NOTIFICATION_IN_PERIOD = 1;//资质临期
    public static final int NOTIFICATION_OVERDUE = 2;//资质过期
}
