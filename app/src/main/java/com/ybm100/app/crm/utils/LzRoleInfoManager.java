package com.ybm100.app.crm.utils;

import android.content.SharedPreferences;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ybm100.app.crm.bean.lzcustomer.LzRoleBean;
import com.ybm100.app.crm.constant.LzRoleConstant;

import java.util.ArrayList;
import java.util.List;

public enum LzRoleInfoManager {
    INSTANCE;
    private List<LzRoleBean> lzRoleBeans;


    LzRoleInfoManager() {
        SharedPrefManager.getInstance().addLogoutCallback(() -> setRoleInfo(null));
    }


    public void setRoleInfo(List<LzRoleBean> roleBeans) {
        SharedPreferences.Editor editor;
        if (roleBeans == null || roleBeans.size() == 0) {
            editor = SharedPrefManager.getInstance().setString("lzRoleBeans", "");
            setCurrRoleType(null);
        } else {
            editor = SharedPrefManager.getInstance().setString("lzRoleBeans", new Gson().toJson(roleBeans));
            setCurrRoleType(roleBeans.get(0));
        }
        editor.apply();
        lzRoleBeans = roleBeans;
    }

    public void setCurrRoleType(LzRoleBean lzRoleBean) {
        SharedPrefManager.getInstance().setString("roleType", new Gson().toJson(lzRoleBean)).apply();
    }

//    public boolean isLzType() {
//        String roleTypeJson = SharedPrefManager.getInstance().getString("roleType", "");
//        if (TextUtils.isEmpty(roleTypeJson)) {
//            return false;
//        }
//        LzRoleBean lzRoleBean = new Gson().fromJson(roleTypeJson, LzRoleBean.class);
//        if (lzRoleBean == null) {
//            return false;
//        } else {
//            return LzRoleConstant.ROLE_TYPE_LZ == lzRoleBean.getRoleCode();
//        }
//    }
    public int getRoleType(){
        String roleTypeJson = SharedPrefManager.getInstance().getString("roleType", "");
        if (TextUtils.isEmpty(roleTypeJson)) {
            return 0;
        }
        LzRoleBean lzRoleBean = new Gson().fromJson(roleTypeJson, LzRoleBean.class);
        if (lzRoleBean == null) {
            return 0;
        } else {
            return lzRoleBean.getRoleCode();
        }
    }

    public LzRoleBean getCurrRoleBean() {
        String roleTypeJson = SharedPrefManager.getInstance().getString("roleType", "");
        if (TextUtils.isEmpty(roleTypeJson)) {
            return new LzRoleBean();
        }
        LzRoleBean lzRoleBean = new Gson().fromJson(roleTypeJson, LzRoleBean.class);
        if (lzRoleBean == null) {
            return new LzRoleBean();
        } else {
            return lzRoleBean;
        }
    }

    public boolean isOnlyLzType() {
        return getLzRoleBeans().size() == 1 && getLzRoleBeans().get(0).getRoleCode() == LzRoleConstant.ROLE_TYPE_LZ;
    }
    public boolean isOnlyHyType() {
        return getLzRoleBeans().size() == 1 && getLzRoleBeans().get(0).getRoleCode() == LzRoleConstant.ROLE_TYPE_HY;
    }

    public List<LzRoleBean> getLzRoleBeans() {
        if (lzRoleBeans == null) {
            String roleBeanJson = SharedPrefManager.getInstance().getString("lzRoleBeans", "");
            if (TextUtils.isEmpty(roleBeanJson)) {
                return new ArrayList<>();
            }
            lzRoleBeans = new Gson().fromJson(roleBeanJson, new TypeToken<List<LzRoleBean>>() {
            }.getType());
        }
        return lzRoleBeans;
    }
    //获取本地角色权限jsonString
    public String getRoleBeansJson() {
        return SharedPrefManager.getInstance().getString("lzRoleBeans", "");
    }

}
