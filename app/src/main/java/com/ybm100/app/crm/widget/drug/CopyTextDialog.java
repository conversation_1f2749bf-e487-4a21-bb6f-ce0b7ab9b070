package com.ybm100.app.crm.widget.drug;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.xyy.common.util.ToastUtils;
import com.ybm100.app.crm.R;

/**
 * 文本复制弹框
 */
public class CopyTextDialog extends TransparentDialog {

    private String text;
    private final TextView content;

    public CopyTextDialog(@NonNull final Context context) {
        super(context);
        content = findViewById(R.id.content);
        findViewById(R.id.copy).setOnClickListener(v -> {
            ClipboardManager cb = (ClipboardManager) context.getSystemService(Context.CLIPBOARD_SERVICE);
            if (cb != null) {
                cb.setPrimaryClip(ClipData.newPlainText(null, text));
                ToastUtils.showShort("复制成功");
                dismiss();
            }
        });
        findViewById(R.id.cancel).setOnClickListener(v -> dismiss());
    }

    public void setText(String text) {
        this.text = text;
        content.setText(text);
    }

    @Override
    public int getLayoutId() {
        return R.layout.dialog_copy_text;
    }

}
