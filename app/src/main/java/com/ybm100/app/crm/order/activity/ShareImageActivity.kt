package com.ybm100.app.crm.order.activity

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import androidx.core.content.ContextCompat
import android.view.View
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.umeng.socialize.UMShareAPI
import com.xyy.common.util.ToastUtils
import com.xyy.utilslibrary.base.activity.BaseCompatActivity
import com.xyy.utilslibrary.utils.AppUtils
import com.ybm100.app.crm.R
import com.ybm100.app.crm.utils.ShareHelper
import kotlinx.android.synthetic.main.activity_share_img.*
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException


/**
 * @author: yuhaibo
 * @time: 2019/3/5 下午3:29.
 * projectName: XyyBeanSprouts.
 * Description:
 */
class ShareImageActivity : BaseCompatActivity(), View.OnClickListener {
    private val REQUEST_WRITE = 1001//申请权限的请求码
    private var targetSharePlatform: ShareHelper.Companion.SocialMedia? = null
    private var imgUrl: String? = null
    var mShareBitmap: Bitmap? = null

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.btn_cancel -> finish()
            R.id.btn_share_local -> checkPermission()
            else -> {
                when {
                    v?.id == R.id.btn_share_wechat -> targetSharePlatform = ShareHelper.Companion.SocialMedia.PLATFORM_WECHAT
                    v?.id == R.id.btn_share_wechat_moment -> targetSharePlatform = ShareHelper.Companion.SocialMedia.PLATFORM_WECHAT_CIRCLE
                }
                if (targetSharePlatform != null) {
                    ShareHelper.shareImage(this@ShareImageActivity, targetSharePlatform!!, mShareBitmap, mShareBitmap, shareCallback)
                }
                finish()
            }
        }


    }

    override fun getLayoutId(): Int {
        return R.layout.activity_share_img
    }

    override fun initView(savedInstanceState: Bundle?) {
        imgUrl = intent.getStringExtra(IMG_URL)
        //取消
        this.btn_cancel.setOnClickListener(this)
        //保存本地
        btn_share_local.setOnClickListener(this)
        btn_share_wechat.setOnClickListener(this)
        btn_share_wechat_moment.setOnClickListener(this)
        if (imgUrl != null) {
            Glide.with(AppUtils.getContext())
                    .asBitmap()   //强制转换Bitmap
                    .load(imgUrl)
                    .into(object : SimpleTarget<Bitmap>() {
                        override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                            mShareBitmap = resource
                            iv_share_img.setImageBitmap(resource)
                        }
                    })
        }

        root_constraintLayout.setOnClickListener {
            finish()
        }
        //手势处理
        iv_share_img.setOnTouchListener { _, _ ->
            return@setOnTouchListener true
        }
        //手势处理
        ll_pop_bottom.setOnTouchListener { _, _ ->
            return@setOnTouchListener true
        }

    }

    override fun isImmersionBarEnabled(): Boolean {
        return false
    }

    /**
     * 检查权限
     */
    private fun checkPermission() {
        //判断是否6.0以上的手机   不是就不用
        if (Build.VERSION.SDK_INT >= 23) {
            //判断是否有这个权限
            if (ContextCompat.checkSelfPermission(this,
                            Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                //2、申请权限: 参数二：权限的数组；参数三：请求码
                requestPermissions(arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE), REQUEST_WRITE)
            } else {
                saveImageToGallery(this, mShareBitmap)
            }
        } else {
            saveImageToGallery(this, mShareBitmap)
        }
    }

    //判断授权的方法  授权成功直接调用写入方法  这是监听的回调
    //参数  上下文   授权结果的数组   申请授权的数组
    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>,
                                            grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == REQUEST_WRITE && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
            saveImageToGallery(this, mShareBitmap)
        } else {
            ToastUtils.showShort("已拒绝SD卡读写操作，无法保存照片到本地")
        }
    }

    /**
     * 保存到本地相册
     * @param context
     * @param bmp
     */
    private fun saveImageToGallery(context: Context?, bmp: Bitmap?) {
        if (bmp == null) {
            ToastUtils.showShort("没有找到要保存的图片")
            return
        }
        val SAVE_PIC_PATH = if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED, ignoreCase = true)) {
            Environment.getExternalStorageDirectory().absolutePath
        } else
            "/mnt/sdcard"//保存到SD卡
        // 首先保存图片
        val appDir = File("$SAVE_PIC_PATH/豆芽/")
        if (!appDir.exists()) {
            appDir.mkdir()
        }
        val nowSystemTime = System.currentTimeMillis()
        val fileName = "$nowSystemTime.png"
        val file = File(appDir, fileName)
        try {
            if (!file.exists()) {
                file.createNewFile()
            }
            val fos = FileOutputStream(file)
            bmp.compress(Bitmap.CompressFormat.JPEG, 100, fos)
            fos.flush()
            fos.close()
        } catch (e: FileNotFoundException) {
            e.printStackTrace()
        } catch (e: IOException) {
            e.printStackTrace()
        }
        //保存图片后发送广播通知更新数据库
        val uri = Uri.fromFile(file)
        context!!.sendBroadcast(Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, uri))
        ToastUtils.showShort("已保存到本地相册")
        finish()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        UMShareAPI.get(this).onActivityResult(requestCode, resultCode, data)
    }

    companion object {
        var shareCallback: ShareHelper.ShareCallback? = null
        const val IMG_URL = "IMG_URL"
        fun startActivity(activity: Activity?, imgUrl: String?) {
            val intent = Intent(activity, ShareImageActivity::class.java)
            intent.putExtra(IMG_URL, imgUrl)
            activity?.startActivity(intent)
            activity?.overridePendingTransition(R.anim.abc_fade_in, R.anim.abc_fade_out)
        }
    }
}