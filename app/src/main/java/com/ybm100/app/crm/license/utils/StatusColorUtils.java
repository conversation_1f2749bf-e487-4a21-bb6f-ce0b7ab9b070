package com.ybm100.app.crm.license.utils;

import android.content.Context;
import androidx.core.content.ContextCompat;
import android.widget.TextView;

import com.ybm100.app.crm.R;

/**
 * @author: zcj
 * @time:2020/7/11. Description:
 */
public class StatusColorUtils {
    /**
     * @param status   {"code": 0,
     *                 "name": "草稿"
     *                 }, {
     *                 "code": 10,
     *                 "name": "一审不通过"
     *                 }, {
     *                 "code": 2,
     *                 "name": "审核中"
     *                 }, {
     *                 "code": 11,
     *                 "name": "一审通过"
     *                 }, {
     *                 "code": 21,
     *                 "name": "二审通过"
     *                 }, {
     *                 "code": 20,
     *                 "name": "二审不通过"
     *                 }, {
     *                 "code": 31,
     *                 "name": "三审通过"
     *                 }, {
     *                 "code": 30,
     *                 "name": "三审不通过"
     *                 }, {
     *                 "code": 40,
     *                 "name": "四审不通过"
     *                 }, {
     *                 "code": 3,
     *                 "name": "资质未回收"
     *                 }, {
     *                 "code": 4,
     *                 "name": "资质已回收"
     *                 }, {
     *                 "code": 5,
     *                 "name": "资质不合格"
     * @param mContext
     * @param tvStatus
     */
    public static void setStatusColor(int status, Context mContext, TextView tvStatus) {
        switch (status) {
            //审核中、一审通过、二审通过、三审通过
            case 2:
            case 11:
            case 21:
            case 31:
                tvStatus.setTextColor(ContextCompat.getColor(mContext, R.color.text_color_35C561));
                break;
            //草稿、资质不合格、资质未回收、一审不通过、二审不通过、三审不通过、四审不通过
            case 0:
            case 3:
            case 5:
            case 10:
            case 20:
            case 30:
            case 40:
                tvStatus.setTextColor(ContextCompat.getColor(mContext, R.color.text_color_FE3D3D));
                break;
            //资质已回收
            default:
                tvStatus.setTextColor(ContextCompat.getColor(mContext, R.color.text_color_8E8E93));
                break;
        }
    }
}
