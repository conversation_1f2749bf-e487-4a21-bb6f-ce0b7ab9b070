package com.ybm100.app.crm.model.drugstore;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiDrugstoreService;
import com.ybm100.app.crm.bean.drugstore.CustomerPublicBean;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.RegisterParams;
import com.ybm100.app.crm.contract.drugstore.CustomerPublicContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import java.util.HashMap;
import java.util.List;

import io.reactivex.Observable;


public class PublicCustomerModel extends BaseModel implements CustomerPublicContract.ICustomerPublicModel {

    public static PublicCustomerModel newInstance() {
        return new PublicCustomerModel();
    }


    @Override
    public Observable<RequestBaseBean<CustomerPublicBean>> searchOpenSea(HashMap<String, String> map) {
        return RetrofitCreateHelper.createApi(ApiDrugstoreService.class).searchOpenSea(map)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean> receive(String id,String skuCollectCode) {
        return RetrofitCreateHelper.createApi(ApiDrugstoreService.class).receive(id,skuCollectCode)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean<List<RegisterParams>>> getRegisterParams() {
        return RetrofitCreateHelper.createApi(ApiDrugstoreService.class).getRegisterParams()
                .compose(RxHelper.rxSchedulerHelper());
    }


}