package com.ybm100.app.crm.presenter;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiService;
import com.ybm100.app.crm.contract.BaseExecutorContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;
import com.ybm100.app.crm.net.exception.DataEmptyException;
import com.ybm100.app.crm.order.bean.DeptHaveAdminBean;
import com.ybm100.app.crm.task.bean.ExecutorLevelItem;

import io.reactivex.annotations.NonNull;
import io.reactivex.functions.Consumer;
import io.reactivex.functions.Function;

/**
 * Created by dengmingjia on 2019/1/4
 */
public abstract class BaseExecutorPresenter<T> extends BasePresenter<BaseExecutorContract.IModel, BaseExecutorContract.IView> {

    public void getData(boolean showParent) {
        mRxManager.register(mIModel.getData()
                .map(new Function<RequestBaseBean<T>, ExecutorLevelItem>() {
                    @Override
                    public ExecutorLevelItem apply(RequestBaseBean<T> t) throws Exception {
                        if (t.isSuccess()) {
                            if (t.getData() == null) {
                                throw new DataEmptyException();
                            } else {
                                ExecutorLevelItem item = getLevelItem(t.getData(), null, showParent);
                                setPersonTotalCount(item);
                                return item;
                            }
                        } else {
                            throw new Exception(t.getErrorMsg());
                        }
                    }

                })
                .compose(RxHelper.<ExecutorLevelItem>rxSchedulerHelper())
                .subscribe(new Consumer<ExecutorLevelItem>() {
                    @Override
                    public void accept(ExecutorLevelItem executorLevelItem) throws Exception {
                        if (mIView == null) return;
                        mIView.onGetSuccess(executorLevelItem);
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        if (mIView == null) return;
                        if (throwable instanceof DataEmptyException) {
                            mIView.onGetSuccess(null);
                        } else {
                            mIView.onGetFail(throwable.getMessage());
                        }
                    }
                }));
    }

    public void isThisDeptHaveAdmin(final ExecutorLevelItem item) {
        if (null == item) {
            return;
        }

        mRxManager.register(RetrofitCreateHelper.createApi(ApiService.class)
                .isThisDeptHaveAdmin(item.getId())
                .map(new Function<RequestBaseBean<DeptHaveAdminBean>, DeptHaveAdminBean>() {
                    @Override
                    public DeptHaveAdminBean apply(@NonNull RequestBaseBean<DeptHaveAdminBean> requestBaseBean) throws Exception {
                        if (requestBaseBean.isSuccess()) {
                            if (requestBaseBean.getData() == null) {
                                throw new DataEmptyException();
                            } else {
                                return requestBaseBean.getData();
                            }
                        } else {
                            throw new Exception(requestBaseBean.getErrorMsg());
                        }
                    }
                })
                .compose(RxHelper.rxSchedulerHelper())
                .subscribe(new Consumer<DeptHaveAdminBean>() {
                    @Override
                    public void accept(DeptHaveAdminBean deptHaveAdminBean) throws Exception {
                        if (mIView != null) {
                            mIView.onGetDeptSuccess(item, deptHaveAdminBean);
                        }
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        if (mIView != null) {
                            String message = throwable.getMessage();
                            if (throwable instanceof DataEmptyException) {
                                message = "返回数据为空";
                            }
                            mIView.onGetDeptFail(message);
                        }
                    }
                }));
    }

    protected abstract ExecutorLevelItem getLevelItem(T data, ExecutorLevelItem parent, boolean showParent);


    private int setPersonTotalCount(ExecutorLevelItem item) {
        if (item.getChild() == null || item.getChild().size() == 0) {
            item.setTotalPersonCount(0);
            return 0;
        }
        int count = 0;
        for (int i = 0; i < item.getChild().size(); i++) {
            ExecutorLevelItem child = item.getChild().get(i);
            if (child.isPerson()) {
                count++;
            } else {
                count = count + setPersonTotalCount(child);
            }

        }
        item.setTotalPersonCount(count);
        return count;
    }

    public void getUserLevel(String oaId) {
        mRxManager.register(
                mIModel.getUserLevel(oaId)
                        .map((Function<RequestBaseBean<Boolean>, Boolean>) userLevelBeanRequest -> {
                            if (userLevelBeanRequest.isSuccess()) {
                                if (userLevelBeanRequest.getData() == null) {
                                    throw new DataEmptyException();
                                } else {
                                    return userLevelBeanRequest.getData();
                                }
                            }
                            throw new Exception(userLevelBeanRequest.getErrorMsg());
                        })
                        .compose(RxHelper.rxSchedulerHelper())
                        .subscribe(new Consumer<Boolean>() {

                            @Override
                            public void accept(Boolean userLevelBean) throws Exception {
                                mIView.onGetUserLevelSuccess(userLevelBean);
                            }
                        }, new Consumer<Throwable>() {
                            @Override
                            public void accept(Throwable throwable) throws Exception {
                                mIView.onGetUserLevelFail(throwable.getMessage());
                            }
                        })
        );
    }
}
