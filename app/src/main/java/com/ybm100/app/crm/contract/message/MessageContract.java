package com.ybm100.app.crm.contract.message;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.message.MessageApiBean;
import com.ybm100.app.crm.bean.message.MessageReadCountBean;
import com.ybm100.app.crm.bean.message.MessageType;
import com.ybm100.app.crm.bean.message.SessionMSGBean;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/21/2018 17:25
 */
public interface MessageContract {

    interface IMessageView extends IBaseActivity {

        void renderCount(MessageReadCountBean bean);

        void getAllMessageTypeSuccess(RequestBaseBean<MessageType> bean);
    }

    interface IMessageModel extends IBaseModel {
        Observable<RequestBaseBean<MessageApiBean>> getMsg(int type, int pageNo, int pageSize);

        Observable<MessageReadCountBean> getMsgCount();

        Observable<RequestBaseBean<MessageType>> getAllMessageType();

        Observable<RequestBaseBean> read(int type,long id);
    }

    interface ISessionMessageView extends IBaseActivity {
        void onSessionMSGListBack(int pageNo, RequestBaseBean<SessionMSGBean> bean);
    }

    interface ISessionMessageModel extends IBaseModel {
        Observable<RequestBaseBean<SessionMSGBean>> getSessionMSGList(int pageNo, int limit);
    }
}
