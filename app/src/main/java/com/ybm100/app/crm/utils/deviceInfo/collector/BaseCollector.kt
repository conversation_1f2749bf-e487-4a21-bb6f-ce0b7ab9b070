package com.ybm100.app.crm.utils.deviceInfo.collector

import android.content.Context
import android.util.Log

abstract class BaseCollector {
    fun collect(context: Context): String {
        val result = try {
            internalCollect(context) ?: "empty"
        } catch (e: Exception) {
            e.message ?: "errEmpty"
        }
        Log.d("collector", "${javaClass.simpleName}:$result")
        return result
    }

    protected abstract fun internalCollect(context: Context): String?
}