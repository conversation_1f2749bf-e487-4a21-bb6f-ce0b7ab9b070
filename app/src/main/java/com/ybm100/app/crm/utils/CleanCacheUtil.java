package com.ybm100.app.crm.utils;

import android.content.Context;
import android.os.Environment;
import android.webkit.CookieManager;
import android.webkit.ValueCallback;
import android.webkit.WebStorage;

import com.xyy.common.util.ToastUtils;
import com.ybm100.app.crm.net.interceptor.ThrottleInterceptor;

import java.io.File;

public class CleanCacheUtil {


    public static void cleanCacheDataForApp(Context context) {
        try {
            ThrottleInterceptor.clearCache(context);
            cleanInternalCache(context);
            cleanExternalCache(context);
            cleanDatabases(context);
            cleanFiles(context);
            cleanWebCache(context);
        } catch (Exception ignore) {
            ToastUtils.showShort("清除缓存失败");
        } finally {
            ToastUtils.showShort("清除缓存完成");
        }
    }

    /**
     * 删除方法 这里只会删除某个文件夹下的文件，如果传入的directory是个文件，将不做处理
     */
    private static void deleteFilesByDirectory(File directory) {
        deleteFilesByDirectory(directory, null);
    }

    /**
     * 重载，排除特殊文件不删除
     */
    private static void deleteFilesByDirectory(File directory, String extFileName) {
        if (directory != null && directory.exists() && directory.isDirectory()) {
            for (File item : directory.listFiles()) {
                if (extFileName != null && extFileName.equals(item.getName())) {
                    continue;
                }
                if (item.isDirectory()) {
                    deleteFilesByDirectory(item);
                } else {
                    item.delete();
                }
            }
        }
    }

    /**
     * 清除webview相关数据,cookie localstroage
     */
    private static void cleanWebCache(Context context) {
        WebStorage.getInstance().deleteAllData();
        CookieManager cookieManager = CookieManager.getInstance();
        cookieManager.removeAllCookies(new ValueCallback<Boolean>() {
            @Override
            public void onReceiveValue(Boolean value) {
                //donothing
            }
        });
    }

    /**
     * 清除内部缓存，/data/data/com.xxx.xxx/cache
     */
    private static void cleanInternalCache(Context context) {
        deleteFilesByDirectory(context.getCacheDir());
    }

    /**
     * 清除外部cache下的内容，/mnt/sdcard/android/data/com.xxx.xxx/cache
     */
    private static void cleanExternalCache(Context context) {
        if (Environment.getExternalStorageState().equals(
                Environment.MEDIA_MOUNTED)) {
            deleteFilesByDirectory(context.getExternalCacheDir());
        }
    }

    /**
     * 清除所有数据库，/data/data/com.xxx.xxx/databases
     */
    private static void cleanDatabases(Context context) {
        deleteFilesByDirectory(new File("/data/data/"
                + context.getPackageName() + "/databases"));
    }

    /**
     * 清除SharedPreference，/data/data/com.xxx.xxx/shared_prefs
     */
    private static void cleanSharedPreference(Context context) {
        deleteFilesByDirectory(new File("/data/data/"
                + context.getPackageName() + "/shared_prefs"), "account_manager_sp.xml");
    }


    /**
     * 清除/data/data/com.xxx.xxx/files下的内容
     */
    private static void cleanFiles(Context context) {
        deleteFilesByDirectory(context.getFilesDir());
    }
}
