package com.ybm100.app.crm.presenter.drugstore.minedrug;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.DrugstoreUpdateLogBean;
import com.ybm100.app.crm.contract.drugstore.minedrug.InformationChangelogContract;
import com.ybm100.app.crm.model.drugstore.minedrug.InformationChangelogModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:15
 */
public class InformationChangelogPresenter extends BasePresenter<InformationChangelogContract.IInformationChangelogModel, InformationChangelogContract.IInformationChangelogView> {

    public static InformationChangelogPresenter newInstance() {
        return new InformationChangelogPresenter();
    }

    @Override
    protected InformationChangelogModel getModel() {
        return InformationChangelogModel.newInstance();
    }

    /**
     * 药店更新日志
     *
     * @param limit
     * @param merchantId
     * @param offset
     */
    public void getInfoChangeLogData(int limit,
                                     String merchantId,
                                     int offset) {
        if (mIView == null || mIModel == null) return;

        mRxManager.register(mIModel.getInfoChangeLogData(limit, merchantId, offset).subscribe(new SimpleSuccessConsumer<RequestBaseBean<DrugstoreUpdateLogBean>>(mIView) {
            @Override
            public void onSuccess(RequestBaseBean<DrugstoreUpdateLogBean> drugstoreInfoBean) {

                mIView.getInfoChangeLogDataSuccess(drugstoreInfoBean);
            }

            @Override
            public void onFailure(int errorCode) {
                super.onFailure(errorCode);
                if (mIView == null) return;
                mIView.showNetError();
            }
        }, new SimpleErrorConsumer(mIView) {
            @Override
            protected void onError(Throwable throwable, String msg) {
                super.onError(throwable, msg);
                if (mIView == null) return;
                mIView.showNetError();
            }
        }));

    }

}
