package com.ybm100.app.crm.model;

import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiService;
import com.ybm100.app.crm.bean.message.MessageReadCountBean;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import io.reactivex.Observable;

/**
 * <AUTHOR>
 * @version 1.0
 * @file CommonModel.java
 * @brief
 * @date 2018/12/29
 * Copyright (c) 2018, 北京小药药
 * All rights reserved.
 */
public class CommonModel {

    public Observable<MessageReadCountBean> getMsgCount() {
        return RetrofitCreateHelper.createApi(ApiService.class).getMsgUnreadCount()
                .compose(RxHelper.rxSchedulerHelper());
    }
}
