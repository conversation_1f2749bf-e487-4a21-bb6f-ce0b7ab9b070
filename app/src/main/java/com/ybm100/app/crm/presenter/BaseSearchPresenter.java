package com.ybm100.app.crm.presenter;

import android.os.Bundle;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.ISearch;
import com.ybm100.app.crm.task.bean.PagingApiBean;
import com.ybm100.app.crm.contract.BaseSearchContract;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;
import com.ybm100.app.crm.utils.PagingHelper;

import io.reactivex.disposables.Disposable;

/**
 * Created by dengmingjia on 2019/1/4
 */
public abstract class BaseSearchPresenter<T extends ISearch> extends BasePresenter<BaseSearchContract.IModel, BaseSearchContract.IView> {
    private static final int PAGE_SIZE = 20;
    Disposable mSearchDisposable;
    private final PagingHelper mPagerHelper = new PagingHelper(PAGE_SIZE);
    private String mKey = "";

    public void searchExecutor(final String searchKey, int offset) {
        if (mSearchDisposable != null) {
            mSearchDisposable.dispose();
            mSearchDisposable = null;
        }
        //206版本进行优化，新增apiVersion
        mRxManager.register(mIModel.search("206", searchKey, mPagerHelper.getLimit(), offset)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean<PagingApiBean<T>>>(mIView) {
                    @Override
                    public void onSuccess(RequestBaseBean<PagingApiBean<T>> listRequestBaseBean) {
                        mKey = searchKey;
                        int currentOffset = listRequestBaseBean.getData().getOffset();
                        mPagerHelper.updateOffset(currentOffset);
                        mIView.onSearchSuccess(listRequestBaseBean.getData().getRows(), currentOffset > 1);
                        mIView.setHasMore(!listRequestBaseBean.getData().isLastPage());
                    }

                }, new SimpleErrorConsumer(mIView) {
                    @Override
                    protected void onError(Throwable throwable, String msg) {
                        super.onError(throwable, msg);
                        mKey = searchKey;
                        if (mIView != null) {
                            mIView.onSearchFail();
                        }
                    }
                }));
    }

    public void loadMore() {
        searchExecutor(mKey, mPagerHelper.getNextPageOffset());
    }

    public void retry() {
        searchExecutor(mKey, 1);
    }

    public void initBundle(Bundle bundle) {

    }
}
