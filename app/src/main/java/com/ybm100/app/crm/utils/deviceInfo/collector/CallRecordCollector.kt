package com.ybm100.app.crm.utils.deviceInfo.collector

import android.content.Context
import com.ybm100.app.crm.schedule.service.CallRecordManager
import java.lang.reflect.Field


class CallRecordCollector : BaseCollector() {

    override fun internalCollect(context: Context): String? {
        return try {
            CallRecordManager.getSP(context)?.all?.size.toString()
        } catch (e: Exception) {
            e.message
        }
    }

}
