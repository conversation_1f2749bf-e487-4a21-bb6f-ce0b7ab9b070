package com.ybm100.app.crm.contract.lzcustomer;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.lzcustomer.LzPrivateDetailBean;

import io.reactivex.Observable;

/**
 * <AUTHOR>
 * @date 2019/1/7
 */
public interface LzPrivateDetailContract {

    interface ILzPrivateDetailModel extends IBaseModel {
        Observable<RequestBaseBean<LzPrivateDetailBean>> privateSeaCustomerDetail(String id);

        Observable<RequestBaseBean> releaseCustomer(String id);
    }

    interface ILzPrivateDetailView extends IBaseActivity {

        void privateSeaDetailSuccess(LzPrivateDetailBean baseBean);

        void releaseCustomerSuccess(RequestBaseBean baseBean);
    }
}
