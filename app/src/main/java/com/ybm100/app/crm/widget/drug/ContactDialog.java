package com.ybm100.app.crm.widget.drug;

import android.app.Activity;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;

import com.xyy.common.widget.DefaultItemDecoration;
import com.xyy.utilslibrary.utils.DisplayUtils;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.drugstore.MerchantContact;
import com.ybm100.app.crm.ui.adapter.drugstore.ContactDialogListAdapter;
import com.ybm100.app.crm.utils.CallUtil;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-07-24
 */
public class ContactDialog extends TransparentDialog {

    public ContactDialog(@NonNull Activity context, List<MerchantContact> contactBeans, String id, String merchantName) {
        super(context);
        RecyclerView recyclerView = findViewById(R.id.recycler);
        findViewById(R.id.iv_close).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        recyclerView.setLayoutManager(new LinearLayoutManager(context));
        ContactDialogListAdapter contactListAdapter = new ContactDialogListAdapter(R.layout.item_contact_dialog, contactBeans);
        recyclerView.addItemDecoration(new DefaultItemDecoration(context, 1));
        recyclerView.setAdapter(contactListAdapter);
        contactListAdapter.setOnItemClickListener((adapter, view, position) -> {
            CallUtil.call(context, contactBeans.get(position).getContactMobile(), id, merchantName, "contact_dialog");
            dismiss();
        });

        WindowManager.LayoutParams lp = getWindow().getAttributes();
        if (contactBeans.size() > 0) {
            lp.height = DisplayUtils.dp2px(400);
        } else {
            lp.height = WindowManager.LayoutParams.WRAP_CONTENT;
        }
        lp.gravity = Gravity.BOTTOM;
        getWindow().setAttributes(lp);
    }

    @Override
    public int getLayoutId() {
        return R.layout.dialog_contact;
    }

}