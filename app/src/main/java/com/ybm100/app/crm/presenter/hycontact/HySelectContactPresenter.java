package com.ybm100.app.crm.presenter.hycontact;



import android.util.Log;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.schedule.ContactListBean;
import com.ybm100.app.crm.contract.schedule.SelectContactContract;
import com.ybm100.app.crm.model.hycontact.HySelectContactModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

import java.util.List;

/**
 * HySelectContactPresenter
 */
public class HySelectContactPresenter extends BasePresenter<SelectContactContract.ISelectContactModel, SelectContactContract.ISelectContactView> {

    public static HySelectContactPresenter newInstance() {
        return new HySelectContactPresenter();
    }

    @Override
    protected SelectContactContract.ISelectContactModel getModel() {
        return HySelectContactModel.newInstance();
    }

    public void getContactList(String merchantId) {
        if (mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.getContactList(merchantId).subscribe(new SimpleSuccessConsumer<RequestBaseBean<List<ContactListBean>>>(mIView, "") {
            @Override
            public void onSuccess(RequestBaseBean<List<ContactListBean>> bean) {
                if (mIView == null) return;
                Log.e("guan", "onSuccess :" + bean.getData().size());
                List<ContactListBean> list = bean.getData();
                ContactListBean contactListBean = new ContactListBean();
                contactListBean.setContactName("无效联系人拜访");
                contactListBean.setIsEffective(2);
                list.add(contactListBean);
                mIView.getContactListSuccess(list);
            }
        }, new SimpleErrorConsumer(mIView) {
            @Override
            protected void onError(Throwable throwable, String msg) {
                super.onError(throwable, msg);
                mIView.getContactListFailed();
            }
        }));
    }
}

