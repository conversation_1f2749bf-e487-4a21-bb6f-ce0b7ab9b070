package com.ybm100.app.crm.model.drugstore.minedrug;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.contract.drugstore.minedrug.ContactContract;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:06
 */
public class ContactModel extends BaseModel implements ContactContract.IContactModel {

    public static ContactModel newInstance() {
        return new ContactModel();
    }

    @Override
    public Observable<RequestBaseBean> deleteContactById(int id) {
        return null;
    }
}