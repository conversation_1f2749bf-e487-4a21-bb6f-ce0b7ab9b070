package com.ybm100.app.crm.presenter

import android.annotation.SuppressLint
import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.IBaseView
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.helper.RxHelper
import com.ybm100.app.crm.api.ApiService
import com.ybm100.app.crm.api.LZApiService
import com.ybm100.app.crm.bean.MineMenuBean
import com.ybm100.app.crm.bean.lzcustomer.LzRoleBean
import com.ybm100.app.crm.bean.update.VersionInfo
import com.ybm100.app.crm.net.RetrofitCreateHelper
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer
import com.ybm100.app.crm.utils.LzRoleInfoManager

class MineAndSettingPresenter : BasePresenter<Any, MineAndSettingPresenter.MineAndSetting>() {

    override fun getModel(): Any {
        return Any()
    }

    @SuppressLint("CheckResult")
    fun getMineMenu() {
        mRxManager.register(RetrofitCreateHelper.createApi(ApiService::class.java).getMineMenu(LzRoleInfoManager.INSTANCE.roleBeansJson)
                .compose(RxHelper.rxSchedulerHelper())
                .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<MineMenuBean?>?>(mIView, false) {
                    override fun onSuccess(t: RequestBaseBean<MineMenuBean?>?) {
                        (mIView as Mine).onGetMineMenuSuccess(t?.data)
                    }

                    override fun onFailure(errorCode: Int) {
                        super.onFailure(errorCode)
                        (mIView as Mine).onGetMineMenuFail()
                    }
                }, SimpleErrorConsumer(mIView)))
    }

    fun checkForUpdate() {
        mRxManager.register(RetrofitCreateHelper.createApi(ApiService::class.java).checkUpdate()
                .compose(RxHelper.rxSchedulerHelper())
                .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<VersionInfo>?>(mIView, "") {
                    override fun onSuccess(t: RequestBaseBean<VersionInfo>?) {
                        (mIView as Setting).onCheckForUpdateSuccess(t?.data)
                    }

                    override fun onFailure(errorCode: Int) {
                        super.onFailure(errorCode)
                        (mIView as Setting).onCheckForUpdateFail()
                    }
                }, SimpleErrorConsumer(mIView)))
    }

    fun getRoles() {
        mRxManager.register(RetrofitCreateHelper.createApi(LZApiService::class.java).roles
                .compose(RxHelper.rxSchedulerHelper())
                .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<List<LzRoleBean>?>>(mIView) {
            override fun onSuccess(bean: RequestBaseBean<List<LzRoleBean>?>) {
                (mIView as Mine).getRolesSuccess(bean.data)
            }

            override fun onFailure(errorCode: Int) {
                (mIView as Mine).getRolesFail()
            }
        }, object : SimpleErrorConsumer((mIView as Mine)) {
            override fun onError(throwable: Throwable, msg: String) {
                (mIView as Mine).getRolesFail()
            }
        }))
    }

    interface MineAndSetting : IBaseView

    interface Mine : MineAndSetting {
        fun onGetMineMenuSuccess(data: MineMenuBean?)
        fun onGetMineMenuFail()

        fun getRolesSuccess(roleBean: List<LzRoleBean>?)
        fun getRolesFail()
    }

    interface Setting : MineAndSetting {
        fun onCheckForUpdateSuccess(data: VersionInfo?)
        fun onCheckForUpdateFail()
    }
}