package com.ybm100.app.crm.contract.schedule;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.schedule.ScheduleDetailBean;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/22/2018 19:17
 */
public interface ScheduleDetailContract {

    interface IScheduleDetailModel extends IBaseModel {
        Observable<RequestBaseBean<ScheduleDetailBean>> getScheduleDetail(String scheduleId);
    }

    interface IScheduleDetailView extends IBaseActivity {
        void getScheduleDetailSuccess(RequestBaseBean<ScheduleDetailBean> baseBean);
    }

}
