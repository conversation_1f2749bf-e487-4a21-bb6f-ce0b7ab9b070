package com.ybm100.app.crm.contract.drugstore.minedrug;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:06
 *  联系人
 */
public interface ContactContract {

    interface IContactModel extends IBaseModel {
        Observable<RequestBaseBean> deleteContactById(int id);
    }

    interface IContactView extends IBaseActivity {
        void deleteContactByIdSuccess(RequestBaseBean baseBean);
    }

}
