package com.ybm100.app.crm.doraemon;

import android.content.Context;
import android.content.Intent;

import com.didichuxing.doraemonkit.kit.AbstractKit;
import com.didichuxing.doraemonkit.kit.Category;
import com.ybm100.app.crm.R;

/**
 * @author: zcj
 * @time:2021/3/26. Description:
 */
public class SimulatedLoginKit extends AbstractKit {
    @Override
    public int getCategory() {
        return Category.BIZ;
    }

    @Override
    public int getName() {
        return R.string.simulation_login;
    }

    @Override
    public int getIcon() {
        return R.drawable.platform_ic_login;
    }

    @Override
    public void onClick(Context context) {
        Intent intent = new Intent(context, SimulatedLoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    @Override
    public void onAppInit(Context context) {

    }
}
