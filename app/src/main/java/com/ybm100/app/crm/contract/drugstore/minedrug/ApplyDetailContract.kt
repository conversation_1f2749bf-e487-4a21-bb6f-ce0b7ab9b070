package com.ybm100.app.crm.contract.drugstore.minedrug

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.ybm100.app.crm.bean.drugstore.minedrugstore.ApplyDetailBean
import io.reactivex.Observable
import java.util.*

/**
 * Created by XyyMvpYkqTemplate on 07/30/2019 18:07
 */
interface ApplyDetailContract {

    interface IApplyDetailModel : IBaseModel {
        /**
         * 请求发票申请详情
         */
        fun reqInvoiceDetail(map: HashMap<String, Any>): Observable<RequestBaseBean<ApplyDetailBean>>
    }

    interface IApplyDetailView : IBaseActivity {
        fun reqInvoiceDetailSuccess(data: ApplyDetailBean?)
    }

}
