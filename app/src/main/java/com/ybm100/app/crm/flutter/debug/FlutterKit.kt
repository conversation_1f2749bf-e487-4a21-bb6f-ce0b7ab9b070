package com.ybm100.app.crm.flutter.debug

import android.content.Context
import com.didichuxing.doraemonkit.kit.AbstractKit
import com.xyy.flutter.container.container.ContainerRuntime
import com.ybm100.app.crm.R

class FlutterKit : AbstractKit() {
    override val icon: Int
        get() = R.mipmap.flutter_icon_red
    override val name: Int
        get() = R.string.flutter_kit_name

    override fun onAppInit(context: Context?) {
    }

    override fun onClick(context: Context?) {
        FlutterJumpActivity.startActivity(context)
    }
}