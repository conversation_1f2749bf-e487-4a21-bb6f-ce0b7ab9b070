package com.ybm100.app.crm.flutter.net

import android.content.Context
import com.google.gson.Gson
import com.xyy.flutter.container.container.bridge.callback.RequestCallback
import com.xyy.utilslibrary.base.IBaseView
import com.xyy.utilslibrary.base.activity.BaseCompatActivity
import com.xyy.utilslibrary.base.activity.BaseMVPCompatActivity
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.utilslibrary.utils.TimeUtils
import com.ybm100.app.crm.flutter.CustomFlutterActivity
import com.ybm100.app.crm.flutter.ErrorCode
import com.ybm100.app.crm.net.BaseUrl
import com.ybm100.app.crm.net.RetrofitCreateHelper
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer
import com.ybm100.app.crm.utils.GsonUtils
import com.ybm100.app.crm.utils.SharedPrefManager
import io.reactivex.Observable
import okhttp3.MediaType
import okhttp3.RequestBody

class NetworkHandler {

    fun handle(
        activity: Context,
        method: String,
        path: String,
        contentType: String,
        requestParams: Any?,
        headerMap: Map<String, String>,
        callback: RequestCallback
    ) {
        if (path.isEmpty()) {
            error(ErrorCode.INVALID_REQUEST_PATH)
            return
        }
        val fixPath = path.removePrefix("/")


        val flutterService = RetrofitCreateHelper.createApi(
            FlutterService::class.java,
            BaseUrl.getBaseUrl(),
            FlutterConverterFactory.create()
        )
        var observable: Observable<String?>? = null
        observable = when (method.toLowerCase()) {
            "post" -> {
                when (contentType.toLowerCase()) {
                    "form" -> {
                        flutterService.requestFormPostAsk(
                            HashMap(headerMap),
                            fixPath,
                            convertParamsToMap(requestParams)
                        )
                    }
                    "json" -> {
                        val completeParams = HashMap<String, Any?>()
                        var json: String? = null
                        if (requestParams is Map<*, *>) {
                            completeParams.putAll(requestParams as Map<String, Any?>)
                            if (!completeParams.containsKey("sysUserId")) {
                                completeParams.put(
                                    "sysUserId", SharedPrefManager.getInstance().userInfo?.sysUserId
                                        ?: ""
                                )
                            }
                            if (!completeParams.containsKey("timestamp")) {
                                completeParams.put("timestamp", TimeUtils.getNowMills().toString())
                            }
                            json = Gson().toJson(completeParams)
                        } else {
                            json = Gson().toJson(requestParams)
                        }
                        val body = RequestBody.create(
                            MediaType.parse("application/json; charset=utf-8"), json
                                ?: ""
                        )
                        flutterService.requestJsonPostAsk(HashMap(headerMap), path, body)
                    }
                    else -> {
                        error(ErrorCode.INVALID_CONTENT_TYPE)
                        return
                    }
                }
            }
            "get" -> {
                flutterService.requestGetAsk(
                    HashMap(headerMap),
                    fixPath,
                    convertParamsToMap(requestParams)
                )
            }
            else -> {
                error(ErrorCode.INVALID_METHOD)
                return
            }
        }
        if (activity is IBaseView) {
            val subscribe = observable
                .compose(RxHelper.rxSchedulerHelper())
                .subscribe(object : FlutterProxyConsumer(activity) {
                    override fun onSuccess(result: String?) {
                        if (result.isNullOrEmpty()) {
                            error(ErrorCode.RESPONSE_EMPTY)
                        } else {
                            callback.success(result)
                        }
                    }

                    override fun onFailure(baseBean: RequestBaseBean<*>?) {

                    }
                }, object : SimpleErrorConsumer(activity) {
                    override fun onError(throwable: Throwable?, msg: String?) {
                        callback.error(
                            ErrorCode.REQUEST_ERROR.errorCode, msg
                                ?: ErrorCode.REQUEST_ERROR.errorMsg
                        )
                    }
                })
            if (activity is CustomFlutterActivity) {
                activity.mRxManager.register(subscribe)
            }else if(activity is BaseMVPCompatActivity<*>){
                activity
            }
            // 这里可能有问题，客户信息没有绑定activity
        } else {
            callback.error(ErrorCode.ERROR_CONTAINER.errorCode, ErrorCode.ERROR_CONTAINER.errorMsg)
        }
    }

    private fun convertParamsToMap(requestParams: Any?): HashMap<String, String> {
        if (requestParams is String) {
            try {
                return HashMap(GsonUtils.toMap<String>(requestParams))
            } catch (e: Exception) {
            }
        } else if (requestParams is HashMap<*, *>) {
            return HashMap<String, String>().also { resultMap ->
                requestParams.forEach {
                    if (it.key != null && it.key is String && it.value != null) {
                        resultMap[it.key as String] = it.value.toString()
                    }
                }
            }
        }
        return hashMapOf()
    }


}