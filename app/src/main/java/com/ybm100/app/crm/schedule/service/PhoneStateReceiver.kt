package com.ybm100.app.crm.schedule.service

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Context.TELEPHONY_SERVICE
import android.content.Intent
import android.telephony.TelephonyManager
import android.util.Log


class PhoneStateReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        val tm = context.getSystemService(TELEPHONY_SERVICE) as TelephonyManager
        when (tm.callState) {
            TelephonyManager.CALL_STATE_IDLE->{
                //挂断电话，去处理残留的通话记录，非app出发的电话也有可能触发该方法
                Log.d("callRecord", "receiver:闲置")
                CallRecordManager.handleCallRecord("receive")
            }
            TelephonyManager.CALL_STATE_OFFHOOK->{
                //打电话中，没卵用
                Log.d("callRecord", "receiver:接通")
            }
        }
    }
}