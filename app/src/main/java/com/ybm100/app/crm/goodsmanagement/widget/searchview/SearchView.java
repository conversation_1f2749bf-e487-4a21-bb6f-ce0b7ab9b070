package com.ybm100.app.crm.goodsmanagement.widget.searchview;

import android.content.Context;
import android.content.res.TypedArray;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.xyy.common.util.KeyboardUtils;
import com.xyy.common.util.PreferencesUtil;
import com.xyy.common.util.StringUtils;
import com.ybm100.app.crm.R;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class SearchView extends LinearLayout {
    private static final String TAG = "SearchView";
    private final Context context;
    // 搜索框组件
    private EditText et_search; // 搜索按键
    private ImageView searchBack; // 返回按键
    private TextView searchBackRight; // 返回按键
    // 回调接口
    private ICallBack mCallBack;// 搜索按键回调接口
    private bCallBack bCallBack; // 返回按键回调接口
    private SearchEventCallBack bSearchEventCallBack; // 右边搜索按钮的点击事件

    // 自定义属性设置
    // 1. 搜索字体属性设置：大小、颜色 & 默认提示
    private int textSizeSearch;
    private int textColorSearch;
    private String textHintSearch;

    // 2. 搜索框设置：高度 & 颜色
    private int searchBlockColor;
    /**
     * 根据搜索类型存的key
     */
    private String searchTypeKey;

    public String getSearchTypeKey() {
        return searchTypeKey;
    }

    public void setSearchTypeKey(String searchTypeKey) {
        this.searchTypeKey = searchTypeKey;
    }

    /**
     * 构造函数
     * 作用：对搜索框进行初始化
     */
    public SearchView(Context context) {
        this(context, null);
    }

    public SearchView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SearchView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        this.context = context;
        initAttrs(context, attrs);
        init();
    }

    /**
     * 关注a
     * 作用：初始化自定义属性
     */
    private void initAttrs(Context context, AttributeSet attrs) {
        // 控件资源名称
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.Search_View);
        // 搜索框字体大小（dp）
        textSizeSearch = typedArray.getDimensionPixelSize(R.styleable.Search_View_textSizeSearch, 14);
        // 搜索框字体颜色（使用十六进制代码，如#333、#8e8e8e）
        int defaultColor = context.getResources().getColor(R.color.color_676773); // 默认颜色 = 灰色
        textColorSearch = typedArray.getColor(R.styleable.Search_View_textColorSearch, defaultColor);
        // 搜索框提示内容（String）
        textHintSearch = typedArray.getString(R.styleable.Search_View_textHintSearch);
        // 搜索框颜色
        searchBlockColor = typedArray.getColor(R.styleable.Search_View_searchBlockColor, R.drawable.box_search);
        // 释放资源
        typedArray.recycle();
    }


    /**
     * 关注b
     * 作用：初始化搜索框
     */
    private void init() {
        // 1. 初始化UI组件->>关注c
        initView();
        /**
         * 监听输入键盘更换后的搜索按键
         * 调用时刻：点击键盘上的搜索键时
         */
        et_search.setOnKeyListener((v, keyCode, event) -> {
            if (keyCode == KeyEvent.KEYCODE_ENTER && event.getAction() == KeyEvent.ACTION_DOWN) {
                // 1. 点击搜索按键后，根据输入的搜索字段进行查询
                // 注：由于此处需求会根据自身情况不同而不同，所以具体逻辑由开发者自己实现，此处仅留出接口
                if (!(mCallBack == null)) {
                    mCallBack.SearchAciton(et_search.getText().toString().trim());
                    KeyboardUtils.hideSoftInput(et_search);
                }
                if (!StringUtils.isEmpty(et_search.getText().toString().trim()))
                    saveSearchHistory(searchTypeKey, et_search.getText().toString().trim());
            }
            return false;
        });

        /**
         * 点击返回按键后的事件
         */
        searchBack.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                // 注：由于返回需求会根据自身情况不同而不同，所以具体逻辑由开发者自己实现，此处仅留出接口
                if (!(bCallBack == null)) {
                    bCallBack.BackAciton();
                }
            }
        });

        searchBackRight.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!(bCallBack == null)) {
                    bCallBack.BackAciton();
                }
            }
        });
    }

    /**
     * 关注c：绑定搜索框xml视图
     */
    private void initView() {
        //  绑定R.layout.search_layout作为搜索框的xml文件
        LayoutInflater.from(context).inflate(R.layout.search_head_layout, this);
        // 绑定搜索框EditText
        et_search = findViewById(R.id.et_search);
        et_search.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSizeSearch);
        et_search.setTextColor(textColorSearch);
        et_search.setHint(textHintSearch);
        et_search.setBackgroundResource(searchBlockColor);
        //返回按键
        searchBack = findViewById(R.id.search_cancel);
        searchBackRight = findViewById(R.id.search_cancel_right);
    }

    /**
     * 给搜索框上设置内容
     *
     * @param s
     */
    public void setText(String s) {
        et_search.setText(s);
    }

    /**
     * 给搜索框上设置hint
     *
     * @param hitStr
     */
    public void setTextHint(String hitStr) {
        et_search.setHint(hitStr);
    }

    /**
     * 获取hint
     */
    public String getTextHint() {
        if (et_search.getHint() != null) {
            return et_search.getHint().toString();
        }
        return "";
    }

    /**
     * 获取内容
     *
     * @return
     */
    public String getText() {
        if (et_search.getText() != null) {
            return et_search.getText().toString();
        }
        return "";
    }

    /**
     * 点击键盘中搜索键后的操作，用于接口回调
     */
    public void setOnClickSearch(ICallBack mCallBack) {
        this.mCallBack = mCallBack;
    }

    /**
     * 点击返回后的操作，用于接口回调
     */
    public void setOnClickBack(bCallBack bCallBack) {
        this.bCallBack = bCallBack;
    }

    public interface bCallBack {
        void BackAciton();
    }

    public void setbSearchEventCallBack(SearchEventCallBack bSearchEventCallBack) {
        this.bSearchEventCallBack = bSearchEventCallBack;
    }

    public interface SearchEventCallBack {
        void SearchEvent();
    }

    public EditText getEt_search() {
        return et_search;
    }

    /**
     * 保存历史记录到sp里
     */
    public void saveSearchHistory(String typeKey, String inputText) {
        if (TextUtils.isEmpty(inputText)) {
            return;
        }
        String longHistory = PreferencesUtil.get(typeKey, "");  //获取之前保存的历史记录
        String[] tmpHistory = longHistory.split(","); //逗号截取 保存在数组中
        List<String> historyList = new ArrayList<String>(Arrays.asList(tmpHistory)); //将改数组转换成ArrayList
        if (historyList.size() > 0) {
            //1.移除之前重复添加的元素
            for (int i = 0; i < historyList.size(); i++) {
                if (inputText.equals(historyList.get(i))) {
                    historyList.remove(i);
                    break;
                }
            }
            historyList.add(0, inputText); //将新输入的文字添加集合的第0位也就是最前面(2.倒序)
            if (historyList.size() > 10) {
                historyList.remove(historyList.size() - 1); //3.最多保存10条搜索记录 删除最早搜索的那一项
            }
            //逗号拼接
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < historyList.size(); i++) {
                sb.append(historyList.get(i) + ",");
            }
            //保存到sp
            PreferencesUtil.put(typeKey, sb.toString());
        } else {
            //之前未添加过
            PreferencesUtil.put(typeKey, inputText + ",");
        }
    }

    //获取搜索记录
    public List<String> getSearchHistory(String typeKey) {
        String longHistory = PreferencesUtil.get(typeKey, "");
        String[] tmpHistory = longHistory.split(","); //split后长度为1有一个空串对象
        List<String> historyList = new ArrayList<>(Arrays.asList(tmpHistory));
        if (historyList.size() == 1 && historyList.get(0).equals("")) { //如果没有搜索记录，split之后第0位是个空串的情况下
            historyList.clear();  //清空集合，这个很关键
        }
        return historyList;
    }

    /**
     * 根据搜索type清空搜索历史记录
     *
     * @param typeKey
     */
    public void clearSearchHistory(String typeKey) {
        PreferencesUtil.remove(typeKey);
    }

    public void deleteItem(String typeKey, String item) {
        String longHistory = PreferencesUtil.get(typeKey, "");
        PreferencesUtil.put(typeKey, longHistory.replaceFirst(item+",", ""));
    }
}
