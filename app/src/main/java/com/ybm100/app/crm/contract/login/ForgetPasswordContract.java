package com.ybm100.app.crm.contract.login;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 01/03/2019 09:40
 */
public interface ForgetPasswordContract {

    interface IForgetPasswordModel extends IBaseModel {
        /**
         * 获取验证码
         *
         * @param account
         * @param phone
         * @return
         */
        Observable<RequestBaseBean> getValidateCode(String account, String phone);

        /**
         * 校验验证码
         *
         * @param userName
         * @param phone
         * @param code
         * @return
         */
        Observable<RequestBaseBean> checkValidate(String userName, String phone, String code);
    }

    interface IForgetPasswordView extends IBaseActivity {
        /**
         * 获取成功
         */
        void onGetValidateCodeSuccess();

        /**
         * 校验成功
         */
        void onCheckValidateSuccess(String account, String phone);
    }

}
