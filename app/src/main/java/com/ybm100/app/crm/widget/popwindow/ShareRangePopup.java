package com.ybm100.app.crm.widget.popwindow;

import android.content.Context;
import android.graphics.Color;
import android.view.Gravity;
import android.view.View;

import com.xyy.common.widget.flowtag.FlowTagLayout;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.order.bean.TagBean;
import com.ybm100.app.crm.ui.adapter.drugstore.FilterAdapter;

import java.util.List;

import razerdp.basepopup.BasePopupWindow;

/**
 * <AUTHOR>
 * @date 2019/3/18
 */
public class ShareRangePopup extends BasePopupWindow {

    private List<TagBean> popList;
    private FilterAdapter adapter;

    public ShareRangePopup(Context context) {
        super(context);
        initView();
        setAlignBackground(false);//背景是否对齐到PopupWindow
        setBackground(Color.TRANSPARENT);// 取消默认的背景颜色
        setOutSideDismiss(true);// 点击popupwindow背景部分不隐藏
    }

    public ShareRangePopup(Context context, List<TagBean> popList) {
        super(context);
        this.popList = popList;
        initView();
        setAlignBackground(false);//背景是否对齐到PopupWindow
        setBackground(Color.TRANSPARENT);// 取消默认的背景颜色
        setOutSideDismiss(true);// 点击popupwindow背景部分不隐藏
        setPopupGravity(Gravity.BOTTOM | Gravity.START);
    }

    private void initView() {
        FlowTagLayout filterStatusLayout = findViewById(R.id.flow_tag_layout_range);
        filterStatusLayout.setTagCheckedMode(FlowTagLayout.FLOW_TAG_CHECKED_SINGLE);
        filterStatusLayout.setTagShowMode(FlowTagLayout.FLOW_TAG_SHOW_FREE);
        adapter = new FilterAdapter(R.layout.item_tag5, popList);
        filterStatusLayout.setAdapter(adapter);

        filterStatusLayout.setOnTagClickListener(new FlowTagLayout.OnTagClickListener() {
            @Override
            public void onItemClick(FlowTagLayout parent, View view, int position) {
                if (clickListener != null) {
                    TagBean tagBean = (TagBean) parent.getAdapter().getItem(position);
                    if (tagBean != null) {
                        clickListener.onItemClick(tagBean, position);
                    }
                }
                dismiss();
            }
        });
    }

    public void refresh(List<TagBean> popList) {
        if (adapter != null) {
            adapter.setNewData(popList);
        }
    }

    public TagBean getItem(int position) {
        return popList.get(position);
    }

    @Override
    public View onCreateContentView() {
        return createPopupById(R.layout.list_popup_filter);
    }

    @Override
    public void showPopupWindow(View v) {
        super.showPopupWindow(v);
    }

    private OnPopItemClickListener clickListener;

    public void setOnPopItemClickListener(OnPopItemClickListener clickListener) {
        this.clickListener = clickListener;
    }

    public interface OnPopItemClickListener {
        void onItemClick(TagBean tagBean, int position);
    }
}
