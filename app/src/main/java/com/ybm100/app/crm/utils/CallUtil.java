package com.ybm100.app.crm.utils;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.net.Uri;

import com.tbruyelle.rxpermissions2.RxPermissions;
import com.xyy.common.util.ToastUtils;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.permission.PermissionUtil;
import com.ybm100.app.crm.schedule.service.CallRecordManager;

/**
 * <AUTHOR>
 * @date 2019-07-24
 */
public class CallUtil {
    private static boolean hasCallPhonePermission = false;
    private static boolean hasCallLogPermission = false;

    private static void callUp(Activity activity, String phoneNo, String merchantId, String merchantName, String source) {
        if (hasCallLogPermission && hasCallPhonePermission) {
            Intent intent = new Intent(Intent.ACTION_CALL);
            Uri data = Uri.parse("tel:" + phoneNo);
            intent.setData(data);
            activity.startActivity(intent);
            CallRecordManager.INSTANCE.addCallRecord(phoneNo, merchantId, merchantName, source);
            hasCallLogPermission = false;
            hasCallPhonePermission = false;
        }
    }

    @SuppressLint("CheckResult")
    public static void call(Activity activity, final String phoneNo, final String merchantId, final String merchantName, final String source) {
        RxPermissions rxPermissions = new RxPermissions(activity);
        rxPermissions
                .requestEach(Manifest.permission.CALL_PHONE, Manifest.permission.READ_CALL_LOG)
                .subscribe(permission -> {
                    if (permission.granted) {
                        if (Manifest.permission.CALL_PHONE.equals(permission.name)) {
                            hasCallPhonePermission = true;
                        } else {
                            hasCallLogPermission = true;
                        }
                        callUp(activity, phoneNo, merchantId, merchantName, source);
                    } else if (permission.shouldShowRequestPermissionRationale) {
                        if (Manifest.permission.CALL_PHONE.equals(permission.name)) {
                            ToastUtils.showShort(activity.getString(R.string.please_open_call_permission));
                        } else {
                            ToastUtils.showShort(activity.getString(R.string.please_open_call_log_permission));
                        }
                    } else {
                        PermissionUtil.showPermissionDialog(activity, activity.getString(R.string.phone_permission_name), false);
                    }
                });
    }

    @SuppressLint("CheckResult")
    public static void call(Activity activity, final String phoneNo) {
        RxPermissions rxPermissions = new RxPermissions(activity);
        rxPermissions.requestEach(Manifest.permission.CALL_PHONE).subscribe(permission -> {
            if (permission.granted) {
                Intent intent = new Intent(Intent.ACTION_CALL);
                Uri data = Uri.parse("tel:" + phoneNo);
                intent.setData(data);
                activity.startActivity(intent);
            } else if (permission.shouldShowRequestPermissionRationale) {
                ToastUtils.showShort(activity.getString(R.string.please_open_call_permission));
            } else {
                PermissionUtil.showPermissionDialog(activity, activity.getString(R.string.phone_permission_name), false);
            }
        });

    }
}
