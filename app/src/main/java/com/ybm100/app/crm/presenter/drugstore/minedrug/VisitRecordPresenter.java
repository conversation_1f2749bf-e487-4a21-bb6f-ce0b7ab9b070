package com.ybm100.app.crm.presenter.drugstore.minedrug;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.DrugstoreVisitBean;
import com.ybm100.app.crm.contract.drugstore.minedrug.VisitRecordContract;
import com.ybm100.app.crm.model.drugstore.minedrug.VisitRecordModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:10
 *
 * <AUTHOR>
 */
public class VisitRecordPresenter extends BasePresenter<VisitRecordContract.IVisitRecordModel, VisitRecordContract.IVisitRecordView> {

    public static VisitRecordPresenter newInstance() {
        return new VisitRecordPresenter();
    }

    @Override
    protected VisitRecordModel getModel() {
        return VisitRecordModel.newInstance();
    }

    public void getVisitListData(int limit, String merchantId, int offset) {

        if (mIView == null || mIModel == null) return;

        mRxManager.register(mIModel.followInRecords(limit, merchantId, offset).subscribe(new SimpleSuccessConsumer<RequestBaseBean<DrugstoreVisitBean>>(mIView) {
            @Override
            public void onSuccess(RequestBaseBean<DrugstoreVisitBean> drugstoreVisitBeanRequestBaseBean) {

                mIView.getVisitListDataSuccess(drugstoreVisitBeanRequestBaseBean);
            }
        }, new SimpleErrorConsumer(mIView) {
            @Override
            protected void onError(Throwable throwable, String msg) {
                super.onError(throwable, msg);
                if (mIView == null) return;
                mIView.showNetError();
            }
        }));

    }

}
