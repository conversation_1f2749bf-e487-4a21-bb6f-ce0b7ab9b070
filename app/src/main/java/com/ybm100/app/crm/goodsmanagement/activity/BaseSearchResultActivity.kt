package com.ybm100.app.crm.goodsmanagement.activity

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import com.xyy.common.util.FragmentUtils
import com.xyy.utilslibrary.base.fragment.BaseCompatFragment
import com.ybm100.app.crm.R
import com.ybm100.app.crm.constant.Constants
import com.ybm100.app.crm.goodsmanagement.bean.GoodsManagementAreaListBean
import com.ybm100.app.crm.goodsmanagement.bean.Zone
import com.ybm100.app.crm.goodsmanagement.fragment.CustomerGoodsManagementFilterFragment
import com.ybm100.app.crm.goodsmanagement.fragment.GoodsManagementFilterFragment
import com.ybm100.app.crm.goodsmanagement.fragment.GoodsManagementFragment
import kotlinx.android.synthetic.main.activity_base_search_v2.*

/**
 * 选择商品
 */
class BaseSearchResultActivity : BaseDrawerActivity(), GoodsManagementFilterFragment.DrawerListener, CustomerGoodsManagementFilterFragment.CustomerDrawerListener {
    private var mSearchType = -1
    private var mSearchKeyword = ""
    private var mAreaCode = ""
    private var mMerchantId = ""
    private lateinit var mFragment: BaseCompatFragment
    private lateinit var mCustomerDrawerFragment: CustomerGoodsManagementFilterFragment

    override fun initTransferData() {
        super.initTransferData()
        intent?.extras?.run {
            mSearchType = getInt(Constants.GoodsManagement.ARG_FRAGMENT_TYPE, -1)
            mSearchKeyword = getString(Constants.GoodsManagement.ARG_SEARCH_KEYWORD, "")
            mAreaCode = getString(Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_AREA_CODE, "")
            mMerchantId = getString(Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_MERCHANT_ID, "")
        }
    }

    override fun getContentMainLayoutID(): Int {
        return R.layout.activity_fragment_container
    }

    override fun initContentMain() {
        initSearchView()

        initMainFragment()
    }

    private fun initMainFragment() {
        mFragment = GoodsManagementFragment.newInstance(intent?.extras?.apply {
            putString(Constants.GoodsManagement.ARG_SEARCH_KEYWORD, mSearchKeyword)
        })
        FragmentUtils.replaceFragment(supportFragmentManager, mFragment, R.id.fragment_container, false)
    }

    override fun getDrawerFragments(): List<Fragment> {
        return if (mSearchType == Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION_SEARCH ||
                mSearchType == Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_ALL_GOODS_SEARCH ||
                mSearchType == Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION_RECOMMENDATION_SEARCH
        ) {
            listOf<Fragment>(GoodsManagementFilterFragment.newInstance(mSearchType, mAreaCode).apply {
                setDrawerListener(this@BaseSearchResultActivity)
            })
        } else {
            mCustomerDrawerFragment = CustomerGoodsManagementFilterFragment.newInstance(mMerchantId).apply {
                setCustomerDrawerListener(this@BaseSearchResultActivity)
            }
            listOf<Fragment>(mCustomerDrawerFragment)
        }
    }


    private fun initSearchView() {
        search_view.visibility = View.VISIBLE

        search_view.text = mSearchKeyword

        //SearchView text hint
        when (mSearchType) {
//            else -> search_view?.textHint = "搜索"
        }
        //取消按钮点击事件
        search_view.setOnClickBack {
            (mFragment as GoodsManagementFragment).onCancelPressed()
        }

        search_view.searchTypeKey = "$mSearchType"

        //键盘搜索点击
        search_view.setOnClickSearch {
            val text: String? = search_view.text.trim()
            if (text.isNullOrEmpty()) {
//                ToastUtils.showShort()
            } else {
                (mFragment as GoodsManagementFragment).search(text)
            }
        }
    }

    override fun onConfirmPressed(itemArea: GoodsManagementAreaListBean.Row?, pos: Int, selectedZone: Zone?) {
        closeDrawer()
        (mFragment as GoodsManagementFragment).onDrawerConfirmPressed(itemArea, selectedZone)
    }

    fun syncCustomerDrawerFilterData(YBMFilterPos: Int) {
        if (::mCustomerDrawerFragment.isInitialized) {
            (mCustomerDrawerFragment).syncCustomerDrawerFilterData(YBMFilterPos)
        }
    }

    override fun onCustomerConfirmPressed(YBMFilterPos: Int, collectionStatusFilterPos: Int, selectedZone: Zone?) {
        closeDrawer()
        (mFragment as GoodsManagementFragment).onCustomerDrawerConfirmPressed(YBMFilterPos, collectionStatusFilterPos, selectedZone)
    }

    companion object {
        @JvmStatic
        fun startActivity(activity: Activity?, searchType: Int, searchKeyword: String, merchantID: String? = "", areaCode: String? = "") {
            val intent = Intent(activity, BaseSearchResultActivity::class.java)
            val bundle = Bundle()
            bundle.putInt(Constants.GoodsManagement.ARG_FRAGMENT_TYPE, searchType)
            bundle.putString(Constants.GoodsManagement.ARG_SEARCH_KEYWORD, searchKeyword)
            bundle.putString(Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_MERCHANT_ID, merchantID)
            bundle.putString(Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_AREA_CODE, areaCode)
            intent.putExtras(bundle)
            activity?.startActivity(intent)
        }
    }
}
