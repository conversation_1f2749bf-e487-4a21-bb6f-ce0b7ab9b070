package com.ybm100.app.crm.order.bean;

import com.google.gson.annotations.SerializedName;
import com.xyy.common.widget.flowtag.OptionCheck;

import java.io.Serializable;

/**
 * @author: yuhaibo
 * @time: 2019/1/2 下午5:13.
 * projectName: XyyBeanSprouts.
 * Description:
 */
public class TagBean implements OptionCheck, Serializable {
    @SerializedName(value = "value", alternate = "branchName")
    private String value;
    private int id;
    private boolean checked;
    private boolean isMutual;
    private String code;
    private String name;
    private String branchCode;

    public TagBean(String tagName, int tagKey, boolean checked) {
        this.value = tagName;
        this.id = tagKey;
        this.checked = checked;
    }

    public TagBean(String tagName, int tagKey, boolean checked, boolean isMutual) {
        this.value = tagName;
        this.id = tagKey;
        this.checked = checked;
        this.isMutual = isMutual;
    }

    @Override
    public boolean isChecked() {
        return checked;
    }

    @Override
    public void setChecked(boolean checked) {
        this.checked = checked;
    }

    @Override
    public boolean isMutual() {
        return isMutual;
    }

    public String getTagName() {
        return value;
    }

    public void setTagName(String tagName) {
        this.value = tagName;
    }

    public int getTagKey() {
        return id;
    }

    public void setTagKey(int tagKey) {
        this.id = tagKey;
    }

    public void setMutual(boolean mutual) {
        isMutual = mutual;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }


}
