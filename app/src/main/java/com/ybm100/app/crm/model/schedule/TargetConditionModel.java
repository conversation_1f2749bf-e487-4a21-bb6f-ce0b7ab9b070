package com.ybm100.app.crm.model.schedule;

import android.content.Context;

import com.google.gson.Gson;
import com.xyy.utilslibrary.base.BaseModel;
import com.ybm100.app.crm.bean.schedule.MerchantEnumsBean;
import com.ybm100.app.crm.contract.schedule.TargetConditionContract;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;

import io.reactivex.Observable;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.schedulers.Schedulers;

/**
 * Created by XyyMvpSportTemplate on 06/21/2019 15:00
 */
public class TargetConditionModel extends BaseModel implements TargetConditionContract.ITargetConditionModel {

    public static TargetConditionModel newInstance() {
        return new TargetConditionModel();
    }

    @Override
    public Observable<MerchantEnumsBean> merchantEnums(Context context) {
        return Observable.create((ObservableOnSubscribe<MerchantEnumsBean>) emitter -> {
            InputStreamReader inputStreamReader;
            try {
                inputStreamReader = new InputStreamReader(context.getAssets().open("merchant.json"), StandardCharsets.UTF_8);
                BufferedReader bufferedReader = new BufferedReader(
                        inputStreamReader);
                String line;
                StringBuilder stringBuilder = new StringBuilder();
                while ((line = bufferedReader.readLine()) != null) {
                    stringBuilder.append(line);
                }
                inputStreamReader.close();
                bufferedReader.close();
                String resultString = stringBuilder.toString();
                Gson gson = new Gson();
                MerchantEnumsBean enumsBean = gson.fromJson(resultString, MerchantEnumsBean.class);
                emitter.onNext(enumsBean);
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            }

        }).subscribeOn(Schedulers.io());
    }
}