package com.ybm100.app.crm.contract.hycustomer;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.hycustomer.HyPrivateDetailBean;
import com.ybm100.app.crm.task.bean.TaskAndMerchantBean;

import io.reactivex.Observable;

/**
 * 荷叶健康私海客户详情接口
 */
public interface HyPrivateDetailContract {
    interface IHyPrivateDetailModel extends IBaseModel {
        Observable<RequestBaseBean<HyPrivateDetailBean>> getBaseInfo(String merchantId);

        /**
         * 新建拜访（客户入口）
         *
         * @param merchantId
         * @param customerType 1客户2线索
         * @return
         */
        Observable<RequestBaseBean<TaskAndMerchantBean>> toAddVisit(String merchantId, String customerType);

        /**
         * 分配至bd
         */
        Observable<RequestBaseBean> distributeToBD(String bindUserId, String customerId);
    }

    interface IHyPrivateDetailView extends IBaseActivity {
        void getBaseInfo(RequestBaseBean<HyPrivateDetailBean> requestBaseBean);

        void distributeToBDSuccess(RequestBaseBean baseBean);


        void toAddVisit(RequestBaseBean<TaskAndMerchantBean> requestBaseBean);
    }
}
