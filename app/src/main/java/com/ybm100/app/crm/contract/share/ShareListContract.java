package com.ybm100.app.crm.contract.share;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.share.ShareListBean;

import java.util.Map;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 03/05/2019 19:07
 */
public interface ShareListContract {

    interface IShareListModel extends IBaseModel {
        Observable<RequestBaseBean<ShareListBean>> getListData(Map<String, String> params);

        Observable<RequestBaseBean> shareTimesAddOne(String sourceId);
    }

    interface IShareListView extends IBaseActivity {

        void getListDataSuccess(boolean refresh, RequestBaseBean<ShareListBean> baseBean);

        void shareTimesAddOneSuccess(RequestBaseBean baseBean);

        void enableLoadMore(boolean b);

        void loadMoreComplete();

    }

}
