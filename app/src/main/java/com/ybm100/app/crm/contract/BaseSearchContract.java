package com.ybm100.app.crm.contract;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.ISearch;
import com.ybm100.app.crm.task.bean.PagingApiBean;

import java.util.List;

import io.reactivex.Observable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/12/25
 * 搜索积累
 */
public interface BaseSearchContract {
    interface IModel<T extends ISearch> extends IBaseModel {
        Observable<RequestBaseBean<PagingApiBean<T>>> search(String apiVersion, String keyword, int limit, int offset);
    }

    interface IView<T> extends IBaseActivity {
        void onSearchSuccess(List<T> executors, boolean append);

        void setHasMore(boolean hasMore);

        void onSearchFail();
    }
}
