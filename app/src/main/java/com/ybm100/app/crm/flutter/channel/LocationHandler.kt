package com.ybm100.app.crm.flutter.channel

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.os.Handler
import android.util.Log
import com.baidu.location.BDLocation
import com.xyy.flutter.container.container.bridge.BaseHandler
import com.xyy.flutter.container.container.bridge.callback.LocationCallback
import com.xyy.flutter.container.container.ui.FlutterRunnerActivity
import com.ybm100.app.crm.permission.PermissionUtil
import com.ybm100.app.crm.ui.activity.lbs.LocationManager

class LocationHandler {

    var callback: LocationCallback? = null


    fun handle(context: Context, callback: LocationCallback) {
        Log.e("guan", "开始定位：${callback.hashCode()},${LocationManager.getInstance().isLocating}")
        this.callback = callback
        val locationListener = object : LocationManager.LocationListener {
            var hasResult = false
            override fun onReceiveLocation(bd: BDLocation?) {
                LocationManager.getInstance().unRegisterLocationListener(this)
                Log.e("guan", "开始定位,success：${<EMAIL>?.hashCode()}")
                if (hasResult) {
                    return
                }
                hasResult = true
                if (bd != null && bd.latitude != 4.9E-324 && bd.longitude != 4.9E-324) {
                    // 定位成功
                    Log.e("guan", "定位成功了：${bd.latitude},${bd.longitude},${bd.addrStr}")
                    <EMAIL>?.result(
                            true, bd.latitude, bd.longitude, mapOf(
                            "address" to bd.addrStr
                    )
                    )
                } else {
                    Log.e("guan", "定位失败了")
                    // 定位失败
                    <EMAIL>?.result(
                            false,
                            Double.MAX_VALUE,
                            Double.MAX_VALUE,
                            null
                    )
                }
            }

        }
        Log.e("guan", "addListener:${locationListener.hashCode()}")

        LocationManager.getInstance()
                .locationPermissions(
                        context as Activity?,
                        locationListener, true,
                        PermissionUtil.OnCancelCallBack {
                            LocationManager.getInstance().unRegisterLocationListener(locationListener)
                            Log.e(
                                    "guan",
                                    "开始定位,cancel：${<EMAIL>?.hashCode()}"
                            )
                            Log.e("guan", "定位取消了")
                            <EMAIL>?.result(
                                    false,
                                    Double.MAX_VALUE,
                                    Double.MAX_VALUE,
                                    null
                            )
                        }
                )

    }


}