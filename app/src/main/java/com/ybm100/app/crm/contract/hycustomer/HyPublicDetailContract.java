package com.ybm100.app.crm.contract.hycustomer;


import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.hycustomer.HyPublicDetailBean;

import io.reactivex.Observable;

/**
 *荷叶健康客户详情接口
 */
public interface HyPublicDetailContract{

    interface IHyPublicDetailModel extends IBaseModel {
        //我的药店列表
        Observable<RequestBaseBean<HyPublicDetailBean>> searchOpenSeaDetail(String id);

        //认领
        Observable<RequestBaseBean> receive(String id);
    }

    interface IHyPublicDetailView extends IBaseActivity {

        void searchOpenSeaDetailSuccess(RequestBaseBean<HyPublicDetailBean> baseBean);

        void receiveSuccess(RequestBaseBean requestBaseBean);
    }
}
