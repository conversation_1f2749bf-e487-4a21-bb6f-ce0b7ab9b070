package com.ybm100.app.crm.presenter.share;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.contract.share.ShareRootContract;
import com.ybm100.app.crm.model.share.ShareRootModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;


/**
 * Created by XyyMvpSportTemplate on 03/05/2019 19:07
 */
public class ShareRootPresenter extends BasePresenter<ShareRootContract.IShareRootModel, ShareRootContract.IShareRootView> {


    public static ShareRootPresenter newInstance() {
        return new ShareRootPresenter();
    }

    @Override
    protected ShareRootModel getModel() {
        return ShareRootModel.newInstance();
    }

    /**
     * 查询省级子公司
     */
    public void getTopGroups() {
        if (mIModel == null || mIView == null) {
            return;
        }
        mRxManager.register(mIModel.getTopGroups().subscribe(new SimpleSuccessConsumer<RequestBaseBean>(mIView) {
            @Override
            public void onSuccess(RequestBaseBean baseBean) {
                if (mIView == null) return;
                mIView.getTopGroupsSuccess(baseBean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }
}
