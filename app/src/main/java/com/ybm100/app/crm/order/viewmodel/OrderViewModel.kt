package com.ybm100.app.crm.order.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.helper.RxHelper
import com.ybm100.app.crm.api.ApiService
import com.ybm100.app.crm.net.RetrofitCreateHelper
import com.ybm100.app.crm.net.helper.BaseObserver
import com.ybm100.app.crm.order.bean.FiltrateBean
import com.ybm100.app.crm.order.bean.OrderBean
import com.ybm100.app.crm.order.bean.TagBean
import java.util.*

/**
 * @author: yuhaibo
 * @time: 2019/1/8 下午1:54.
 * projectName: XyyBeanSprouts.
 * Description: 订单查询接口viewModel
 */
class OrderViewModel : ViewModel() {
    val data = MutableLiveData<OrderBean>()
    var parameterMap = MutableLiveData<HashMap<String, String>>()


    var reqErrorLoadFiltrateStatus = MutableLiveData<Boolean>()
    val filtrateData = MutableLiveData<FiltrateBean>()
    var effectiveOrderStatuses = MutableLiveData<ArrayList<TagBean>>()
    var homeOrderRange = MutableLiveData<ArrayList<TagBean>>()
    //记录时间的id
    var dataTypeData = MutableLiveData<Int>()

    /**
     * 请求筛选条件数据
     */
    fun loadFiltrateData() {
        RetrofitCreateHelper.createApi(ApiService::class.java).orderFiltrateData
                .compose(RxHelper.rxSchedulerHelper<RequestBaseBean<FiltrateBean>>())
                .subscribe(object : BaseObserver<FiltrateBean>() {
                    override fun onSuccess(response: FiltrateBean?) {
                        reqErrorLoadFiltrateStatus.value = false

                        val activities = response?.activities
                        if (!activities.isNullOrEmpty()) {
                            val activityTagBean = activities[0]
                            activityTagBean.isChecked = true
                            activityTagBean.isMutual = true
                        }
                        val orderPayTypes = response?.orderPayTypes
                        if (!orderPayTypes.isNullOrEmpty()) {
                            val payTypeTagBean = orderPayTypes[0]
                            payTypeTagBean.isMutual = true
                            payTypeTagBean.isChecked = true
                        }
                        val payChannels = response?.payChannels
                        if (!payChannels.isNullOrEmpty()) {
                            val payChannelTagBean = payChannels[0]
                            payChannelTagBean.isChecked = true
                            payChannelTagBean.isMutual = true
                        }
                        val orderStatuses = response?.orderStatuses
                        if (!orderStatuses.isNullOrEmpty()) {
                            val orderStatusTagBean = orderStatuses[0]
                            orderStatusTagBean.isMutual = true
                            orderStatusTagBean.isChecked = true
                        }
                        val effectiveOrder = response?.effectiveOrderStatuses
                        if (!effectiveOrder.isNullOrEmpty()) {
                            val effectiveOrderStatusesBean = effectiveOrder[0]
                            effectiveOrderStatusesBean.isMutual = true
                            effectiveOrderStatusesBean.isChecked = true
                        }
                        val sysGroups = response?.sysGroups
                        if (!sysGroups.isNullOrEmpty()) {
                            val orderRangeTagBean = sysGroups[0]
                            orderRangeTagBean.isChecked = false
                            orderRangeTagBean.isMutual = false
                        }
                        response?.orderPayTypes = orderPayTypes
                        response?.payChannels = payChannels
                        response?.orderStatuses = orderStatuses
                        response?.activities = activities
                        response?.sysGroups = sysGroups
                        response?.effectiveOrderStatuses = effectiveOrder

                        filtrateData.value = response
                        val effectiveOrderData = response?.effectiveOrderStatuses
                        if (effectiveOrderData != null) {
                            effectiveOrderStatuses.value = effectiveOrderData as ArrayList<TagBean>?
                        }
                    }

                    override fun onFailure(msg: String?) {
                        reqErrorLoadFiltrateStatus.value = true
                    }
                })

    }

}
