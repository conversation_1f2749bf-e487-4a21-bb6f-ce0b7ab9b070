package com.ybm100.app.crm.presenter.message;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.message.MessageReadCountBean;
import com.ybm100.app.crm.bean.message.MessageType;
import com.ybm100.app.crm.contract.message.MessageContract;
import com.ybm100.app.crm.model.message.MessageModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

/**
 * <AUTHOR>
 * @version 1.0
 * @file MessagePresenter2.java
 * @brief
 * @date 2018/12/22
 * Copyright (c) 2018, 北京小药药
 * All rights reserved.
 */
public class MessagePresenter2 extends BasePresenter<MessageContract.IMessageModel, MessageContract.IMessageView> {
    @Override
    protected MessageContract.IMessageModel getModel() {
        return new MessageModel();
    }

    public static MessagePresenter2 newInstance() {
        return new MessagePresenter2();
    }

    public void getMsgReadCount() {
        mRxManager.register(
                mIModel.getMsgCount()
                        .subscribe(new SimpleSuccessConsumer<MessageReadCountBean>(mIView) {
                            @Override
                            public void onSuccess(MessageReadCountBean bean) {
                                mIView.renderCount(bean);
                            }
                        }, new SimpleErrorConsumer(mIView)));
    }


    public void getAllMessageType(){
        mRxManager.register(
                mIModel.getAllMessageType()
                        .subscribe(new SimpleSuccessConsumer<RequestBaseBean<MessageType>>(mIView) {
                            @Override
                            public void onSuccess(RequestBaseBean<MessageType> bean) {
                                mIView.getAllMessageTypeSuccess(bean);
                            }
                        }, new SimpleErrorConsumer(mIView)));
    }
}
