package com.ybm100.app.crm.flutter.debug

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.View
import com.xyy.common.navigationbar.AbsNavigationBar
import com.xyy.common.navigationbar.DefaultNavigationBar
import com.xyy.common.widget.flowtag.FlowTagLayout
import com.xyy.common.widget.flowtag.OptionCheck
import com.xyy.common.widget.flowtag.adaper.BaseFlowAdapter
import com.xyy.common.widget.flowtag.adaper.BaseTagHolder
import com.xyy.flutter.container.container.ContainerRuntime
import com.xyy.flutter.container.container.route.IOpenCallback
import com.xyy.utilslibrary.base.activity.BaseCompatActivity
import com.ybm100.app.crm.R
import kotlinx.android.synthetic.main.activity_flutter_jump.*

class FlutterJumpActivity : BaseCompatActivity() {


    companion object {
        fun startActivity(context: Context?) {
            context?.startActivity(Intent(context, FlutterJumpActivity::class.java))
        }
    }


    override fun initHead(): AbsNavigationBar<*> {
        return DefaultNavigationBar.Builder(this).setTitle("跳转Flutter页面")
                .setLeftIcon(R.drawable.nav_return)
                .builder()
    }

    override fun getLayoutId(): Int {
        return R.layout.activity_flutter_jump
    }

    override fun initView(savedInstanceState: Bundle?) {
        bt_jump.setOnClickListener {
            var path = et_path.text.trim().toString()
            if (path.isEmpty()) {
                path = "/"
            }
            saveHistory(path)
            ftl_history.adapter.notifyDataSetChanged()
            handleUrl(path)
        }
        ftl_history.setTagCheckedMode(FlowTagLayout.FLOW_TAG_CHECKED_SINGLE)
        ftl_history.setTagShowMode(FlowTagLayout.FLOW_TAG_SHOW_FREE)
        ftl_history.visibility = View.VISIBLE
        ftl_history.setOnTagClickListener { parent, view, position ->
            et_path.setText(view.tag as String?)
        }

    }

    private fun handleUrl(url: String) {
        //去除path和query部分
        try {
            Log.e("guan", "---------------------------start---------------------------")
            Log.e("guan", "url:${url}")
            val parse = Uri.parse(url)
            Log.e("guan", "isRelative:${parse.isRelative}")
            Log.e("guan", "scheme:${parse.scheme}")
            Log.e("guan", "host:${parse.host}")
            Log.e("guan", "path:${parse.path}")
            Log.e("guan", "query:${parse.query}")
            ContainerRuntime.getFlutterRouter().open(this, url, object : IOpenCallback {
                override fun result(resultData: Map<String, Any?>?) {
                    Log.e("guan", resultData.toString())
                }
            })
        } catch (e: Exception) {
            Log.e("guan", "error:${e.message}")
            e.printStackTrace()
        }

    }

    override fun onResume() {
        super.onResume()
        ftl_history.adapter = object : BaseFlowAdapter<Tag, BaseTagHolder>(
                R.layout.item_flutter_history,
                getHistoryList().map<String, Tag> {
                    Tag(it)
                }) {
            override fun convert(tagHelper: BaseTagHolder?, item: Tag?) {
                tagHelper?.setText(R.id.tv_content, item?.content ?: "")
                tagHelper?.itemView?.tag = item?.content ?: ""
            }
        }
    }

    private fun saveHistory(path: String) {
        val sp = getSharedPreferences("flutter_history", Context.MODE_PRIVATE)
        val set = sp.getStringSet("history", emptySet())
        set?.let { history ->
            if (!history.contains(path)) {
                sp.edit().putStringSet("history", mutableSetOf<String>().also { empty ->
                    empty.addAll(history)
                    empty.add(path)
                }).apply()
            }
        }
    }

    fun getHistoryList(): List<String> {
        val sp = getSharedPreferences("flutter_history", Context.MODE_PRIVATE)
        val set = sp.getStringSet("history", emptySet())
        return set?.toList() ?: emptyList()
    }


    class Tag(val content: String) : OptionCheck {
        var check = false
        override fun isChecked(): Boolean {
            return check
        }

        override fun setChecked(checked: Boolean) {
            this.check = checked
        }

        override fun isMutual(): Boolean {
            return true
        }

    }
}