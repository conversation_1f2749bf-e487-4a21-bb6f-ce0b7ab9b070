package com.ybm100.app.crm.utils.deviceInfo

import android.content.Context
import com.ybm100.app.crm.utils.SnowGroundUtils
import com.ybm100.app.crm.utils.deviceInfo.collector.*
import kotlin.concurrent.thread


/**
 * 做一些简单的检测，部分检测逻辑参考了这个库：https://github.com/lamster2018/EasyProtector
 *
 * ps1：这里检测不是准确的，java层的检测结果都是可以被hook篡改的，仅做参考！！！！
 * ps2：后续考虑做jni层的检测更准确一些，不过工程量巨大
 * ps3：虚拟定位考虑了很久，感觉目前的技术手段基本检测不出来，Android系统开的口子太大了
 * ps4：这种功能还是要有专门的安全工程师来做，我们没有时间作这些深入源码的工作
 * ps5：暂时还没发售
 */
object DeviceInfoReporter {
    var reportState = ReportState.READY_TO_REPORT

    private val collectorMap: Map<String, BaseCollector> by lazy {
        mapOf(
                "abis" to ABISCollector(),
                "xposed" to XposedCollector(),
                "sandBox" to SandBoxCollector(),
                "root" to RootCollector(),
                "isEmu" to EmulatorCollector(),
                "screen" to ScreenCollector(),
                "proxy" to ProxyCollector(),
                "notification" to NotificationCollector(),
//                "callRecord" to CallRecordCollector(),
//                "wifiList" to WifiListCollector(),
//                "appList" to AppListCollector(),
//                "SDCardFileList" to SDCardFileListCollector(),
                "catchRat" to CatchRatCollector()
        )
    }


    fun report(context: Context?) {
        if (context == null) {
            return
        }
        val app = context.applicationContext
        if (reportState == ReportState.READY_TO_REPORT) {
            thread {
                if (app == null) {
                    return@thread
                }
                reportState = ReportState.IN_THE_REPORT
                val extra = hashMapOf<String, String>()
                for (entry in collectorMap.entries) {
                    extra[entry.key] = entry.value.collect(app)
                }
                SnowGroundUtils.track("device_info", extra)
                reportState = ReportState.REPORTED
            }
        }
    }


    enum class ReportState {
        READY_TO_REPORT, IN_THE_REPORT, REPORTED
    }
}