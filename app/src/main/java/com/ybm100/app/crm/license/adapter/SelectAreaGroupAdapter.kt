package com.ybm100.app.crm.license.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.ybm100.app.crm.R
import com.ybm100.app.crm.bean.license.DeliveryAddressBean

/**
 * Created by dengmingjia on 2019/1/14
 */
class SelectAreaGroupAdapter : BaseQuickAdapter<DeliveryAddressBean, BaseViewHolder>(R.layout.item_select_area_group) {
    override fun convert(helper: BaseViewHolder, item: DeliveryAddressBean) {
        val position = helper.adapterPosition
        helper.setText(R.id.tv_name, item.areaName)
        if (position == itemCount - 1) {
            helper.setGone(R.id.tv_line, true)
        } else {
            helper.setGone(R.id.tv_line, false)
        }
    }

    override fun addData(data: DeliveryAddressBean) {
        super.addData(data)
        notifyDataSetChanged()
    }
}