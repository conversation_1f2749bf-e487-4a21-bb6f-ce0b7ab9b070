package com.ybm100.app.crm.flutter.channel

import android.Manifest
import android.annotation.SuppressLint
import android.util.Log
import androidx.fragment.app.FragmentActivity
import com.tbruyelle.rxpermissions2.Permission
import com.tbruyelle.rxpermissions2.RxPermissions
import com.xyy.common.util.ToastUtils
import com.xyy.flutter.container.container.bridge.BaseHandler
import com.ybm100.app.crm.R
import com.ybm100.app.crm.function.speech.SpeechManager
import com.ybm100.app.crm.permission.PermissionUtil
import io.reactivex.functions.Consumer

class VoiceInputHandler : BaseHandler() {

    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {
        val action = (params["action_type"] ?: "start") as String

        when (action) {
            "start" -> {
                start(activity)
            }
            "stop" -> {
                stop()
            }
        }

    }

    @SuppressLint("CheckResult")
    private fun start(activity: FragmentActivity) {
        RxPermissions(activity).requestEach(Manifest.permission.RECORD_AUDIO, Manifest.permission.WRITE_EXTERNAL_STORAGE).subscribe(object : Consumer<Permission> {
            override fun accept(permission: Permission) {
                when {
                    permission.granted -> {
                        SpeechManagerHelper.start(activity)
                    }
                    permission.shouldShowRequestPermissionRationale -> {
                        ToastUtils.showShort(activity.getString(R.string.please_open_storage_permission))
                    }
                    else -> {
                        PermissionUtil.showPermissionDialog(activity, activity.getString(R.string.storage_permission_name), false)
                    }
                }
            }
        })
        success("")
    }

    private fun stop() {
        SpeechManagerHelper.stop(this)
    }


    object SpeechManagerHelper : SpeechManager.OnSpeechCallback {
        var voiceInputText = ""

        var speechManager: SpeechManager? = null


        var resultHandler: BaseHandler? = null


        var isStart = false
        var hasStop = false


        fun start(activity: FragmentActivity) {
            Log.e("guan flutter", "start")
            reset()
            if (speechManager == null) {
                speechManager = SpeechManager(activity, this)
            }
            speechManager?.startSpeech()
            isStart = true
        }

        private fun reset() {
            speechManager?.stopSpeech()
            resultHandler = null
            voiceInputText = ""
            hasStop = false
        }

        fun stop(baseHandler: BaseHandler?) {
            Log.e("guan flutter", "stop call")
            if (isStart) {
                Log.e("guan flutter", "stop isStart")
                // 返回当前的语音转换文案，结束上一次的handler
                resultHandler?.success(voiceInputText)
                // 保存最新的handler
                resultHandler = baseHandler
                if (!hasStop) {
                    // 如果没有停止过，则停止收集语音
                    Log.e("guan flutter", "stop hasStop")
                    speechManager?.stopSpeech()
                    hasStop = true
                }
            } else {
                Log.e("guan flutter", "stop call direct result")
                baseHandler?.success(voiceInputText)
            }
        }

        override fun onSpeechConvertResult(content: String?, isLast: Boolean) {
            Log.e("guan flutter", "content$content,isLast:$isLast")
            voiceInputText += content
            if (isLast && hasStop && resultHandler != null) {
                Log.e("guan flutter", "reuslt:${voiceInputText}")
                resultHandler?.success(voiceInputText)
                reset()
            }
        }

        override fun onVolumeChanged(volume: Int) {
        }

        override fun onBeginOfSpeech() {
        }

        override fun onError(code: Int, hintMsg: String?) {
        }

        override fun onEndOfSpeech() {
            Log.e("guan flutter", "onEndOfSpeech")
        }
    }

}
