package com.ybm100.app.crm.doraemon

import android.Manifest
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import com.didichuxing.doraemonkit.kit.webdoor.WebDoorManager
import com.didichuxing.doraemonkit.zxing.activity.CaptureActivity
import com.tbruyelle.rxpermissions2.RxPermissions
import com.xyy.common.navigationbar.AbsNavigationBar
import com.xyy.common.navigationbar.DefaultNavigationBar
import com.xyy.common.util.ToastUtils
import com.xyy.utilslibrary.base.activity.BaseCompatActivity
import com.ybm100.app.crm.R
import com.ybm100.app.crm.permission.PermissionUtil
import kotlinx.android.synthetic.main.activity_qr_code.*

/**
 * Debug 模式 -- 二维码入口
 */
class QRCodeActivity : BaseCompatActivity(), View.OnClickListener {

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.tv_copy -> {
                if (!tv_qr_code_value.text.isNullOrEmpty()) {
                    val clipboard = this.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                    val clip: ClipData = ClipData.newPlainText("simple text", tv_qr_code_value.text)
                    clipboard.primaryClip = clip
                    ToastUtils.showShort("${tv_qr_code_value.text} 已复制")
                }
            }
        }
    }

    override fun initHead(): AbsNavigationBar<*> {
        return  DefaultNavigationBar.Builder(this).setTitle("二维码")
                .setLeftIcon(R.drawable.nav_return)
                .builder()
    }

    override fun getLayoutId(): Int {
        return R.layout.activity_qr_code
    }

    override fun initView(savedInstanceState: Bundle?) {
        tv_copy.setOnClickListener(this)
        val rxPermissions = RxPermissions(this)
        rxPermissions.requestEach(Manifest.permission.CAMERA).subscribe { permission ->
            when {
                permission.granted -> {
                    qlCode()
                }
                permission.shouldShowRequestPermissionRationale -> ToastUtils.showShort("请打开相机权限，才能使用该功能")
                else -> PermissionUtil.showPermissionDialog(this, "需要相机权限", false)
            }
        }
    }

    private fun qlCode() {
        val intent = Intent(this, CaptureActivity::class.java)
        startActivityForResult(intent, REQUEST_QR_CODE)
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK && requestCode == REQUEST_QR_CODE) {
            data?.extras?.run {
                val result = getString(CaptureActivity.INTENT_EXTRA_KEY_QR_SCAN, "")
                if (result.isNotEmpty()) {
                    doSearch(result)
                }
            }
        } else {
            finish()
        }
    }

    private fun doSearch(url: String?) {
        url?.let {
            if (it.startsWith("http", true)) {
                WebDoorManager.getInstance().saveHistory(url)
                WebDoorManager.getInstance().webDoorCallback.overrideUrlLoading(this, url)
                finish()
            } else {
                tv_copy.visibility = View.VISIBLE
                tv_qr_code_value.text = url
            }
        }
    }


    companion object {
        private const val REQUEST_CAMERA = 2
        private const val REQUEST_QR_CODE = 3
    }
}
