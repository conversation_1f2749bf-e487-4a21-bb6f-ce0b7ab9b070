package com.ybm100.app.crm.flutter

enum class ErrorCode(val errorCode: String, val errorMsg: String) {
    ACTIVITY_IS_NULL("-1000", "activity is null"),
    INTERNAL_ERROR("-1001", "An exception occurred in handling"),


    /**
     * NetworkHandler
     */
    ERROR_CONTAINER("1001", "容器必须继承自iBaseView"),
    INVALID_METHOD("1002", "请求method只能为get或post"),
    INVALID_CONTENT_TYPE("1003", "contentType只能为form或json"),
    INVALID_REQUEST_PATH("1004", "请求路径不能为空"),
    RESPONSE_EMPTY("1005", "返回response为空"),
    REQUEST_ERROR("1006", "请求错误"),

    /**
     * PhotoHandler
     */
    NO_CAMERA_PERMISSION("1011", "没有相机权限"),
    NO_STORAGE_PERMISSION("1012", "没有存储权限"),
    NO_RESULT("1013", "没有返回结果"),
    NO_EXIST("1014", "文件不存在"),
    FILE_CORRUPTED("1015", "文件已损坏"),
    RESULT_IS_EMPTY("1016", "选择返回结果为空"),

    /**
     * UploadPhotoHandler
     */
    INVALID_URL("1021", "uploadUrl不合法"),
    PHOTO_LIST_EMPTY("1022", "localPaths为空"),
    INVALID_LOCAL_PATH("1023", "localPaths不合法"),
    PHOTO_COMPRESS_FAIL("1024", "图片压缩失败")
}