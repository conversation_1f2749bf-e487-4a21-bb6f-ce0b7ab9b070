package com.ybm100.app.crm.contract.home;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.home.NotificationBean;
import com.ybm100.app.crm.bean.home.PwdFlagBean;
import com.ybm100.app.crm.bean.lzcustomer.LzRoleBean;
import com.ybm100.app.crm.bean.message.MessageReadCountBean;

import java.util.HashMap;
import java.util.List;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/19/2018 18:18
 */
public interface MainContract {

    interface IMainModel extends IBaseModel {

        /**
         * 请求通知信息
         *
         * @return
         */
        Observable<RequestBaseBean<NotificationBean>> requestNotification(HashMap<String, Object> map);

        /**
         * 是否需要强制修改密码
         */
        Observable<RequestBaseBean<PwdFlagBean>> changePwdFlag();

        /**
         * 获取灵芝问诊权限
         *
         * @return
         */
        Observable<RequestBaseBean<List<LzRoleBean>>> getRoles();
    }

    interface IMainView extends IBaseActivity {


        void renderCountSuccess(MessageReadCountBean bean);

        void changePwdFlagSuccess(PwdFlagBean bean);

        /**
         * 修改密码失败
         */
        void changePwdFlagFailed();

        /**
         * 请求通知成功
         *
         * @param bean
         */
        void onRequestNotificationSuccess(NotificationBean bean);


        void getRolesSuccess(List<LzRoleBean> roleBean);
    }

}
