package com.ybm100.app.crm.contract.home;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.lzcustomer.LzRoleBean;
import com.ybm100.app.crm.home.bean.ModuleConfig;

import java.util.List;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/19/2018 19:27
 */
public interface HomeContract {

    interface IHomeModel extends IBaseModel {

        /**
         * 获取灵芝问诊权限
         *
         * @return
         */
        Observable<RequestBaseBean<List<LzRoleBean>>> getRoles();

        /**
         * 获取首页卡片配置
         */
        Observable<RequestBaseBean<List<ModuleConfig>>> getModulesConfig();

    }

    interface IHomeView extends IBaseActivity {

        void getRolesSuccess(List<LzRoleBean> roleBean);

        void getRolesFail();

        void getModulesConfigSuccess(List<ModuleConfig> moduleConfigs);

        void getModulesConfigFail();
    }

}
