package com.ybm100.app.crm.presenter.drugstore.minedrug;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.BasicInfo;
import com.ybm100.app.crm.contract.drugstore.minedrug.BasicInfoContract;
import com.ybm100.app.crm.model.drugstore.minedrug.BasicInfoModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:04
 */
public class BasicInfoPresenter extends BasePresenter<BasicInfoContract.IBaseInfoModel, BasicInfoContract.IBaseInfoView> {

    public static BasicInfoPresenter newInstance() {
        return new BasicInfoPresenter();
    }


    public void getAuditInfo(String merchantId) {
        if (mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.getAuditInfo(merchantId).subscribe(new SimpleSuccessConsumer<RequestBaseBean<BasicInfo>>(mIView, false) {
            @Override
            public void onSuccess(RequestBaseBean<BasicInfo> drugstoreInfoBean) {

                mIView.getAuditInfo(drugstoreInfoBean);
            }
        }, new SimpleErrorConsumer(mIView)));

    }

    @Override
    protected BasicInfoContract.IBaseInfoModel getModel() {
        return BasicInfoModel.newInstance();
    }
}
