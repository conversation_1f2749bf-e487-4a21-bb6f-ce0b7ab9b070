package com.ybm100.app.crm.presenter.lzcustomer;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.lzcustomer.LzPublicDetialBean;
import com.ybm100.app.crm.contract.lzcustomer.LzPublicDetailContract;
import com.ybm100.app.crm.model.lzcustomer.LzPublicDetailModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR>
 * @date 2019/1/7
 */
public class LzPublicDetailPresenter extends BasePresenter<LzPublicDetailContract.ILzPublicDetailModel, LzPublicDetailContract.ILzPublicDetailView> {
    public static LzPublicDetailPresenter newInstance() {
        return new LzPublicDetailPresenter();
    }

    @Override
    protected LzPublicDetailContract.ILzPublicDetailModel getModel() {
        return LzPublicDetailModel.newInstance();
    }

    /**
     * 公海详情
     *
     * @param id 客户ID
     */
    public void searchOpenSeaDetail(String id) {
        if (mIView == null || mIModel == null) return;
        Disposable subscribe = mIModel.searchOpenSeaDetail(id)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean<LzPublicDetialBean>>(mIView, "") {
                    @Override
                    public void onSuccess(RequestBaseBean<LzPublicDetialBean> baseBean) {
                        if (mIView == null) return;
                        mIView.searchOpenSeaDetailSuccess(baseBean.getData());
                    }
                }, new SimpleErrorConsumer(mIView));
        mRxManager.register(subscribe);
    }

    /**
     * 认领
     *
     * @param id 客户ID
     */
    public void receive(String id) {
        if (mIView == null || mIModel == null) return;
        Disposable subscribe = mIModel.receive(id)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean>(mIView, "") {
                    @Override
                    public void accept(RequestBaseBean baseBean) throws Exception {
                        if (mIView == null) return;
                        mIView.hideWaitDialog();
                        if (baseBean.isSuccess()) {
                            mIView.receiveSuccess(baseBean);
                        } else if (!baseBean.isSuccess() && (baseBean.getErrorCode() == 405 || baseBean.getErrorCode() == 406)) {
                            mIView.receiveSuccess(baseBean);
                        } else {
                            mIView.showToast(baseBean.getErrorMsg());
                        }
                    }

                    @Override
                    public void onSuccess(RequestBaseBean baseBean) {

                    }
                }, new SimpleErrorConsumer(mIView));
        mRxManager.register(subscribe);
    }

}
