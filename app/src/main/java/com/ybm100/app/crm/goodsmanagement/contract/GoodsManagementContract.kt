package com.ybm100.app.crm.goodsmanagement.contract

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.ybm100.app.crm.goodsmanagement.bean.AvailableZone
import com.ybm100.app.crm.goodsmanagement.bean.EstimatedPriceListBean
import com.ybm100.app.crm.goodsmanagement.bean.GoodsManagementListBean
import com.ybm100.app.crm.goodsmanagement.bean.VarietyListBean
import com.ybm100.app.crm.task.bean.ShareConfirm
import io.reactivex.Observable


class GoodsManagementContract {
    interface IGoodsManagementView : IBaseActivity {
        fun onGetGoodsManagementGoodsListSuccess(data: RequestBaseBean<GoodsManagementListBean?>?, isRefresh: Boolean, isLastPage: Boolean)
        fun onGetGoodsManagementGoodsListFail()
        fun onGetVarietyListSuccess(data: RequestBaseBean<VarietyListBean?>?)
        fun onGetVarietyListFail()
        fun onCollectSuccess(data: RequestBaseBean<Any?>?, position: Int)
        fun onCollectFail()
        fun onRequestShareConfirmSuccess(data: RequestBaseBean<ShareConfirm?>?)
        fun onRequestShareConfirmFail()
        fun onGetEstimatedPricesSuccess(data: RequestBaseBean<EstimatedPriceListBean?>?)
        fun onGetEstimatedPricesFail()
    }

    interface IGoodsManagementModel : IBaseModel {
        fun getGoodsManagementGoodsList(queryMap: Map<String, String>): Observable<RequestBaseBean<GoodsManagementListBean?>?>
        fun getVarietyList(branchCode: String): Observable<RequestBaseBean<VarietyListBean?>?>
        fun collect(queryMap: Map<String, String>): Observable<RequestBaseBean<Any?>?>
        fun requestShareConfirm(queryMap: Map<String, String>): Observable<RequestBaseBean<ShareConfirm?>?>
        fun getEstimatedPrices(queryMap: Map<String, String>): Observable<RequestBaseBean<EstimatedPriceListBean?>?>
    }
}