package com.ybm100.app.crm.goodsmanagement.adapter

import android.widget.TextView
import androidx.core.content.ContextCompat
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.ybm100.app.crm.R
import com.ybm100.app.crm.goodsmanagement.bean.AvailableZone
import com.ybm100.app.crm.goodsmanagement.bean.GoodsManagementAreaListBean
import com.ybm100.app.crm.goodsmanagement.bean.Zone


class GoodsZoneFilterAdapter : BaseQuickAdapter<Zone?, BaseViewHolder>(R.layout.item_goods_management_filter) {


    override fun convert(helper: BaseViewHolder?, item: Zone?) {
        helper?.setText(R.id.rtv_content, item?.zoneName ?: "")
        if (item?.isSelect == true) {
            helper?.setTextColor(R.id.rtv_content, ContextCompat.getColor(mContext, R.color.color_00B377))
                    ?.setBackgroundColor(R.id.rtv_content, ContextCompat.getColor(mContext, R.color.color_E5F7F1))
        } else {
            helper?.setTextColor(R.id.rtv_content, ContextCompat.getColor(mContext, R.color.color_676773))
                    ?.setBackgroundColor(R.id.rtv_content, ContextCompat.getColor(mContext, R.color.color_F7F7F8))
        }
    }

    fun getSelectedZone(): Zone? {
        return data.find {
            it?.isSelect == true
        }
    }

}