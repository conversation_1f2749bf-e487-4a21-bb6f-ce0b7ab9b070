package com.ybm100.app.crm.contract.drugstore.minedrug;
import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.DrugstoreOrderBean;

import io.reactivex.Observable;
/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:09
 * 药店订单记录
 */
public interface OrderRecordContract {

    interface IOrderRecordModel extends IBaseModel {
        Observable<RequestBaseBean<DrugstoreOrderBean>> getOrderListData(int limit,
                                                                         String merchantId,
                                                                         int offset);
    }

    interface IOrderRecordView extends IBaseActivity {
        void getOrderListDataSuccess(RequestBaseBean<DrugstoreOrderBean> requestBaseBean);
    }

}
