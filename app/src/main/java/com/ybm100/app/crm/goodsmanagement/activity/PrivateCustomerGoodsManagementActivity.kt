package com.ybm100.app.crm.goodsmanagement.activity

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.viewpager.widget.ViewPager
import com.flyco.tablayout.listener.OnTabSelectListener
import com.xyy.userbehaviortracking.utils.UserBehaviorTrackingUtils
import com.xyy.utilslibrary.adapter.CommonPageAdapter
import com.ybm100.app.crm.R
import com.ybm100.app.crm.constant.Constants
import com.ybm100.app.crm.goodsmanagement.bean.Zone
import com.ybm100.app.crm.goodsmanagement.fragment.CustomerGoodsManagementFilterFragment
import com.ybm100.app.crm.goodsmanagement.fragment.GoodsManagementFragment
import com.ybm100.app.crm.ui.fragment.drugstore.minedrug.FrequentBuyCommodityFragment
import com.ybm100.app.crm.ui.fragment.drugstore.minedrug.ShoppingCommodityFragment
import kotlinx.android.synthetic.main.activity_customer_goods_management.*

class PrivateCustomerGoodsManagementActivity : BaseDrawerActivity(), CustomerGoodsManagementFilterFragment.CustomerDrawerListener {
    private var mCurrentTab: Int = 0
    private var mMerchantID: String = ""
    private var mTabTitles = arrayOf("全部商品", "常购商品", "购物车商品")
    private var mFragmentList = mutableListOf<Fragment>()
    private lateinit var mCustomerDrawerFragment: Fragment

    override fun initTransferData() {
        super.initTransferData()
        intent?.extras?.run {
            mCurrentTab = getInt(Constants.GoodsManagement.ARG_CUSTOMER_GOODS_MANAGEMENT_SELECTED_TAB, Constants.GoodsManagement.CONSTANT_CUSTOMER_GOODS_MANAGEMENT_TAB_ALL_GOODS)
            mMerchantID = getString(Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_MERCHANT_ID, "")
        }
        if (mMerchantID.isEmpty()) {
            intent?.data?.let {
                mMerchantID = it.getQueryParameter("shopid") ?: ""
                mCurrentTab =it.getQueryParameter("selectIndex")?.toIntOrNull()?:0
            }
        }
        initFragments()
    }

    override fun getContentMainLayoutID(): Int {
        return R.layout.activity_customer_goods_management
    }

    override fun initContentMain() {

        initViewPager()

        initSlidingTabLayout()

        registerListener()
    }

    private fun initSlidingTabLayout() {
        stl_tab.apply {
            setViewPager(view_pager)
        }

        stl_tab.currentTab = mCurrentTab
    }

    private fun initViewPager() {
        view_pager.apply {
            adapter = CommonPageAdapter(supportFragmentManager, mFragmentList, mTabTitles)
            offscreenPageLimit = mTabTitles.size
        }
    }

    private fun registerListener() {
        iv_back.setOnClickListener {
            finish()
        }

        iv_search.setOnClickListener {
            BaseSearchActivity.startActivity(this, Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_SEARCH, mMerchantID)

            UserBehaviorTrackingUtils.track("mc-productmgt-search")
        }

        view_pager.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrollStateChanged(p0: Int) {

            }

            override fun onPageScrolled(p0: Int, p1: Float, p2: Int) {

            }

            override fun onPageSelected(p0: Int) {
                setSearchIconVisibility(p0)
            }
        })
        stl_tab.setOnTabSelectListener(object : OnTabSelectListener {
            override fun onTabSelect(position: Int) {
                setSearchIconVisibility(position)
            }

            override fun onTabReselect(position: Int) {

            }
        })
    }

    private fun initFragments() {
        mFragmentList.add(GoodsManagementFragment.newInstance(Bundle().apply {
            putInt(Constants.GoodsManagement.ARG_FRAGMENT_TYPE, Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS)
            putString(Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_MERCHANT_ID, mMerchantID)
        }))
        mFragmentList.add(FrequentBuyCommodityFragment.newInstance(mMerchantID))
        mFragmentList.add(ShoppingCommodityFragment.newInstance(mMerchantID))
    }

    override fun getDrawerFragments(): List<Fragment> {
        mCustomerDrawerFragment = CustomerGoodsManagementFilterFragment.newInstance(mMerchantID).apply {
            setCustomerDrawerListener(this@PrivateCustomerGoodsManagementActivity)
        }
        return listOf(mCustomerDrawerFragment)
    }

    private fun setSearchIconVisibility(position: Int) {
        if (position == 0) {
            iv_search.visibility = View.VISIBLE
        } else {
            iv_search.visibility = View.GONE
        }
    }

    companion object {
        /**
         * @param activity
         * @param selectedTab 选中的tab
         */
        @JvmStatic
        fun startActivity(activity: Activity?, merchantID: String?, selectedTab: Int? = Constants.GoodsManagement.CONSTANT_CUSTOMER_GOODS_MANAGEMENT_TAB_ALL_GOODS) {
            val intent = Intent(activity, PrivateCustomerGoodsManagementActivity::class.java)
            val bundle = Bundle()

            bundle.putString(Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_MERCHANT_ID, merchantID
                    ?: "")
            bundle.putInt(Constants.GoodsManagement.ARG_CUSTOMER_GOODS_MANAGEMENT_SELECTED_TAB, selectedTab
                    ?: Constants.GoodsManagement.CONSTANT_GOODS_MANAGEMENT_TAB_MY_COLLECTION)

            intent.putExtras(bundle)
            activity?.startActivity(intent)
        }
    }

    fun syncCustomerDrawerFilterData(YBMFilterPos: Int) {
        if (::mCustomerDrawerFragment.isInitialized) {
            (mCustomerDrawerFragment as CustomerGoodsManagementFilterFragment).syncCustomerDrawerFilterData(YBMFilterPos)
        }
    }

    override fun onCustomerConfirmPressed(YBMFilterPos: Int, collectionStatusFilterPos: Int, selectedZone: Zone?) {
        closeDrawer()
        if (mFragmentList.size > 0) {
            (mFragmentList[0] as GoodsManagementFragment).onCustomerDrawerConfirmPressed(YBMFilterPos, collectionStatusFilterPos, selectedZone)
        }
    }

}