package com.ybm100.app.crm.order.bean

import com.ybm100.app.crm.bean.ISearch

/**
 * 订单列表和退单列表model
 */
class OrderBeanV2 : ISearch {
    var pageNum: Int = 0
    var pageSize: Int = 0
    var pages: Int = 0
    var currentPage: Int = 0
    var currentPageTmp: Int = 0
    var limit: Int = 0
    var offset: Int = 0
    var pageCount: Int = 0

    var total: Int = 0
    val isLastPage: Boolean = false
    val lastPage: Any? = null
    var totalMoney: Double = 0.toDouble()
    var list: List<ListBeanV2>? = null
    var orderPageList: List<ListBeanV2>? = null
    var rows: List<ListBeanV2>? = null
    var page: OrderBeanV2? = null

    override fun getDisplay(): String? {
        return null
    }

    class ListBeanV2 {
        var merchantName: String? = null // 药店名称
        var payTypeName: String? = null // 支付方式
        var imageUrl: String? = null // 商品图
        var varietyNum: Int = 0 // 商品种类数
        var statusName: String? = null // 订单状态名称
        var createTime: String? = null // 下单时间
        var money: Double = 0.toDouble() // 订单实付金额
        var payType: Int = 0
        var status: Int = 0 // 订单状态
        var merchantId: Int = 0
        var totalAmount: Double = 0.toDouble() // 订单金额
        var orderNo: String? = null
        var id: String? = null
        var appAuditStatusName: String? = null//退款状态
        var refundChannelName: String? = null//退款渠道
        var refundCreateTime: String? = null//申请时间
        var refundFee: String? = null
        var refundOrderNo: String? = null//退款单号
        var refundType: String? = null
        var refundTypeName: String? = null
        var refundChannel: String? = null
        var productAmount: String? = null
        var branchName: String? = null
        var auditState: Int? = null
        var auditProcessState: Int? = null
        var remark3: String? = null
        var preferentialList: List<OrderBean.PreferentialBean>? = null // 订单 优惠列表
    }
}
