package com.ybm100.app.crm.model.drugstore.minedrug

import com.xyy.utilslibrary.base.BaseModel
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.helper.RxHelper
import com.ybm100.app.crm.api.ApiService
import com.ybm100.app.crm.bean.drugstore.minedrugstore.InvoiceBean
import com.ybm100.app.crm.contract.drugstore.minedrug.InvoiceTypeContract
import com.ybm100.app.crm.net.RetrofitCreateHelper
import io.reactivex.Observable
import java.util.HashMap

/**
 * Created by XyyMvpYkqTemplate on 07/29/2019 11:33
 */
class InvoiceTypeModel : BaseModel(), InvoiceTypeContract.IInvoiceTypeModel {

    override fun reqInvoiceData(map: HashMap<String, Any>): Observable<RequestBaseBean<InvoiceBean>> {
        return RetrofitCreateHelper.createApi(ApiService::class.java).getInvoiceData(map)
                .compose(RxHelper.rxSchedulerHelper<RequestBaseBean<InvoiceBean>>())
    }

}
