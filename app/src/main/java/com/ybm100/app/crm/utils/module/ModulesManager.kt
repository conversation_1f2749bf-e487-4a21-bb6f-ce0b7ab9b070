package com.ybm100.app.crm.utils.module

import android.content.Context
import android.view.View
import android.view.ViewGroup
import com.ybm100.app.crm.utils.module.factory.IModuleFactory
import com.ybm100.app.crm.utils.module.module.BaseModule
import com.ybm100.app.crm.utils.module.normal.DefaultModuleGroupView

class ModulesManager(val context: Context, private val moduleFactory: IModuleFactory<out BaseModule<*>>) {

    companion object {
        fun newInstance(context: Context?, moduleFactory: IModuleFactory<out BaseModule<*>>?): ModulesManager {
            if (context == null) {
                throw IllegalArgumentException("context is null")
            }
            if (moduleFactory == null) {
                throw IllegalArgumentException("moduleFactory is null")
            }
            return ModulesManager(context, moduleFactory)
        }
    }


    var modulesListView: ModuleGroupView? = null

    fun loadView(container: ViewGroup?) {
        container?.let {
            if (modulesListView == null) {
                modulesListView = DefaultModuleGroupView(context)
            }

            modulesListView?.initView(moduleFactory)


            if (modulesListView is View) {
                container.addView(modulesListView as View)
            } else {
                throw  IllegalArgumentException("ModuleGroupView must be View")
            }
        }
    }

    fun setModuleGroupView(moduleGroupView: ModuleGroupView?) {
        this.modulesListView = moduleGroupView
    }

    fun loadData(IModuleItems: List<IModuleItem>?) {
        IModuleItems?.let {
            modulesListView?.setModulesData(it)
        }
    }

    fun refresh() {
        modulesListView?.refresh()
    }

    fun refresh(typeId: Int) {
        modulesListView?.refresh(typeId)
    }

    fun getCurrentItems(): List<IModuleItem> {
        return modulesListView?.getCurrentItems()?: emptyList()
    }

    fun hasModule(typeId: Int): Boolean {
        return modulesListView?.hasModule(typeId) ?: false
    }


    interface ModuleGroupView {
        fun initView(moduleFactory: IModuleFactory<out BaseModule<*>>)

        fun setModulesData(moduleItems: List<IModuleItem>)

        fun notifyDataSetChanged()

        fun refresh()

        fun refresh(typeId: Int)

        fun hasModule(typeId: Int): Boolean

        fun getCurrentItems(): List<IModuleItem>

    }

}