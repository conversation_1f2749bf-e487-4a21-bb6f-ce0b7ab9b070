package com.ybm100.app.crm.presenter.drugstore;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.PublicCustomerDetailBean;
import com.ybm100.app.crm.contract.drugstore.PublicCustomerDetailContract;
import com.ybm100.app.crm.model.drugstore.BaseInfoPublicCustomerDetailModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

import io.reactivex.disposables.Disposable;

/**
 * 基本信息未注册Presenter
 */
public class YBMBaseInfoPublicCustomerDetailPresenter extends BasePresenter<PublicCustomerDetailContract.IBaseInfoPublicCustomerDetailModel, PublicCustomerDetailContract.IBaseInfoPublicCustomerDetailView> {
    public static YBMBaseInfoPublicCustomerDetailPresenter newInstance() {
        return new YBMBaseInfoPublicCustomerDetailPresenter();
    }

    @Override
    protected BaseInfoPublicCustomerDetailModel getModel() {
        return BaseInfoPublicCustomerDetailModel.newInstance();
    }

    /**
     * 公海详情
     *
     * @param id 客户ID
     */
    public void searchOpenSeaDetail(String id) {
        if (mIView == null || mIModel == null) return;
        Disposable subscribe = mIModel.searchOpenSeaDetail(id)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean<PublicCustomerDetailBean>>(mIView, "") {
                    @Override
                    public void onSuccess(RequestBaseBean<PublicCustomerDetailBean> baseBean) {
                        if (mIView == null) return;
                        mIView.searchOpenSeaDetailSuccess(baseBean);
                    }
                }, new SimpleErrorConsumer(mIView));
        mRxManager.register(subscribe);
    }

    /**
     * 认领
     *
     * @param id 客户ID
     */
    public void receive(String id) {
        // do nothing
    }
}
