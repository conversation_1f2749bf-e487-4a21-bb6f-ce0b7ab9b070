package com.ybm100.app.crm.utils.module.factory

import android.content.Context
import com.ybm100.app.crm.utils.module.ExtraPropertyCallback
import com.ybm100.app.crm.utils.module.IModuleItem
import com.ybm100.app.crm.utils.module.module.BaseModule
import com.ybm100.app.crm.utils.module.module.EmptyModule

abstract class IModuleFactory<T>(val context: Context,
                                 private val refreshCallback: BaseModule.OnRefreshCallback,
                                 private val extraPropertyCallback: ExtraPropertyCallback?) {

    private val supportMap: Map<Int, Class<out BaseModule<*>>> = initSupportMap()

    fun isSupport(typeId: Int): Boolean {
        return supportMap.containsKey(typeId)
    }

    fun build(typeId: Int, moduleId: Int): BaseModule<*> {
        return create(supportMap[typeId] ?: getEmptyModule()).also {
            it.typeId = typeId
            it.moduleId = moduleId
            it.refreshCallback = refreshCallback
            it.extraPropertyCallback = extraPropertyCallback
        }
    }

    private fun create(clazz: Class<out BaseModule<*>>): BaseModule<*> {
        return clazz.getDeclaredConstructor(Context::class.java).newInstance(context)
    }

    fun buildAll(IModuleItems: List<IModuleItem>): List<BaseModule<*>> {
        return IModuleItems.map { item ->
            build(item.getLocalTypeId(), item.getLocalModuleId())
        }
    }

    fun getEmptyModule(): Class<out BaseModule<*>> {
        return EmptyModule::class.java
    }

    abstract fun initSupportMap(): Map<Int, Class<out BaseModule<*>>>

}