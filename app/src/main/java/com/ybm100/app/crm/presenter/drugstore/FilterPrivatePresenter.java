package com.ybm100.app.crm.presenter.drugstore;


import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.FilterPrivateBean;
import com.ybm100.app.crm.contract.drugstore.FilterPrivateContract;
import com.ybm100.app.crm.model.drugstore.FilterPrivateModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

/**
 * @author: zcj
 * @time:2020/4/2. Description:
 */
public class FilterPrivatePresenter extends BasePresenter<FilterPrivateContract.IFilterPrivateModel, FilterPrivateContract.IFilterPrivateView> {
    @Override
    protected FilterPrivateContract.IFilterPrivateModel getModel() {
        return FilterPrivateModel.newInstance();
    }

    public static FilterPrivatePresenter newInstance() {
        return new FilterPrivatePresenter();
    }

    public void getOtherFilterItems() {
        if (mIModel == null || mIView == null) return;
        mRxManager.register(mIModel.getOtherFilterItems().subscribe(new SimpleSuccessConsumer<RequestBaseBean<FilterPrivateBean>>(mIView) {
            @Override
            public void onSuccess(RequestBaseBean<FilterPrivateBean> baseBean) {
                if (mIView == null) return;
                mIView.getOtherFilterItemsSuccess(baseBean);
            }

            @Override
            public void onFailure(int errorCode) {
                super.onFailure(errorCode);
            }
        }, new SimpleErrorConsumer(mIView)));
    }
}
