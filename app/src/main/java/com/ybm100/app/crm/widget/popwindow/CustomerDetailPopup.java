package com.ybm100.app.crm.widget.popwindow;

import android.content.Context;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.Gravity;
import android.view.View;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.xyy.common.widget.DefaultItemDecoration;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.ui.adapter.popwindow.CustomerPopDateAdapter;

import java.util.List;

import razerdp.basepopup.BasePopupWindow;

/**
 * @author: zcj
 * @time:2020/3/29. Description:
 */
public class CustomerDetailPopup extends BasePopupWindow {
    private List<String> popList;
    private CustomerPopDateAdapter popDateAdapter;
    private int mBackgroundDrawable;

    public CustomerDetailPopup(Context context) {
        super(context);
        initRecyclerView();
        setAlignBackground(false);//背景是否对齐到PopupWindow
        setOutSideDismiss(true);// 点击popupwindow背景部分不隐藏
    }

    public CustomerDetailPopup(Context context, List<String> popList) {
        super(context);
        this.popList = popList;
        initRecyclerView();
        setAlignBackground(false);//背景是否对齐到PopupWindow
        setOutSideDismiss(true);// 点击popupwindow背景部分不隐藏
        setPopupGravity(Gravity.BOTTOM | Gravity.START);
    }

    public void setPopList(List<String> popList) {
        this.popList = popList;
        initRecyclerView();
    }

    public void setBackgroundDrawable(int backgroundDrawable) {
        this.mBackgroundDrawable = backgroundDrawable;
        initRecyclerView();
    }

    private void initRecyclerView() {
        RecyclerView rvPopDate = findViewById(R.id.rv_pop_date);
        if (mBackgroundDrawable != 0) {
            rvPopDate.setBackgroundResource(mBackgroundDrawable);
        }
        rvPopDate.setLayoutManager(new LinearLayoutManager(getContext()));
        rvPopDate.addItemDecoration(new DefaultItemDecoration(getContext()));
        popDateAdapter = new CustomerPopDateAdapter(R.layout.item_pop_date, popList);
        popDateAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                if (clickListener != null) {
                    clickListener.onItemClick(position);
                }
                dismiss();
            }
        });
        rvPopDate.setAdapter(popDateAdapter);
    }

    public String getItem(int position) {
        return popList.get(position);
    }

    @Override
    public View onCreateContentView() {
        return createPopupById(R.layout.list_popup_date);
    }

    @Override
    public void showPopupWindow(View v) {
        super.showPopupWindow(v);
    }

    @Override
    public void showPopupWindow(int x, int y) {
        super.showPopupWindow(x, y);
    }

    private HomeDatePopup.OnPopItemClickListener clickListener;

    public void setOnPopItemClickListener(HomeDatePopup.OnPopItemClickListener clickListener) {
        this.clickListener = clickListener;
    }

    public interface OnPopItemClickListener {
        void onItemClick(int position);
    }

    /**
     * 设置新的数据源
     *
     * @param popList
     */
    public void setNewData(List<String> popList) {
        this.popList.clear();
        this.popList.addAll(popList);
        if (popDateAdapter != null) {
            popDateAdapter.setNewData(this.popList);
        }
    }
}