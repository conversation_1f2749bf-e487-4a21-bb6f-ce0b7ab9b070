package com.ybm100.app.crm.model.drugstore.minedrug

import com.xyy.utilslibrary.base.BaseModel
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.helper.RxHelper
import com.ybm100.app.crm.api.ApiService
import com.ybm100.app.crm.bean.drugstore.minedrugstore.ApplyDetailBean
import com.ybm100.app.crm.contract.drugstore.minedrug.ApplyDetailContract
import com.ybm100.app.crm.net.RetrofitCreateHelper
import io.reactivex.Observable
import java.util.*

/**
 * Created by XyyMvpYkqTemplate on 07/30/2019 18:07
 */
class ApplyDetailModel : BaseModel(), ApplyDetailContract.IApplyDetailModel {

    override fun reqInvoiceDetail(map: HashMap<String, Any>): Observable<RequestBaseBean<ApplyDetailBean>> {
        return RetrofitCreateHelper.createApi(ApiService::class.java).getInvoiceDetail(map)
                .compose(RxHelper.rxSchedulerHelper<RequestBaseBean<ApplyDetailBean>>())
    }

}
