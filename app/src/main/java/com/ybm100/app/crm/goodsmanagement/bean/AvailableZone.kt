package com.ybm100.app.crm.goodsmanagement.bean

import androidx.annotation.Keep

@Keep
data class AvailableZone(val rows: List<Zone?>?)


@Keep
//isSelect是本地变量
data class Zone(var zoneId: String?, var zoneName: String?, var isSelect: Boolean = false){
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as Zone

        if (zoneId != other.zoneId) return false

        return true
    }

    override fun hashCode(): Int {
        return zoneId?.hashCode() ?: 0
    }
}

