package com.ybm100.app.crm.contract.license;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.license.DeliveryAddressBean;

import java.util.List;

import io.reactivex.Observable;

public interface LicenseSelectAreaContract {
    interface ILicenseSelectAreaModel extends IBaseModel {
        Observable<RequestBaseBean<List<DeliveryAddressBean>>> getAddress(String areaCode);
    }

    interface ILicenseSelectAreaView extends IBaseActivity {
        void getAddressSuccess(RequestBaseBean<List<DeliveryAddressBean>> requestBaseBean);
    }

}
