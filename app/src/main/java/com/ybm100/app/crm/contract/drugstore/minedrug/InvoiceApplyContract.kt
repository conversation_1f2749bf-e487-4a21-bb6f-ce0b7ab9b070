package com.ybm100.app.crm.contract.drugstore.minedrug

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.ybm100.app.crm.bean.drugstore.minedrugstore.ApplyInitBean
import com.ybm100.app.crm.order.bean.AptitudeInitBean
import io.reactivex.Observable
import java.util.*

/**
 * Created by XyyMvpYkqTemplate on 07/30/2019 18:32
 */
interface InvoiceApplyContract {

    interface IInvoiceApplyModel : IBaseModel {
        /**
         * 发票类型申请页初始化
         */
        fun reqInvoiceInit(map: HashMap<String, Any>): Observable<RequestBaseBean<ApplyInitBean>>

        /**
         * 提交发票类型申请
         */
        fun submitInvoiceApply(map: HashMap<String, Any>): Observable<RequestBaseBean<*>>

        /**
         * 资质变更前的判断
         */
        fun initLicenseAuditDetail(merchantId: String, from: String): Observable<RequestBaseBean<AptitudeInitBean>>
    }

    interface IInvoiceApplyView : IBaseActivity {
        fun reqInvoiceInitSuccess(data: ApplyInitBean?)
        fun submitInvoiceApplySuccess()
        fun initLicenseAuditDetailSuccess(requestBaseBean: RequestBaseBean<AptitudeInitBean>)
    }

}
