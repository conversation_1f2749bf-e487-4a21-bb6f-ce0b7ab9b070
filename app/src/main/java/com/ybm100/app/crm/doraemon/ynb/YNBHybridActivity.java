package com.ybm100.app.crm.doraemon.ynb;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.google.gson.Gson;
import com.just.ynbweb.DeviceInfo;
import com.just.ynbweb.WebViewClient;
import com.just.ynbweb.YNBJsCallBack;
import com.just.ynbweb.bean.AppInfo;
import com.just.ynbweb.bean.DataBean;
import com.just.ynbweb.bean.ImgInfo;
import com.just.ynbweb.bean.LocationInfo;
import com.just.ynbweb.bean.ShareMsgBean;
import com.just.ynbweb.bean.UserInfo;
import com.tbruyelle.rxpermissions2.RxPermissions;
import com.xyy.common.util.DeviceUtils;
import com.xyy.common.util.ToastUtils;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.utilslibrary.utils.AppUtils;
import com.xyy.utilslibrary.utils.BitmapUtils;
import com.xyy.utilslibrary.utils.ImageUtils;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.api.ApiService;
import com.ybm100.app.crm.bean.ImageCallBackBean;
import com.ybm100.app.crm.bean.UploadInfos;
import com.ybm100.app.crm.bean.YNBPhotoBean;
import com.ybm100.app.crm.bean.user.UserInfoBean;
import com.ybm100.app.crm.utils.GsonUtils;
import com.ybm100.app.crm.net.RetrofitCreateHelper;
import com.ybm100.app.crm.order.activity.AlbumActivity;
import com.ybm100.app.crm.order.photo.PhotoBean;
import com.ybm100.app.crm.ui.activity.lbs.LocationManager;
import com.ybm100.app.crm.utils.AppFileUtils;
import com.ybm100.app.crm.utils.BitmapUtil;
import com.ybm100.app.crm.utils.CameraUtils;
import com.ybm100.app.crm.utils.ShareHelper;
import com.ybm100.app.crm.utils.ShareHelper.Companion;
import com.ybm100.app.crm.utils.SharedPrefManager;

import org.jetbrains.annotations.NotNull;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import butterknife.BindView;
import io.reactivex.functions.Consumer;
import okhttp3.MediaType;
import okhttp3.RequestBody;


/**
 * @author: zcj
 * @time:2020/3/19. Description:
 */
public class YNBHybridActivity extends BaseYNBWebActivity {
    private LocationManager.LocationListener locationListener;
    private LocationInfo locationInfo;
    private YNBJsCallBack callBack;
    private YNBJsCallBack multipleChooseImageCallBack;
    private YNBJsCallBack uploadCallBack;
    private YNBJsCallBack savePhotoCallBack;
    private String url = "";
    private String title = "";
    private String rightText;
    private String rightIcon;
    @BindView(R.id.iv_back)
    ImageView ivBack;
    @BindView(R.id.iv_close)
    ImageView ivClose;
    @BindView(R.id.tv_toolbar_title)
    TextView tvTitle;
    @BindView(R.id.tv_rightText)
    TextView tvRightView;
    @BindView(R.id.iv_rightBt)
    ImageView ivRightView;
    private int count;
    private String mShareBase64Str;
    private final static int REQUEST_WRITE = 9000;

    public static void jumpYnb(Activity activity, String url, LocationInfo locationInfo) {
        Intent intentPost = new Intent(activity, YNBHybridActivity.class);
        Bundle bundle = new Bundle();
        bundle.putString("url", url);
        if (null != locationInfo) {
            bundle.putParcelable("locationInfo", locationInfo);
        }
        intentPost.putExtras(bundle);
        activity.startActivity(intentPost);
    }

    @Override
    protected void uesWebTitle(String title) {//使用默认标题
        setTitle(title);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        this.decodeDataFromIntent();
        super.onCreate(savedInstanceState);
        ivBack.setOnClickListener(v -> {
            if (!getAgentWeb().back()) {
                finish();
            }
        });
        ivClose.setOnClickListener(v -> finish());
    }

    private void decodeDataFromIntent() {
        try {

            Intent it = getIntent();
            Uri uri = it.getData();
            if (null != uri) {
                url = Uri.decode(uri.getQueryParameter("url"));
            } else {
                url = it.getStringExtra("url");
                locationInfo = it.getParcelableExtra("locationInfo");
            }
            Log.e("guan", url);
            if (null == locationInfo) {
                locationInfo = new LocationInfo();
            }
        } catch (Exception ignore) {

        }
    }

    @NonNull
    @Override
    protected ViewGroup getAgentWebParent() {
        return this.findViewById(R.id.linearLayout);
    }

    @Nullable
    @Override
    protected String getUrl() {
        return url;
    }

    @Override
    public UserInfo getCurrentUserInfo() {
        UserInfoBean infoBean = SharedPrefManager.getInstance().getUserInfo();
        UserInfo info = new UserInfo();
        info.setRealName(infoBean.getRealName());
        info.setRoleType(infoBean.getRoleType());
        info.setToken(infoBean.getToken());
        info.setUserId(infoBean.getSysUserId());
        info.setUserName(infoBean.getName());
        return info;
    }

    @Override
    public void getLocation(@NotNull YNBJsCallBack callBack) {
        if (locationInfo == null || TextUtils.isEmpty(locationInfo.getLatitude()) || TextUtils.isEmpty(locationInfo.getLongitude())) {
            locationListener = bd -> {
                locationInfo = new LocationInfo();
                locationInfo.setLatitude(String.valueOf(bd.getLatitude()));
                locationInfo.setLongitude(String.valueOf(bd.getLongitude()));
                callBack.doCallBackWithParam(new Gson().toJson(locationInfo));
            };
            LocationManager.getInstance().locationPermissions(this, locationListener,
                    true, () -> callBack.doCallBackWithParam(""));
        } else {
            callBack.doCallBackWithParam(new Gson().toJson(locationInfo));
        }
    }

    @Override
    public DeviceInfo getDeviceInfo() {
        DeviceInfo info = new DeviceInfo();
        info.setOsVersion(Build.VERSION.RELEASE);
        info.setOs("Android");
        info.setModel(Build.MODEL);
        info.setDeviceId(DeviceUtils.getXyyDeviceUid());
        return info;
    }

    @Override
    public AppInfo getAppInfo() {
        AppInfo info = new AppInfo();
        info.setPackageName(AppUtils.getAppPackageName());
        info.setVersion(AppUtils.getAppVersionName(this));
        info.setYNBVersion("0.0.6");
        return info;
    }

    @SuppressLint("CheckResult")
    @Override
    public void chooseImage(String type, YNBJsCallBack ynbJsCallBack) {
        this.callBack = ynbJsCallBack;
        RxPermissions rxPermissions = new RxPermissions(this); // where this is an Activity instance
        rxPermissions.request(
                android.Manifest.permission.READ_EXTERNAL_STORAGE,
                android.Manifest.permission.WRITE_EXTERNAL_STORAGE,
                android.Manifest.permission.CAMERA
        ).subscribe(new Consumer<Boolean>() {
            @Override
            public void accept(Boolean granted) throws Exception {
                if (granted) { // 在android 6.0之前会默认返回true
                    //获取权限
                    if ("gallery".equals(type)) {
                        Intent intent = new Intent(YNBHybridActivity.this, AlbumActivity.class);
                        //设置最大选择数量
                        intent.putExtra(AlbumActivity.MAX_PIECE, 1);
                        startActivityForResult(intent, CameraUtils.REQUEST_GALLERY);
                    } else {
                        CameraUtils.openCamera(YNBHybridActivity.this);
                    }
                } else {
                    // 未获取权限
                    Toast.makeText(YNBHybridActivity.this, "您没有授权该权限，请在设置中打开授权", Toast.LENGTH_LONG).show();
                }
            }
        }, new Consumer<Throwable>() {
            @Override
            public void accept(Throwable throwable) throws Exception {

            }
        });
    }

    @Override
    public void setTitle(String title) {
        if (!TextUtils.isEmpty(title)) {
            this.title = title;
            setTitleBar("");
        }
    }

    @Override
    public void setNavRightButton(String type, String text, String icon) {
        rightIcon = icon;
        rightText = text;
        setTitleBar(type);
    }

    private void setTitleBar(String type) {
        if ("text".equals(type)) {
            tvRightView.setText(rightText);
            tvRightView.setTextColor(ContextCompat.getColor(this, R.color.text_color_35C561));
            ivRightView.setVisibility(View.GONE);
        } else if ("base64".equals(type)) {
            Bitmap bitmap = ImageUtils.stringToBitmapByUrlEncode(rightIcon);
            tvRightView.setVisibility(View.GONE);
            ivRightView.setImageBitmap(bitmap);
        }
        tvTitle.setText(title);
    }

    @Override
    public void resetNavRightButton() {
        ivRightView.setVisibility(View.GONE);
        tvRightView.setVisibility(View.GONE);
    }

    @Override
    public void closeWebView() {
        finish();
    }

    @Override
    public void openWebView(String s, boolean b) {
        YNBHybridActivity.jumpYnb(this, s, locationInfo);
        if (b) {
            finish();
        }
    }

    @Override
    public void multipleChooseImage(String type, int count, YNBJsCallBack ynbJsCallBack) {
        this.multipleChooseImageCallBack = ynbJsCallBack;
        if ("gallery".equals(type)) {
            Intent intent = new Intent(YNBHybridActivity.this, AlbumActivity.class);
            //设置最大选择数量
            intent.putExtra(AlbumActivity.MAX_PIECE, count);
            startActivityForResult(intent, CameraUtils.REQUEST_GALLERY_MULTIPLE_CHOOSE);
        } else {
            CameraUtils.openCameraMultiple(YNBHybridActivity.this);
        }
    }

    @Override
    public void uploadImage(ArrayList<String> arrayList, YNBJsCallBack ynbJsCallBack) {
        this.uploadCallBack = ynbJsCallBack;
        if (arrayList != null) {
            newUrls.clear();
            count = arrayList.size();
            for (int i = 0; i < arrayList.size(); i++) {
                File file = new File(formatUrl(arrayList.get(i)));
                if (file.exists()) {
                    String path = getCacheDir().getAbsolutePath() + "/tx_" + System.currentTimeMillis() + ".png";
                    BitmapUtil.compressFile(file.getAbsolutePath(), path);
                    uploadImage(path);
                } else {
                    ToastUtils.showLongSafe("图片存储失败");
                }
            }
        } else {
            ToastUtils.showLongSafe("请选择图片");
        }
    }


    @Override
    public void savePhotoToAlbums(String s, YNBJsCallBack ynbJsCallBack) {
        savePhotoCallBack = ynbJsCallBack;
        if (!TextUtils.isEmpty(s)) {
            YNBPhotoBean photoBean = GsonUtils.fromJson(s, YNBPhotoBean.class);
            mShareBase64Str = photoBean.getPhoto();
            checkPermission(mShareBase64Str);
        } else {
            DataBean dataBean = new DataBean("图片不存在", false);
            ynbJsCallBack.doCallBackWithParam(new Gson().toJson(dataBean));
        }

    }

    @Override
    public void downloadFile(String url, String fileType, YNBJsCallBack ynbJsCallBack) {
        grantedPermissions(new OnGrantedNotify() {
            @Override
            public void granted() {
                if (fileType.equals("1") || fileType.equals("2")) {
                    Glide.with(AppUtils.getContext())
                            .asBitmap()   //强制转换Bitmap
                            .load(url)
                            .into(new SimpleTarget<Bitmap>() {
                                @Override
                                public void onResourceReady(@NonNull Bitmap resource, @Nullable Transition<? super Bitmap> transition) {
                                    String locationId = saveImageToGallery(resource, fileType.equals("1") ? ".png" : (fileType.equals("2") ? ".jpg" : ""));
                                    ImageCallBackBean imageCallBackBean = new ImageCallBackBean();
                                    imageCallBackBean.setLocationId(locationId);
                                    ynbJsCallBack.doCallBackWithParam(new Gson().toJson(imageCallBackBean));
                                }
                            });
                } else {
                    ImageCallBackBean imageCallBackBean = new ImageCallBackBean();
                    imageCallBackBean.setErrorMsg("类型暂不支持：" + fileType);
                    ynbJsCallBack.doCallBackWithParam(new Gson().toJson(imageCallBackBean));
                }
            }
        }, android.Manifest.permission.READ_EXTERNAL_STORAGE, android.Manifest.permission.WRITE_EXTERNAL_STORAGE);
    }

    @Override
    public void shareForWeChat(String type, String mediaType, ShareMsgBean.MediaMessage
            mediaMessage, YNBJsCallBack ynbJsCallBack) {
        Companion.SocialMedia targetSharePlatform = null;
        switch (type) {
            case "Friends":
                targetSharePlatform = Companion.SocialMedia.PLATFORM_WECHAT;
                break;
            case "Circle":
                targetSharePlatform = Companion.SocialMedia.PLATFORM_WECHAT_CIRCLE;
                break;
        }
        switch (mediaType) {
            case "Text":
                shareText(targetSharePlatform, mediaMessage.getText(), ynbJsCallBack);
                break;
            case "Image":
                if (!TextUtils.isEmpty(mediaMessage.getImageFileId())) {
                    String thumbImgUrl = mediaMessage.getThumbFileId();
                    String imageUrl = mediaMessage.getImageFileId();
                    shareImage(targetSharePlatform, BitmapUtil.compressFile(imageUrl), BitmapUtil.compressFile(thumbImgUrl), ynbJsCallBack);
                } else {
                    Bitmap imgUrl = ImageUtils.stringToBitmapByUrlEncode(mediaMessage.getImageData());
                    Bitmap thumbImgUrl = ImageUtils.stringToBitmapByUrlEncode(mediaMessage.getThumbData());
                    shareImage(targetSharePlatform, imgUrl, thumbImgUrl, ynbJsCallBack);
                }
                break;
            case "Webpage":
                try {
                    if (!TextUtils.isEmpty(mediaMessage.getThumbFileId())) {
                        String thumbImgUrl = mediaMessage.getThumbFileId();
                        shareWeb(targetSharePlatform, URLDecoder.decode(mediaMessage.getWebpageUrl(), "UTF-8"), mediaMessage.getTitle(),
                                mediaMessage.getDescription(), thumbImgUrl, true, ynbJsCallBack);
                    } else {
                        shareWeb(targetSharePlatform, URLDecoder.decode(mediaMessage.getWebpageUrl(), "UTF-8"), mediaMessage.getTitle(),
                                mediaMessage.getDescription(), mediaMessage.getThumbData(), false, ynbJsCallBack);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                break;
        }
    }

    private void shareText(Companion.SocialMedia targetSharePlatform, String text, YNBJsCallBack ynbJsCallBack) {
        if (text == null) {
            ToastUtils.showShort("分享的文本没有找到，请重试！");
            return;
        }
        ShareHelper.Companion.shareText(this, targetSharePlatform, text, new ShareHelper.ShareCallback() {
            @Override
            public void onCompleted(@org.jetbrains.annotations.Nullable String s) {
                DataBean dataBean = new DataBean(null, true);
                ynbJsCallBack.doCallBackWithParam(new Gson().toJson(dataBean));
            }

            @Override
            public void onError(@org.jetbrains.annotations.Nullable String s, @org.jetbrains.annotations.Nullable String s1) {
                DataBean dataBean = new DataBean(s, false);
                ynbJsCallBack.doCallBackWithParam(new Gson().toJson(dataBean));
            }

            @Override
            public void onCancel(@org.jetbrains.annotations.Nullable String s) {
                DataBean dataBean = new DataBean(null, true);
                ynbJsCallBack.doCallBackWithParam(new Gson().toJson(dataBean));
            }

            @Override
            public void onStart(@org.jetbrains.annotations.Nullable String s) {

            }
        });
    }

    private void shareImage(Companion.SocialMedia targetSharePlatform, Bitmap imgUrl, Bitmap thumbImgUrl, YNBJsCallBack ynbJsCallBack) {
        if (targetSharePlatform != null) {
            ShareHelper.Companion.shareImage(this, targetSharePlatform, imgUrl, thumbImgUrl, new ShareHelper.ShareCallback() {
                @Override
                public void onCompleted(@org.jetbrains.annotations.Nullable String s) {
                    DataBean dataBean = new DataBean(null, true);
                    ynbJsCallBack.doCallBackWithParam(new Gson().toJson(dataBean));
                }

                @Override
                public void onError(@org.jetbrains.annotations.Nullable String s, @org.jetbrains.annotations.Nullable String s1) {
                    DataBean dataBean = new DataBean(s, false);
                    ynbJsCallBack.doCallBackWithParam(new Gson().toJson(dataBean));
                }

                @Override
                public void onCancel(@org.jetbrains.annotations.Nullable String s) {
                    DataBean dataBean = new DataBean(null, true);
                    ynbJsCallBack.doCallBackWithParam(new Gson().toJson(dataBean));
                }

                @Override
                public void onStart(@org.jetbrains.annotations.Nullable String s) {

                }
            });
        }
    }

    private void shareWeb(Companion.SocialMedia platformName, String shareUrl, String title, String description, String thumbImgUrl, boolean isFile, YNBJsCallBack ynbJsCallBack) {
        if (isFile) {
            ShareHelper.Companion.shareUrlByFileThumb(this, platformName, shareUrl, title, description, new File(thumbImgUrl), new ShareHelper.ShareCallback() {
                @Override
                public void onCompleted(@org.jetbrains.annotations.Nullable String s) {
                    DataBean dataBean = new DataBean(null, true);
                    ynbJsCallBack.doCallBackWithParam(new Gson().toJson(dataBean));
                }

                @Override
                public void onError(@org.jetbrains.annotations.Nullable String s, @org.jetbrains.annotations.Nullable String s1) {
                    DataBean dataBean = new DataBean(s, false);
                    ynbJsCallBack.doCallBackWithParam(new Gson().toJson(dataBean));
                }

                @Override
                public void onCancel(@org.jetbrains.annotations.Nullable String s) {
                    DataBean dataBean = new DataBean(null, true);
                    ynbJsCallBack.doCallBackWithParam(new Gson().toJson(dataBean));
                }

                @Override
                public void onStart(@org.jetbrains.annotations.Nullable String s) {

                }
            });
        } else {
            ShareHelper.Companion.shareUrlByUrlThumb(this, platformName, shareUrl, title, description, thumbImgUrl, new ShareHelper.ShareCallback() {
                @Override
                public void onCompleted(@org.jetbrains.annotations.Nullable String s) {
                    DataBean dataBean = new DataBean(null, true);
                    ynbJsCallBack.doCallBackWithParam(new Gson().toJson(dataBean));
                }

                @Override
                public void onError(@org.jetbrains.annotations.Nullable String s, @org.jetbrains.annotations.Nullable String s1) {
                    DataBean dataBean = new DataBean(s, false);
                    ynbJsCallBack.doCallBackWithParam(new Gson().toJson(dataBean));
                }

                @Override
                public void onCancel(@org.jetbrains.annotations.Nullable String s) {
                    DataBean dataBean = new DataBean(null, true);
                    ynbJsCallBack.doCallBackWithParam(new Gson().toJson(dataBean));
                }

                @Override
                public void onStart(@org.jetbrains.annotations.Nullable String s) {

                }
            });
        }

    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_WRITE && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
            String result = saveImageToGallery(mShareBase64Str, ".png");
            if (result == null || result.isEmpty()) {
                ToastUtils.showShort("保存失败");
                DataBean dataBean = new DataBean("保存失败", false);
                savePhotoCallBack.doCallBackWithParam(new Gson().toJson(dataBean));
                return;
            }
            ToastUtils.showShort("已保存到本地相册");
            DataBean dataBean = new DataBean(null, true);
            savePhotoCallBack.doCallBackWithParam(new Gson().toJson(dataBean));
        } else {
            ToastUtils.showShort("已拒绝SD卡读写操作，无法保存照片到本地");
        }
    }

    ArrayList<String> newUrls = new ArrayList<>();

    @SuppressLint("CheckResult")
    private void uploadImage(String imgPath) {
        Map<String, RequestBody> map = new HashMap<>();
        if (TextUtils.isEmpty(imgPath)) {
            ToastUtils.showLong("上传图片异常");
            return;
        }
        File file = new File(imgPath);
        RequestBody requestBody = RequestBody.create(MediaType.parse("multipart/form-data"), file);
        map.put("file\"; filename=\"" + file.getName(), requestBody);
        RetrofitCreateHelper.createH5Api(ApiService.class).uploadImagesToH5(map)
                .compose(RxHelper.rxSchedulerHelper())
                .subscribe(bean -> {
                    if ("success".equals(bean.getStatus()) && bean.getFileName() != null) {
                        newUrls.add(bean.getFileName().get(0));
                        if (uploadCallBack != null && newUrls.size() == count) {
                            UploadInfos uploadInfos = new UploadInfos();
                            uploadInfos.setPhotoUrls(newUrls);
                            uploadCallBack.doCallBackWithParam(new Gson().toJson(uploadInfos));
                        }
                    } else {
                        ToastUtils.showLong("图片上传失败请重试");
                        newUrls.add("");
                        if (uploadCallBack != null && newUrls.size() == count) {
                            UploadInfos uploadInfos = new UploadInfos();
                            uploadInfos.setPhotoUrls(newUrls);
                            uploadCallBack.doCallBackWithParam(new Gson().toJson(uploadInfos));
                        }
                    }
                }, throwable -> {
                    if (throwable != null) {
                        ToastUtils.showLong(throwable.getMessage());
                    }
                });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        File file = null;
        if (requestCode == CameraUtils.REQUEST_CAMERA) {
            file = new File(SharedPrefManager.getInstance().getCameraImaFilePath());
            if (file.exists()) {
                file = BitmapUtils.compressQuality(file);
                String base64 = AppFileUtils.file2Base64(file);
                ImgInfo info = new ImgInfo();
                info.setPhoto(base64);
                if (callBack != null) {
                    callBack.doCallBackWithParam(new Gson().toJson(info));
                }
            } else {
                ToastUtils.showLong("文件不存在或已损坏");
            }
        } else if (requestCode == CameraUtils.REQUEST_GALLERY) {
            if (data == null || data.getExtras() == null) {
                return;
            }
            List<PhotoBean> result = data.getParcelableArrayListExtra(AlbumActivity.EXTRA_RESULT);
            if (result.size() > 0) {
                File urlFile = new File(result.get(0).path);
                Uri uri = Uri.fromFile(urlFile);
                file = CameraUtils.handleImageOn19(uri, this);
            }
            if (file != null && file.exists()) {
                file = BitmapUtils.compressQuality(file);
                String base64 = AppFileUtils.file2Base64(file);
                ImgInfo info = new ImgInfo();
                info.setPhoto(base64);
                if (callBack != null) {
                    callBack.doCallBackWithParam(new Gson().toJson(info));
                }
            } else {
                ToastUtils.showLong("文件不存在或已损坏");
            }
        } else if (requestCode == CameraUtils.REQUEST_CAMERA_MULTIPLE_CHOOSE) {
            file = new File(SharedPrefManager.getInstance().getCameraImaFilePath());
            if (file.exists()) {
                UploadInfos infos = new UploadInfos();
                ArrayList<String> str = new ArrayList<>();
                str.add(getFormatUrl(file.getPath()));
                infos.setPhotoInfos(str);
                if (multipleChooseImageCallBack != null) {
                    multipleChooseImageCallBack.doCallBackWithParam(new Gson().toJson(infos));
                }
            } else {
                ToastUtils.showLong("文件不存在或已损坏");
            }
        } else if (requestCode == CameraUtils.REQUEST_GALLERY_MULTIPLE_CHOOSE) {
            if (data == null || data.getExtras() == null) {
                return;
            }
            List<PhotoBean> result = data.getParcelableArrayListExtra(AlbumActivity.EXTRA_RESULT);
            ArrayList<String> pathInfos = new ArrayList<>();
            for (int i = 0; i < result.size(); i++) {
                pathInfos.add(getFormatUrl(result.get(i).path));
            }
            UploadInfos infos = new UploadInfos();
            infos.setPhotoInfos(pathInfos);
            if (multipleChooseImageCallBack != null) {
                multipleChooseImageCallBack.doCallBackWithParam(new Gson().toJson(infos));
            }
        }

    }

    private String getFormatUrl(String url) {
        return "ynb-media://client?imageUrl=" + url;
    }

    private String formatUrl(String url) {
        if (url.startsWith("ynb-media://client?imageUrl=")) {
            return url.replace("ynb-media://client?imageUrl=", "");
        }
        return "";
    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_hybrid;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
    }

    private void checkPermission(String shareBase64Str) {
        //判断是否6.0以上的手机   不是就不用
        if (Build.VERSION.SDK_INT >= 23) {
            //判断是否有这个权限
            if (ContextCompat.checkSelfPermission(this,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                //2、申请权限: 参数二：权限的数组；参数三：请求码
                this.requestPermissions(new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, REQUEST_WRITE);
            } else {
                String result = saveImageToGallery(shareBase64Str, ".png");
                if (result == null || result.isEmpty()) {
                    ToastUtils.showShort("保存失败");
                    DataBean dataBean = new DataBean("保存失败", false);
                    savePhotoCallBack.doCallBackWithParam(new Gson().toJson(dataBean));
                    return;
                }
                ToastUtils.showShort("已保存到本地相册");
                DataBean dataBean = new DataBean(null, true);
                savePhotoCallBack.doCallBackWithParam(new Gson().toJson(dataBean));
            }
        } else {
            String result = saveImageToGallery(shareBase64Str, ".png");
            if (result == null || result.isEmpty()) {
                ToastUtils.showShort("保存失败");
                DataBean dataBean = new DataBean("保存失败", false);
                savePhotoCallBack.doCallBackWithParam(new Gson().toJson(dataBean));
                return;
            }
            ToastUtils.showShort("已保存到本地相册");
            DataBean dataBean = new DataBean(null, true);
            savePhotoCallBack.doCallBackWithParam(new Gson().toJson(dataBean));
        }
    }


    private String saveImageToGallery(String base64Str, String type) {
        byte[] base64 = stringToByteArrayByUrlEncode(base64Str);
        if (base64 == null || base64.length == 0) {
            ToastUtils.showShort("没有找到要保存的图片");
            return null;
        }
        //保存到SD卡
        String SAVE_PIC_PATH = Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED) ? Environment.getExternalStorageDirectory().getAbsolutePath() : "/mnt/sdcard";
        // 首先保存图片
        File appDir = new File(SAVE_PIC_PATH + "/豆芽/");
        if (!appDir.exists()) {
            appDir.mkdir();
        }
        long nowSystemTime = System.currentTimeMillis();
        String fileName = nowSystemTime + type;
        File file = new File(appDir, fileName);

        FileOutputStream fileOutputStream = null;
        try {
            if (!file.exists()) {
                file.createNewFile();
            }
            fileOutputStream = new FileOutputStream(file);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            return null;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }

        final BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(
                fileOutputStream);
        try {
            bufferedOutputStream.write(base64);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        } finally {
            try {
                bufferedOutputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
                return null;
            }
        }
        //保存图片后发送广播通知更新数据库
        Uri uri = Uri.fromFile(file);
        sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, uri));
        return file.getAbsolutePath();
    }

    public static byte[] stringToByteArrayByUrlEncode(String string) {
        try {
            String text = ImageUtils.toURLDecoder(string);
            text = text.replace("%20", "+");
            if (text.startsWith("data:image/png;base64,") || text.startsWith("data:image/jpeg;base64,")) {
                text = text.split(",")[1];
            }
            return Base64.decode(text, Base64.DEFAULT);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;

    }


    /**
     * 保存到本地相册
     *
     * @param bmp
     */
    private String saveImageToGallery(Bitmap bmp, String type) {
        if (bmp == null) {
            ToastUtils.showShort("没有找到要保存的图片");
            return "";
        }
        //保存到SD卡
        String SAVE_PIC_PATH = Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED) ? Environment.getExternalStorageDirectory().getAbsolutePath() : "/mnt/sdcard";
        // 首先保存图片
        File appDir = new File(SAVE_PIC_PATH + "/豆芽/");
        if (!appDir.exists()) {
            appDir.mkdir();
        }
        long nowSystemTime = System.currentTimeMillis();
        String fileName = nowSystemTime + type;
        File file = new File(appDir, fileName);
        try {
            if (!file.exists()) {
                file.createNewFile();
            }
            OutputStream fos = new FileOutputStream(file);
            bmp.compress(Bitmap.CompressFormat.JPEG, 100, fos);
            fos.flush();
            fos.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        //保存图片后发送广播通知更新数据库
        Uri uri = Uri.fromFile(file);
        sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, uri));
        return file.getAbsolutePath();
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        LocationManager.getInstance().unRegisterLocationListener(locationListener);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (mAgentWeb != null && mAgentWeb.handleKeyEvent(keyCode, event)) {
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Nullable
    @Override
    protected WebViewClient getWebViewClient() {
        return new WebViewClient() {
            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                mAgentWeb.getJsAccessEntrace().quickCallJs("window.localStorage.setItem", "key", "val", "aaa", "bbb");
            }
        };
    }


    @SuppressLint("CheckResult")
    private void grantedPermissions(OnGrantedNotify callBack, String... permissions) {
        RxPermissions rxPermissions = new RxPermissions(this); // where this is an Activity instance
        rxPermissions.request(permissions).subscribe(new Consumer<Boolean>() {
            @Override
            public void accept(Boolean granted) throws Exception {
                if (granted) { // 在android 6.0之前会默认返回true
                    //获取权限
                    callBack.granted();
                } else {
                    // 未获取权限
                    Toast.makeText(YNBHybridActivity.this, "您没有授权该权限，请在设置中打开授权", Toast.LENGTH_LONG).show();
                }
            }
        }, new Consumer<Throwable>() {
            @Override
            public void accept(Throwable throwable) throws Exception {

            }
        });
    }

    interface OnGrantedNotify {
        void granted();
    }
}
