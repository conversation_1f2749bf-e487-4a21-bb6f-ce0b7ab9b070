package com.ybm100.app.crm.widget.webview;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.util.AttributeSet;
import android.view.View;

import com.ybm100.app.crm.R;

/**
 * <AUTHOR>
 * @version 1.0
 * @file WebViewProgressBar.java
 * @brief
 * @date 2018/12/28
 * Copyright (c) 2018, 北京小药药
 * All rights reserved.
 */
public class WebViewProgressBar extends View {
    private int progress = 1;
    private  static final int HEIGHT = 4;
    private final Paint paint;
    private final static int[] colors = new int[]{};
    public WebViewProgressBar(Context context, AttributeSet attrs) {
        super(context, attrs);
        paint=new Paint(Paint.DITHER_FLAG);
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeWidth(HEIGHT);
        paint.setAntiAlias(true);
        paint.setColor(context.getResources().getColor(R.color.color_007AFF));
        //        paint.setShader(shader);
    }

    public WebViewProgressBar(Context context) {
        this(context,null);
    }
    public void setProgress(int progress){
        this.progress = progress;
        invalidate();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        canvas.drawRect(0, 0, getWidth() * progress / 100, HEIGHT, paint);
    }

}