package com.ybm100.app.crm.flutter.channel

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.tbruyelle.rxpermissions2.RxPermissions
import com.xyy.common.ActivityStackManager
import com.xyy.common.util.ToastUtils
import com.xyy.flutter.container.container.bridge.BaseHandler
import com.xyy.utilslibrary.RxManager
import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.IBaseView
import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.utilslibrary.utils.BitmapUtils
import com.xyy.utilslibrary.utils.TimeUtils
import com.ybm100.app.crm.api.ApiService
import com.ybm100.app.crm.bean.schedule.UploadImgResultBean
import com.ybm100.app.crm.flutter.ErrorCode
import com.ybm100.app.crm.net.RetrofitCreateHelper
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer
import com.ybm100.app.crm.order.activity.AlbumActivity
import com.ybm100.app.crm.order.photo.PhotoBean
import com.ybm100.app.crm.utils.AppFileUtils
import com.ybm100.app.crm.utils.CameraUtils
import com.ybm100.app.crm.utils.DialogUtils
import com.ybm100.app.crm.utils.SharedPrefManager
import io.reactivex.disposables.Disposable
import okhttp3.MediaType
import okhttp3.RequestBody
import java.io.File

class ScheduleSelectImageHandler : BaseHandler() {
    companion object {
        private const val FRAGMENT_TAG = "schedule_select_image_handler_fragment"
    }

    private var permissionDispose: Disposable? = null

    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {

        //添加权限 打开相机
        permissionDispose = RxPermissions(activity).requestEach(
                Manifest.permission.CAMERA,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
        ).subscribe { permission ->
            if (Manifest.permission.CAMERA == permission.name) {
                if (!permission.granted) {
                    error(
                            ErrorCode.NO_CAMERA_PERMISSION.errorCode,
                            ErrorCode.NO_CAMERA_PERMISSION.errorMsg
                    )
                    permissionDispose?.dispose()
                }
            } else {
                if (!permission.granted) {
                    error(
                            ErrorCode.NO_STORAGE_PERMISSION.errorCode,
                            ErrorCode.NO_STORAGE_PERMISSION.errorMsg
                    )
                } else {
                    //打开相机或者相册
                    val emptyFragment = EmptyFragment()
                    emptyFragment.setParams(this, params)
                    activity.supportFragmentManager.findFragmentByTag(FRAGMENT_TAG)?.let {
                        activity.supportFragmentManager.beginTransaction().remove(it).commitNowAllowingStateLoss()
                    }

                    activity.supportFragmentManager.beginTransaction()
                            .add(emptyFragment, FRAGMENT_TAG).commitAllowingStateLoss()
                }
            }
        }
    }

    class EmptyFragment : Fragment(), IBaseView {

        private var isFirstSelectImg = true

        private var handler: ScheduleSelectImageHandler? = null

        private var photoParams: Map<String, Any?>? = null
        private var address = ""

        private val mRxManager: RxManager = RxManager()

        fun setParams(handler: ScheduleSelectImageHandler, params: Map<String, Any?>) {
            photoParams = params
            this.handler = handler
        }

        override fun onCreate(savedInstanceState: Bundle?) {
            super.onCreate(savedInstanceState)
            if (savedInstanceState != null) {
                return
            }
            if (photoParams == null) {
                handler = null
                ToastUtils.showShortSafe("拍照数据错误，请关闭豆芽进程重新进入")
                return
            }
            // true 打开相册，false 直接拍照，不要问为什么，就是这样
            val isCamera = ((photoParams!!["isCamera"] ?: "true") as String).toBoolean()
            address = (photoParams!!["address"] ?: "") as String


            activity?.let {
                if (isCamera) {
                    openAlbum(1, it)
                } else {
                    openCamera(it)
                }
            }
        }

        private fun openAlbum(count: Int, activity: FragmentActivity) {
            val intent = Intent(activity, AlbumActivity::class.java)
            //设置最大选择数量
            intent.putExtra(AlbumActivity.MAX_PIECE, count)
            startActivityForResult(intent, CameraUtils.REQUEST_GALLERY)
        }

        private fun openCamera(activity: FragmentActivity) {
            if (isFirstSelectImg) {
                AppFileUtils.getCompressTempImgFile()
                AppFileUtils.clearCompressTempImg()
                isFirstSelectImg = false
            }
            CameraUtils.openCamera(this)
        }

        private fun buildResult(fileList: String) {
            if (photoParams == null) {
                handler = null
                ToastUtils.showShortSafe("拍照数据错误，请关闭豆芽进程重新进入!")
                parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
                return
            }
            handler?.result?.success(mapOf("success" to "true", "imageUrl" to fileList))
            parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
        }

        override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
            super.onActivityResult(requestCode, resultCode, data)
            if (resultCode == Activity.RESULT_OK && handler != null) {
                when (requestCode) {
                    CameraUtils.REQUEST_GALLERY -> {
                        handleGalleryResult(data)
                    }
                    CameraUtils.REQUEST_CAMERA -> {
                        handleCameraResult()
                    }
                }
            } else {
                handler?.error(ErrorCode.NO_RESULT.errorCode, ErrorCode.NO_RESULT.errorMsg)
                parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
            }
        }

        private fun handleCameraResult() {
            val file = File(SharedPrefManager.getInstance().cameraImaFilePath)
            if (!file.exists()) {
                handler?.error(ErrorCode.NO_EXIST.errorCode, ErrorCode.NO_EXIST.errorMsg)
                parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
                return
            }
            if (file.length() <= 0) {
                handler?.error(ErrorCode.FILE_CORRUPTED.errorCode, ErrorCode.FILE_CORRUPTED.errorMsg)
                parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
                return
            }
            uploadImageFile(file)
        }

        private fun handleGalleryResult(data: Intent?) {
            val result: List<PhotoBean?>? =
                    data?.getParcelableArrayListExtra(AlbumActivity.EXTRA_RESULT)
            if (result.isNullOrEmpty()) {
                handler?.error(
                        ErrorCode.RESULT_IS_EMPTY.errorCode,
                        ErrorCode.RESULT_IS_EMPTY.errorMsg
                )
                parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
                return
            }
            val resultList = ArrayList<String>()

            result.forEach {
                if (it == null) {
                    handler?.error(ErrorCode.NO_EXIST.errorCode, ErrorCode.NO_EXIST.errorMsg)
                    parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
                    return
                }
                val urlFile = File(it.path)
                val uri = Uri.fromFile(urlFile)
                val file = CameraUtils.handleImageOn19(uri, activity)
                if (file == null || !file.exists()) {
                    handler?.error(ErrorCode.NO_EXIST.errorCode, ErrorCode.NO_EXIST.errorMsg)
                    parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
                    return
                }
                if (file.length() <= 0) {
                    handler?.error(
                            ErrorCode.FILE_CORRUPTED.errorCode,
                            ErrorCode.FILE_CORRUPTED.errorMsg
                    )
                    parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
                    return
                }
                resultList.add(file.absolutePath)
//                val sourceType = data.getIntExtra(AlbumActivity.SOURCE_TYPE, 0)
            }
            if (resultList.size > 0 && resultList[0].isNotEmpty()) {
                uploadImageFile(File(resultList[0]))
            } else {
                handler?.error(
                        ErrorCode.NO_RESULT.errorCode,
                        ErrorCode.NO_RESULT.errorMsg
                )
                parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
            }
        }

        private fun uploadImageFile(file: File) {
            if (file.exists()) {
                val pathList = BitmapUtils.compress(file, 800 * 800, true,
                        address, TimeUtils.getNowString(TimeUtils.DATA_FORMAT_YMDHM_SLANTING))
                if (pathList == null || pathList.size == 0) {
                    ToastUtils.showLongSafe("图片压缩失败")
                    handler?.error(
                            ErrorCode.PHOTO_COMPRESS_FAIL.errorCode,
                            ErrorCode.PHOTO_COMPRESS_FAIL.errorMsg
                    )
                    parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
                    return
                }
                uploadImage(pathList.get(0))
            } else {
                ToastUtils.showLongSafe("图片存储失败")
                handler?.error(
                        ErrorCode.NO_EXIST.errorCode,
                        ErrorCode.NO_EXIST.errorMsg
                )
                parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
            }
        }


        fun uploadImage(path: String) {
            val map: MutableMap<String, RequestBody> = HashMap()
            val file = File(path)
            val requestBody: RequestBody = RequestBody.create(MediaType.parse("multipart/form-data"), file)
            map["files\"; filename=\"" + file.name] = requestBody
            showWaitDialog("上传中")
            mRxManager.register(RetrofitCreateHelper.createApi(ApiService::class.java).uploadImages(map, "schedule", "uploadLicenseAuditImg")
                    .compose(RxHelper.rxSchedulerHelper()).subscribe(
                            object : SimpleSuccessConsumer<UploadImgResultBean>(this, null) {
                                override fun onSuccess(bean: UploadImgResultBean) {
                                    hideWaitDialog()
                                    val data = bean.data
                                    if (data.isEmpty() || data[0].isNullOrEmpty()) {
                                        ToastUtils.showLong("返回结果为空")
                                        handler?.error(
                                                ErrorCode.RESPONSE_EMPTY.errorCode,
                                                ErrorCode.RESPONSE_EMPTY.errorMsg
                                        )
                                        parentFragmentManager.beginTransaction().remove(this@EmptyFragment).commitNowAllowingStateLoss()
                                    } else {
                                        buildResult(data[0])
                                    }
                                }

                                override fun onFailure(errorCode: Int) {
                                    super.onFailure(errorCode)
                                    hideWaitDialog()
                                    handler?.error(
                                            ErrorCode.REQUEST_ERROR.errorCode,
                                            ErrorCode.REQUEST_ERROR.errorMsg
                                    )
                                    parentFragmentManager.beginTransaction().remove(this@EmptyFragment).commitNowAllowingStateLoss()
                                }
                            }, object : SimpleErrorConsumer(null) {
                        override fun accept(throwable: Throwable?) {
                            super.accept(throwable)
                            hideWaitDialog()
                            handler?.error(
                                    ErrorCode.REQUEST_ERROR.errorCode,
                                    ErrorCode.REQUEST_ERROR.errorMsg
                            )
                            parentFragmentManager.beginTransaction().remove(this@EmptyFragment).commitNowAllowingStateLoss()
                        }
                    }))
        }


        override fun onDestroy() {
            super.onDestroy()
            mRxManager.unSubscribe()
        }

        override fun showToast(msg: String?) {
        }

        override fun showWaitDialog(waitMsg: String?) {
            DialogUtils.showProgressDialog(ActivityStackManager.getInstance().currentActivity(), waitMsg, true)
        }

        override fun hideWaitDialog() {
            DialogUtils.dismissProgressDialog()
        }

        override fun hideKeyboard() {
        }

        override fun initPresenter(): BasePresenter<*, *> {
            return object : BasePresenter<Any?, Any?>() {
                override fun getModel(): Any? {
                    return null
                }
            }
        }

        override fun showNetError() {
        }

        override fun back() {
        }
    }

}