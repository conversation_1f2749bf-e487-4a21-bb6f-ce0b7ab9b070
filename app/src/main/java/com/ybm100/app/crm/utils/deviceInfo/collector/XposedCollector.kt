package com.ybm100.app.crm.utils.deviceInfo.collector

import android.content.Context
import java.lang.reflect.Field


class XposedCollector : BaseCollector() {

    override fun internalCollect(context: Context): String? {
        val isXposedExistByThrow = isXposedExistByThrow()
        val isXposedExistsByClass = isXposedExists()
        return "${isXposedExistByThrow},${isXposedExistsByClass}"
    }


    private val XPOSED_HELPERS = "de.robv.android.xposed.XposedHelpers"
    private val XPOSED_BRIDGE = "de.robv.android.xposed.XposedBridge"

    //手动抛出异常，检查堆栈信息是否有xp框架包
    private fun isXposedExistByThrow(): Bo<PERSON>an {
        return try {
            throw java.lang.Exception("gg")
        } catch (e: java.lang.Exception) {
            for (stackTraceElement in e.stackTrace) {
                if (stackTraceElement.className.contains(XPOSED_BRIDGE)) return true
            }
            false
        }
    }

    //，通过检查classloader，检查xposed包是否存在
    private fun isXposedExists(): <PERSON><PERSON><PERSON> {
        try {
            val xpHelperObj = ClassLoader
                    .getSystemClassLoader()
                    .loadClass(XPOSED_HELPERS)
                    .newInstance()
        } catch (e: InstantiationException) {
            e.printStackTrace()
            return true
        } catch (e: IllegalAccessException) { //实测debug跑到这里报异常
            e.printStackTrace()
            return true
        } catch (e: ClassNotFoundException) {
            e.printStackTrace()
            return false
        }
        try {
            val xpBridgeObj = ClassLoader
                    .getSystemClassLoader()
                    .loadClass(XPOSED_BRIDGE)
                    .newInstance()
        } catch (e: InstantiationException) {
            e.printStackTrace()
            return true
        } catch (e: IllegalAccessException) { //实测debug跑到这里报异常
            e.printStackTrace()
            return true
        } catch (e: ClassNotFoundException) {
            e.printStackTrace()
            return false
        }
        return true
    }

    /**
     * java层检测意义不大，暂不实现
     * 主要用于检测Cydia Substrate，Substrate用于hook native层代码
     * 原理：通过读取/proc/self/maps，获取当前app加载的所有so文件，
     * 判断是否存在libsubstrate.so，libsubstrate-dvm.so两个文件
     */
    private fun checkSOMaps() :Boolean{
        return false
    }

    //尝试关闭xp的全局开关，备用
    private fun tryShutdownXposed(): Boolean {
        return if (isXposedExistByThrow()) {
            var xpdisabledHooks: Field? = null
            try {
                xpdisabledHooks = ClassLoader.getSystemClassLoader()
                        .loadClass(XPOSED_BRIDGE)
                        .getDeclaredField("disableHooks")
                xpdisabledHooks.isAccessible = true
                xpdisabledHooks.set(null, java.lang.Boolean.TRUE)
                true
            } catch (e: NoSuchFieldException) {
                e.printStackTrace()
                false
            } catch (e: ClassNotFoundException) {
                e.printStackTrace()
                false
            } catch (e: IllegalAccessException) {
                e.printStackTrace()
                false
            }
        } else true
    }
}
