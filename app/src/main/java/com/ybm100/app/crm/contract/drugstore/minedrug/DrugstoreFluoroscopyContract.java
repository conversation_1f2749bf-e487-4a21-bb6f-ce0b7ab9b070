package com.ybm100.app.crm.contract.drugstore.minedrug;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.PharmacyPerspective;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:17
 */
public interface DrugstoreFluoroscopyContract {

    interface IDrugstoreFluoroscopyModel extends IBaseModel {
        Observable<RequestBaseBean<PharmacyPerspective>> getPerspectiveData(String shopId);
    }

    interface IDrugstoreFluoroscopyView extends IBaseActivity {
        void getPerspectiveDataSuccess(RequestBaseBean<PharmacyPerspective> requestBaseBean);
        void getPerspectiveDataFail(String msg);
    }

}
