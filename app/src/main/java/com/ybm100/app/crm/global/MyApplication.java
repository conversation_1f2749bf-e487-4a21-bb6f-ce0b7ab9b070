package com.ybm100.app.crm.global;

import android.app.Activity;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;

import com.tencent.tinker.loader.app.TinkerApplication;
import com.tencent.tinker.loader.shareutil.ShareConstants;
import com.ybm100.app.crm.bean.user.UserInfoBean;
import com.ybm100.app.crm.utils.SharedPrefManager;
import com.ybm100.app.crm.widget.marker.Watermark;

/**
 * @author: zcj
 * @time:2020/8/26. Description:
 */
public class MyApplication extends TinkerApplication {
    public MyApplication() {
        super(ShareConstants.TINKER_ENABLE_ALL, MyApplicationLike.class.getName(),
                "com.tencent.tinker.loader.TinkerLoader", false);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Watermark.getInstance()
                .setTextColor(0x3EAEAEAE)
                .setTextSize(15);
        registerActivityLifecycleCallbacks(new ActivityLifecycleCallbacks() {
            @Override
            public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
                new Handler().post(() -> {
                    if (!SharedPrefManager.getInstance().isLogin()) return;
                    UserInfoBean userInfo = SharedPrefManager.getInstance().getUserInfo();
                    if (userInfo == null) return;
                    String userName = userInfo.getRealName();
                    if (TextUtils.isEmpty(userName)) return;
                    String phone = userInfo.getPhone();
                    String phoneSuffix = "";
                    if (phone != null) {
                        if (phone.length() > 4) {
                            phoneSuffix = phone.substring(phone.length() - 4);
                        } else phoneSuffix = phone;
                    }
                    Watermark.getInstance().show(activity, userName + phoneSuffix);
                });
            }

            @Override
            public void onActivityStarted(Activity activity) {

            }

            @Override
            public void onActivityResumed(Activity activity) {

            }

            @Override
            public void onActivityPaused(Activity activity) {

            }

            @Override
            public void onActivityStopped(Activity activity) {

            }

            @Override
            public void onActivitySaveInstanceState(Activity activity, Bundle outState) {

            }

            @Override
            public void onActivityDestroyed(Activity activity) {

            }
        });
    }
}
