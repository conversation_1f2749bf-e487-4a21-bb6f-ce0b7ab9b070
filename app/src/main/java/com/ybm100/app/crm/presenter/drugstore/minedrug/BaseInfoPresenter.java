package com.ybm100.app.crm.presenter.drugstore.minedrug;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.BaseInfo;
import com.ybm100.app.crm.contract.drugstore.minedrug.BaseInfoContract;
import com.ybm100.app.crm.model.drugstore.minedrug.BaseInfoModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;
import com.ybm100.app.crm.task.bean.TaskAndMerchantBean;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:04
 */
public class BaseInfoPresenter extends BasePresenter<BaseInfoContract.IBaseInfoModel, BaseInfoContract.IBaseInfoView> {

    public static BaseInfoPresenter newInstance() {
        return new BaseInfoPresenter();
    }

    @Override
    protected BaseInfoModel getModel() {
        return BaseInfoModel.newInstance();
    }

    public void getDrugBaseInfo(String merchantId, int isMerchantId) {
        if (mIView == null || mIModel == null) return;

        mRxManager.register(mIModel.getBaseInfo(merchantId, isMerchantId).subscribe(new SimpleSuccessConsumer<RequestBaseBean<BaseInfo>>(mIView) {
            @Override
            public void onSuccess(RequestBaseBean<BaseInfo> drugstoreInfoBean) {

                mIView.getBaseInfo(drugstoreInfoBean);
            }
        }, new SimpleErrorConsumer(mIView)));

    }

    public void releaseToPublic(String customerId, String skuCollectCodes) {
        if (mIView == null || mIModel == null) return;

        mRxManager.register(mIModel.releaseToPublic(customerId, skuCollectCodes).subscribe(new SimpleSuccessConsumer<RequestBaseBean>(mIView, "加载中...") {
            @Override
            public void onSuccess(RequestBaseBean bean) {

                mIView.releaseToPublicSuccess(bean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }

    /**
     * 新建拜访（药店入口）
     *
     * @param merchantId
     * @param customerType
     */
    public void toAddVisit(String merchantId, String customerType) {
        if (mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.toAddVisit(merchantId, customerType)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean<TaskAndMerchantBean>>(mIView) {
                    @Override
                    public void onSuccess(RequestBaseBean<TaskAndMerchantBean> listRequestBaseBean) {
                        if (mIView == null) return;
                        mIView.toAddVisit(listRequestBaseBean);
                    }

                    @Override
                    public void onFailure(int errorCode) {
                        super.onFailure(errorCode);
                    }
                }, new SimpleErrorConsumer(mIView)));
    }

    public void distributeToBD(String bindUserId, String customerId, String skuCollectCodes) {
        if (mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.distributeToBD(bindUserId, customerId, skuCollectCodes)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean>(mIView, "") {
                    @Override
                    public void onSuccess(RequestBaseBean listRequestBaseBean) {
                        if (mIView == null) return;
                        mIView.distributeToBDSuccess(listRequestBaseBean);
                    }

                    @Override
                    public void onFailure(int errorCode) {

                    }
                }, new SimpleErrorConsumer(mIView) {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        mIView.showToast(throwable.getMessage());
                    }
                }));
    }

}
