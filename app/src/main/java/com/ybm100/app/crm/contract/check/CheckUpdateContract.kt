package com.ybm100.app.crm.contract.check

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.ybm100.app.crm.bean.update.VersionInfo
import io.reactivex.Observable

/**
 * @author: zcj
 * @time:2019/12/2.
 * Description:
 */
interface CheckUpdateContract {
    interface ICheckUpdateModel : IBaseModel {
        /**
         * 更新请求
         *
         * @return
         */
        fun checkUpdate(): Observable<RequestBaseBean<VersionInfo>>
    }

    interface ICheckUpdateView : IBaseActivity {
        /**
         * 检测更新成功
         */
        fun checkUpdateSuccess(versionInfo: VersionInfo)

        /**
         * 检测更新失败
         */
        fun checkUpdateFailed()
    }
}
