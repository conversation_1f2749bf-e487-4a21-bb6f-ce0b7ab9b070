package com.ybm100.app.crm.constant;

/**
 * Created by den<PERSON><PERSON><PERSON><PERSON> on 2019/1/7
 */
public class ExtraConstants {
    public static final String NAME = "name";
    public static final String ID = "id";
    public static final String IS_GROUP = "isgroup";
    public static final String IS_CAN_SELECT_P = "isCanSelectP";
    public static final String DATA = "Data";
    public static final String VISIT_TYPE_PARAMS = "visitTypeParams ";
    public static final String CUSTOMER_TYPE = "customerType ";
    public static final String CUSTOMER_ID = "customerId ";
    public static final String AUTH_TYPE = "just_me";
    public static final String DURATION = "duration";
    public static final String LATITUDE = "latitude";
    public static final String LONGITUDE = "longitude";

    /*数据权限范围*/
    /*仅自己的*/
    public static final int AUTH_TYPE_JUST_ME = 1;
    /*自己以及权限组*/
    public static final int AUTH_TYPE_NOT_JUST_ME = 0;

    //是否会员
    public static final String IFVIP = "ifvip";
    //会员
    public static final int IFVIP_VIP = 1;
    //私海
    public static final int IFVIP_SIHAI = 2;
    public static final int IFVIP_ALL = 0;
    //location_default
    public static final int LOCATION_DEFAULT = 0;


    public static final String IS_COMPLETE = "IScOMPLETE";
    public static final String MERCHANT_ID = "merchantId";
    public static final String MERCHANT_NAME = "merchantName";
    public static final String MERCHANT_PHONE = "merchantPhone";

    public static final String SELETED_OBJECT = "selectedObject";
    public static final int REQ_SEARCH_EXECUTOR = 1;

    public static final int REQUEST_CODE_SELECT_IMAGE = 4;
    //选择联系人
    public static final int REQUEST_CODE_SELECT_CONSTACT = 9;
    //选择关联任务
    public static final int REQUEST_CODE_SELECT_TASK = 1;
    //对象经营状况
    public static final int REQUEST_CODE_TARGET_CONDITION = 3;
    //选择执行对象
    public static final int REQUEST_CODE_SELECT_OBJECT = 2;
}
