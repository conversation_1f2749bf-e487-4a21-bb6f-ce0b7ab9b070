package com.ybm100.app.crm.utils.module.module

import android.content.Context
import android.content.ContextWrapper
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import androidx.annotation.IdRes
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.RecyclerView
import com.xyy.common.util.ToastUtils
import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.IBaseView
import com.xyy.utilslibrary.base.activity.BaseCompatActivity
import com.xyy.utilslibrary.base.activity.BaseMVPCompatActivity
import com.ybm100.app.crm.utils.module.ExtraPropertyCallback
import com.ybm100.app.crm.utils.module.presenter.BaseModulePresenter
import java.lang.Exception

abstract class BaseModule<P>(context: Context) : FrameLayout(context), IBaseView
        where P : BaseModulePresenter<*> {
    var typeId: Int = 0
    var moduleId: Int = 0
    var position: Int = -1


    var refreshCallback: OnRefreshCallback? = null
    var extraPropertyCallback: ExtraPropertyCallback? = null


    protected var layoutInflater: LayoutInflater = LayoutInflater.from(context)

    private var currentState: ModuleState = ModuleState.NORMAL

    protected var mPresenter: P? = null


    init {
        initContentView()
    }

    private fun initPresenterForModel() {
        try {
            mPresenter = getPresenter()?.newInstance()
        } catch (ignore: Exception) {
        }
        mPresenter?.attachMVForModel(this)
    }

    fun init() {
        initPresenterForModel()
        onInit()
        onRefresh()
    }

    private fun initContentView() {
        layoutInflater.inflate(getContentLayoutId(), this, true)
    }

    override fun showToast(msg: String?) {
        ToastUtils.showShortSafe(msg)
    }

    protected fun hideSelf() {
        layoutParams.let {
            it.height = 0
            layoutParams = it
        }
    }

    protected fun showSelf() {
        layoutParams.let {
            it.height = LayoutParams.WRAP_CONTENT
            layoutParams = it
        }
    }


    fun <T : View?> fv(@IdRes id: Int): T? {
        return findViewById<T>(id)
    }


    protected fun getActivity(): FragmentActivity? {
        var viewContext = context
        while (viewContext is ContextWrapper) {
            if (viewContext is FragmentActivity) {
                return viewContext
            }
            viewContext = viewContext.baseContext
        }
        return null
    }

    protected fun startActivity(clz: Class<*>) {
        val activity = getActivity()
        if (activity is BaseCompatActivity) {
            activity.startActivity(clz)
        } else {
            context.startActivity(Intent(context, clz))
        }

    }

    protected fun startActivity(clz: Class<*>, bundle: Bundle) {
        val activity = getActivity()
        if (activity is BaseCompatActivity) {
            activity.startActivity(clz, bundle)
        } else {
            context.startActivity(Intent(context, clz).also {
                it.putExtras(bundle)
            })
        }

    }

    abstract fun getContentLayoutId(): Int
    abstract fun onInit()
    abstract fun onRefresh()
    abstract fun getPresenter(): Class<P>?

    open fun onRefreshSpecial() {
        onRefresh()
    }

    fun onDestroy() {
        mPresenter?.detachMV()
    }


    fun fixLayoutParams() {
        layoutParams = RecyclerView.LayoutParams(RecyclerView.LayoutParams.MATCH_PARENT, RecyclerView.LayoutParams.WRAP_CONTENT)
    }

    enum class ModuleState {
        NORMAL, LOADING, EMPTY, ERROR
    }


    override fun initPresenter(): BasePresenter<*, *> {
        return mPresenter!!
    }


    /**
     * 显示等待dialog
     *
     * @param waitMsg 等待消息字符串
     */
    override fun showWaitDialog(waitMsg: String?) {
        val activity = getActivity()
        if (activity is BaseMVPCompatActivity<*>) {
            activity.showWaitDialog(waitMsg)
        }
    }

    /**
     * 隐藏等待dialog
     */
    override fun hideWaitDialog() {
        val activity = getActivity()
        if (activity is BaseMVPCompatActivity<*>) {
            activity.hideWaitDialog()
        }
    }

    /**
     * 隐藏键盘
     */
    override fun hideKeyboard() {
        val activity = getActivity()
        if (activity is BaseMVPCompatActivity<*>) {
            activity.hideKeyboard()
        }
    }

    /**
     * 回退
     */
    override fun back() {
        val activity = getActivity()
        if (activity is BaseMVPCompatActivity<*>) {
            activity.back()
        }
    }

    /**
     * 显示网络错误
     */
    override fun showNetError() {
    }


    interface OnRefreshCallback {
        fun refreshFinish(typeId: Int, params: Map<String, Any>?)
    }
}