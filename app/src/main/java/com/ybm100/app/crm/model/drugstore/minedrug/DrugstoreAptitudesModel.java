package com.ybm100.app.crm.model.drugstore.minedrug;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiDrugstoreInfo;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.AptitudesBean;
import com.ybm100.app.crm.contract.drugstore.minedrug.DrugstoreAptitudesContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;
import com.ybm100.app.crm.order.bean.AptitudeInitBean;
import com.ybm100.app.crm.order.bean.InvoiceHasBean;

import java.util.HashMap;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:08
 */
public class DrugstoreAptitudesModel extends BaseModel implements DrugstoreAptitudesContract.IDrugstoreAptitudesModel {

    public static DrugstoreAptitudesModel newInstance() {
        return new DrugstoreAptitudesModel();
    }

    @Override
    public Observable<RequestBaseBean<AptitudesBean>> getAptitudeDetailList(String merchantId) {
        return RetrofitCreateHelper.createApi(ApiDrugstoreInfo.class).getAptitudeDetailList(merchantId)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean<InvoiceHasBean>> isHaveType(String merchantId) {
        return RetrofitCreateHelper.createApi(ApiDrugstoreInfo.class).isHaveType(merchantId)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean<AptitudeInitBean>> initLicenseAuditDetail(HashMap<String, String> map) {
        return RetrofitCreateHelper.createApi(ApiDrugstoreInfo.class).initLicenseAuditDetail(map)
                .compose(RxHelper.rxSchedulerHelper());
    }
}