package com.ybm100.app.crm.utils.deviceInfo.collector

import android.content.Context
import android.content.pm.ApplicationInfo
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.net.wifi.WifiManager
import android.os.Environment
import android.text.TextUtils
import java.io.BufferedReader
import java.io.InputStreamReader


class SDCardFileListCollector : BaseCollector() {

    override fun internalCollect(context: Context): String? {
        val dir = Environment.getExternalStorageDirectory()
        return dir.listFiles().joinToString { it.name }
    }


}
