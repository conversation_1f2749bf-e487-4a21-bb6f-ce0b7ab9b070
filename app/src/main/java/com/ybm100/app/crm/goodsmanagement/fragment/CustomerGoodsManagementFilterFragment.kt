package com.ybm100.app.crm.goodsmanagement.fragment

import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.RadioButton
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.google.android.flexbox.FlexboxLayoutManager
import com.xyy.userbehaviortracking.utils.UserBehaviorTrackingUtils
import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.base.fragment.BaseCompatFragment
import com.xyy.utilslibrary.base.fragment.BaseMVPCompatFragment
import com.ybm100.app.crm.R
import com.ybm100.app.crm.constant.Constants
import com.ybm100.app.crm.goodsmanagement.adapter.GoodsZoneFilterAdapter
import com.ybm100.app.crm.goodsmanagement.bean.AvailableZone
import com.ybm100.app.crm.goodsmanagement.bean.Zone
import com.ybm100.app.crm.goodsmanagement.contract.GoodsFilterContract
import com.ybm100.app.crm.goodsmanagement.contract.GoodsManagementContract
import com.ybm100.app.crm.goodsmanagement.presenter.GoodsFilterPresenter
import com.ybm100.app.crm.goodsmanagement.presenter.GoodsManagementPresenter
import com.ybm100.app.crm.ui.adapter.MyBaseQuickAdapter
import kotlinx.android.synthetic.main.fragment_customer_goods_management_filter.*

class CustomerGoodsManagementFilterFragment : BaseMVPCompatFragment<GoodsFilterPresenter>(), View.OnClickListener, GoodsFilterContract.IGoodsFilterView {

    private var mMerchantId = ""

    private var mYBMFilterPos = -1
    private var mCollectionStatusFilterPos = -1
    private lateinit var mCustomerDrawerListener: CustomerDrawerListener
    private var mZoneListView: RecyclerView? = null
    private var mZoneLabelView: View? = null
    private var mAdapter: GoodsZoneFilterAdapter? = null

    override fun getLayoutId(): Int {
        return R.layout.fragment_customer_goods_management_filter
    }

    override fun showNetError() {
    }

    override fun onGetAvailableZoneSuccess(data: RequestBaseBean<AvailableZone?>?) {
        data?.data?.rows?.let { it ->
            if (it.isNotEmpty()) {
                setZoneGroupVisible(true)
                mZoneListView?.layoutManager = FlexboxLayoutManager(context)
                mZoneListView?.adapter = GoodsZoneFilterAdapter().also { adapter ->
                    it.find {
                        it?.zoneId == "0"
                    }?.isSelect = true
                    adapter.setNewData(it)
                    mAdapter = adapter
                    adapter.setOnItemClickListener { adapter, view, position ->
                        if (adapter is GoodsZoneFilterAdapter) {
                            UserBehaviorTrackingUtils.track("mc-productmgt-zonesoft")
                            val preIndex = adapter.data.indexOfFirst { zone ->
                                if (zone?.isSelect == true) {
                                    zone.isSelect = false
                                    true
                                } else {
                                    false
                                }
                            }
                            adapter.data.forEach { item ->
                                item?.isSelect = false
                            }
                            adapter.data.getOrNull(position)?.isSelect = true
                            if (preIndex != position) {
                                adapter.notifyItemChanged(preIndex)
                                adapter.notifyItemChanged(position)
                            }
                        }
                    }
                }
            }
        }
    }

    override fun onGetAvailableZoneFail() {
        setZoneGroupVisible(false)
    }

    override fun initPresenter(): BasePresenter<*, *> {
        return GoodsFilterPresenter()
    }

    override fun initUI(view: View?, savedInstanceState: Bundle?) {
        mMerchantId = arguments?.getString(Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_MERCHANT_ID)
                ?: ""
        registerListener()


        if (mYBMFilterPos == 1) {
            (rg_ybm?.getChildAt(mYBMFilterPos) as? RadioButton)?.isChecked = true
        }
        mZoneListView = view?.findViewById(R.id.rv_zone_list)
        mZoneLabelView = view?.findViewById(R.id.tv_zone_list_label)
        if (mMerchantId.isNotEmpty()) {
            requestZoneList()
        }
    }

    private fun setZoneGroupVisible(isVisible: Boolean) {
        (if (isVisible) View.VISIBLE else View.GONE).let {
            mZoneLabelView?.visibility = it
            mZoneListView?.visibility = it
        }
    }


    private fun requestZoneList() {
        val map = HashMap<String, String>().also {
            it["merchantId"] = mMerchantId
        }
        mPresenter.getAvailableZone(map)
    }

    private fun registerListener() {
        rtv_reset.setOnClickListener(this)
        rtv_confirm.setOnClickListener(this)
        rg_ybm.setOnCheckedChangeListener { group, checkedId ->
            when (checkedId) {
                R.id.rb_in_stock -> {
                    mYBMFilterPos = 0

                    UserBehaviorTrackingUtils.track("mc-productmgt-instock")
                }
                R.id.rb_promotional -> {
                    mYBMFilterPos = 1
                }
            }
        }
        rg_collection_status.setOnCheckedChangeListener { group, checkedId ->
            when (checkedId) {
                R.id.rb_uncollected -> {
                    mCollectionStatusFilterPos = 0

                    UserBehaviorTrackingUtils.track("mc-productmgt-markfilter")
                }
                R.id.rb_collected -> {
                    mCollectionStatusFilterPos = 1

                    UserBehaviorTrackingUtils.track("mc-productmgt-unmarkfilter")
                }
            }
        }
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.rtv_reset -> {
                rg_ybm.clearCheck()
                rg_collection_status.clearCheck()
                mAdapter?.let {
                    it.getSelectedZone()?.isSelect = false
                    it.data.firstOrNull()?.isSelect = true
                    it.notifyDataSetChanged()
                }
                mYBMFilterPos = -1
                mCollectionStatusFilterPos = -1
            }
            R.id.rtv_confirm -> {
                val selectedZone = mAdapter?.getSelectedZone()
                Log.e("guan", "selectedZone:${selectedZone}")
                mCustomerDrawerListener.onCustomerConfirmPressed(mYBMFilterPos, mCollectionStatusFilterPos, selectedZone)
            }
        }
    }

    fun syncCustomerDrawerFilterData(YBMFilterPos: Int) {
        if (YBMFilterPos == 1) {
            mYBMFilterPos = YBMFilterPos
            (rg_ybm?.getChildAt(mYBMFilterPos) as? RadioButton)?.isChecked = true
        } else {
            mYBMFilterPos = -1
            rg_ybm?.clearCheck()
        }
    }

    fun setCustomerDrawerListener(listener: CustomerDrawerListener) {
        mCustomerDrawerListener = listener
    }

    companion object {
        @JvmStatic
        fun newInstance(merchantId: String?) =
                CustomerGoodsManagementFilterFragment().apply {
                    arguments = Bundle().apply {
                        putString(Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_MERCHANT_ID, merchantId)
                    }
                }
    }

    interface CustomerDrawerListener {
        fun onCustomerConfirmPressed(YBMFilterPos: Int, collectionStatusFilterPos: Int, selectedZone: Zone?)
    }
}