package com.ybm100.app.crm.goodsmanagement.bean

data class GoodsManagementListBean(
        val lastPage: Boolean? = false, // false
        val rows: List<Row?>? = listOf(),
        val total: Int? = 0 // 1
) {
    data class Row(
            val barcode: String? = "", // 测试内容j1g4
            var collect: Int? = 0, // 40067
            val fob: String? = "", // 测试内容91xv
            val grossMargin: String? = "", // 测试内容r48k
            val id: String? = "", // 77417
            val imageUrl: String? = "", // 测试内容vi4v
            val productLabelList: List<ProductLabel?>? = listOf(),
            val showName: String? = "", // 测试内容gs92
            val spec: String? = "", // 测试内容d1h5
            val suggestPrice: String? = "", // 测试内容wls1
            val tagList: List<Tag?>? = listOf(),
            val branchCode: String? = "",
            val orgCode: String? = "",
            var isSelected: Boolean? = false,
            var discountPrice: String? = "",
            val zoneList: List<String?>?,
            val shopName: String? = ""
    ) {
        data class ProductLabel(
                val tagName: String? = "", // 测试内容8f95
                val tagType: Int? = 0 //1红，2黄
        )

        data class Tag(
                val description: String? = "", // 测试内容1im1
                val name: String? = "", // 测试内容84ct
                val uiType: Int? = 0 // 1（临期/近效期）2活动标签
        )
    }
}