package com.ybm100.app.crm.contract.coupon;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.coupon.VoucherBean;

import java.util.HashMap;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 04/16/2019 11:26
 */
public interface CouponCountContract {

    interface ICouponCountModel extends IBaseModel {
        Observable<RequestBaseBean<VoucherBean>> getVoucherData(HashMap<String, String> map);
    }

    interface ICouponCountView extends IBaseActivity {
        void getVoucherDataSuccess(boolean refresh ,VoucherBean voucherBean);

        void enableLoadMore(boolean b);

        void loadMoreComplete();

        void showEmpty();
    }

}
