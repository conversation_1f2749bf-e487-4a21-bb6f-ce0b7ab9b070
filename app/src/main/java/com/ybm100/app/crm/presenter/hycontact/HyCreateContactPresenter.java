package com.ybm100.app.crm.presenter.hycontact;


import android.text.TextUtils;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.rxbus.RxBus;
import com.xyy.utilslibrary.utils.JsonUtils;
import com.xyy.utilslibrary.utils.LogUtils;
import com.xyy.utilslibrary.utils.ResourcesUtils;
import com.xyy.utilslibrary.utils.StringUtils;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.contact.BaseDrugInfoBean;
import com.ybm100.app.crm.bean.contact.ContactBean;
import com.ybm100.app.crm.constant.RxBusCode;
import com.ybm100.app.crm.contract.contact.CreateContactContract;
import com.ybm100.app.crm.model.contact.ContactConstants;
import com.ybm100.app.crm.model.hycontact.HyCreateContactModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;
import com.ybm100.app.crm.schedule.model.ContactJobEnum;
import com.ybm100.app.crm.utils.InputFilter.EditUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.ybm100.app.crm.constant.RxBusCode.RX_BUS_UPDATE_CONTACT_BOOK;
import static com.ybm100.app.crm.model.contact.ContactConstants.SPLIT_TAG;
import static com.ybm100.app.crm.utils.InputFilter.EditUtil.CONTACT_TAG_PATTERN;

/**
 * HyCreateContactPresenter
 */
public class HyCreateContactPresenter extends BasePresenter<CreateContactContract.ICreateContactModel, CreateContactContract.ICreateContactView> {

    private ContactBean contact;

    public static HyCreateContactPresenter newInstance() {
        return new HyCreateContactPresenter();
    }

    @Override
    protected HyCreateContactModel getModel() {
        return HyCreateContactModel.newInstance();
    }

    public void setData(ContactBean contact) {

        this.contact = contact;
    }

    public void getContactJobEnum() {
        if (mIView == null || mIModel == null)
            return;
        mRxManager.register(mIModel.getContactJob().subscribe(
                new SimpleSuccessConsumer<RequestBaseBean<List<ContactJobEnum>>>(mIView) {

                    @Override
                    public void onSuccess(RequestBaseBean<List<ContactJobEnum>> listRequestBaseBean) {
                        if (listRequestBaseBean != null) {
                            mIView.getContactJobSuccess(listRequestBaseBean.getData());
                        }
                    }

                    @Override
                    public void onFailure(int errorCode) {
                    }
                }, new SimpleErrorConsumer(mIView) {
                    @Override
                    protected void onError(Throwable throwable, String msg) {
                    }
                }));
    }

    public void addTag(final String tag) {
        //验证tag。字数不能超过8个，中文，数字，英文字母，相对常用的标点逗号，句号，冒号，括号，叹号都可以。
        Pattern pattern = Pattern.compile(CONTACT_TAG_PATTERN, Pattern.DOTALL);
        Matcher matcher = pattern.matcher(tag);
        if (!matcher.matches()) {
            mIView.showToast("字数不能超过8个，中文，数字，英文字母，相对常用的标点逗号，句号，冒号，括号，叹号");
            return;
        }
        if (contact.getId() == null) {//新建
            if (contact.getContactTag() == null) {
                contact.setContactTag("");
            }

            if (validateTag(tag)) return;
            String tags = contact.getContactTag();

            StringBuilder sb = new StringBuilder(tags != null ? tags : "");
            if (!TextUtils.isEmpty(tags)) {
                sb.append("、");
            }
            sb.append(tag);
            contact.setContactTag(sb.toString());
            mIView.updateTags(contact);
        } else {
            //更新
            if (validateTag(tag)) return;
            mIView.showWaitDialog(ResourcesUtils.getString(R.string.adding_tag));
            mRxManager.register(mIModel.updateContact(contact, false, tag)
                    .subscribe(new SimpleSuccessConsumer<RequestBaseBean>(mIView) {
                        @Override
                        public void onSuccess(RequestBaseBean response) {
                            String tags = contact.getContactTag();
                            StringBuilder sb = new StringBuilder(tags != null ? tags : "");
                            if (!TextUtils.isEmpty(tags)) {
                                sb.append("、");
                            }
                            sb.append(tag);
                            contact.setContactTag(sb.toString());
                            mIView.updateTags(contact);
                            RxBus.get().send(RX_BUS_UPDATE_CONTACT_BOOK, contact);
                        }
                    }, new SimpleErrorConsumer(mIView)));
        }
    }

    private boolean validateTag(String tag) {
        String tags = contact.getContactTag();
        if (!TextUtils.isEmpty(tags)) {
            String[] split = tags.split(SPLIT_TAG);
            if (split.length >= 10) {
                mIView.showToast("标签数量不能超过10个");
                return true;
            }

            for (String s : split) {
                if (TextUtils.equals(s, tag)) {
                    mIView.showToast("不允许添加重复的标签");
                    return true;
                }
            }
        }
        return false;
    }

    public void del(final String tag) {
        if (contact.getId() == null) {
            //新建直接删除
            String tags = contact.getContactTag();
            String[] split = tags.split("、");

            ArrayList<String> strings1 = new ArrayList<>();
            Collections.addAll(strings1, split);

            ListIterator<String> stringListIterator = strings1.listIterator();
            while (stringListIterator.hasNext()) {
                String next = stringListIterator.next();
                if (TextUtils.equals(next, tag)) {
                    stringListIterator.remove();
                    break;
                }
            }
            String s = StringUtils.listSplitByChar(strings1, '、');
            contact.setContactTag(s);
            mIView.updateTags(contact);
        } else {
            //更新
            String tags = contact.getContactTag();
            if (TextUtils.isEmpty(tags)) {
                return;
            }
            String[] split = tags.split(SPLIT_TAG);
            ArrayList<String> objects = new ArrayList<>();
            Collections.addAll(objects, split);

            Iterator<String> iterator = objects.iterator();
            while (iterator.hasNext()) {
                String next = iterator.next();
                if (TextUtils.equals(next, tag)) {
                    iterator.remove();
                }
            }

            String s = StringUtils.listSplitByChar(objects, '、');
            contact.setContactTag(s);
            mIView.showWaitDialog(ResourcesUtils.getString(R.string.del_tag));
            mRxManager.register(mIModel.delTag(contact, false)
                    .subscribe(new SimpleSuccessConsumer<RequestBaseBean>(mIView) {
                        @Override
                        public void onSuccess(RequestBaseBean response) {
                            mIView.updateTags(contact);
                            RxBus.get().send(RX_BUS_UPDATE_CONTACT_BOOK, contact);
                        }
                    }, new SimpleErrorConsumer(mIView)));
        }
    }

    public void updateContact(String name, String phone, Object store, int sex, String birthdayText, String positionText) {
        try {
            if (TextUtils.isEmpty(name)) {
                throw new Exception("联系人姓名不能为空");
            }
            String reg = EditUtil.ILLEGAL_PATTERN;
            Pattern pattern = Pattern.compile(reg, Pattern.DOTALL);
            Matcher matcher = pattern.matcher(name);
            if (matcher.find()) {
                throw new Exception("联系人姓名不能包含非法字符");
            }

            if (TextUtils.isEmpty(phone)) {
                throw new Exception("联系人手机号不能为空");
            }
//            if (phone.length() < 11 || phone.length() > 12) {
//                throw new Exception("请输入正确的手机号");
//            }

            if (!(store instanceof BaseDrugInfoBean)) {
                throw new Exception("请选择客户名称后提交");
            }


            if (((BaseDrugInfoBean) store).getId() == null || ((BaseDrugInfoBean) store).getRealName() == null) {
                throw new Exception("客户id不能为空");
            }
            if (sex == -1) {
                throw new Exception("性别不能为空");
            }
/*            if (TextUtils.isEmpty(birthdayText)) {
                throw new Exception("联系人生日不能为空");
            }*/
            if (TextUtils.isEmpty(positionText)) {
                throw new Exception("岗位不能为空");
            }

            ContactBean clone = contact.clone();
            clone.setContactName(name);
            clone.setContactMobile(phone);
            clone.setMerchantName(((BaseDrugInfoBean) store).getRealName());
            clone.setMerchantId(((BaseDrugInfoBean) store).getId());
            clone.setContactSex(sex);
            if (!TextUtils.isEmpty(birthdayText)) {
                clone.setContactBirth(birthdayText);
            }

            if (!TextUtils.isEmpty(positionText)) {
                clone.setContactJob(positionText);
            }

            HashMap<String, String> contactJobs = ContactConstants.contactJobs;
            Set<Map.Entry<String, String>> entries = contactJobs.entrySet();
            for (Map.Entry<String, String> next : entries) {
                String value = next.getValue();
                if (TextUtils.equals(value, positionText)) {
                    String key = next.getKey();
                    clone.setContactJob(key);
                    clone.setContactJobName(value);
                    break;
                }
            }
            clone.setPoiId(contact.getPoiId());
            clone.setPoiName(contact.getPoiName());

            String serialize = JsonUtils.serialize(clone);
            LogUtils.i(serialize);
            mIView.showWaitDialog("正在添加，请稍后...");
            //请求成功后 更新界面
            mRxManager.register(mIModel.updateContact(clone, contact.getId() == null, null)
                    .subscribe(new SimpleSuccessConsumer<RequestBaseBean<ContactBean>>(mIView) {
                        @Override
                        public void onSuccess(RequestBaseBean<ContactBean> requestBaseBean) {
                            ContactBean data = requestBaseBean.getData();
                            RxBus.get().send(RX_BUS_UPDATE_CONTACT_BOOK, data);
                            RxBus.get().send(RxBusCode.RX_BUS_UPDATE_SELECT_CONTACT_LIST);
                            mIView.finish();
                        }
                    }, new SimpleErrorConsumer(mIView)));
        } catch (Exception e) {
            e.printStackTrace();
            mIView.showToast(e.getMessage());
        }
    }

    public void getDrugInfo(String obj) {
//        mRxManager.register(mIModel.getDrugInfo(obj)
//                .subscribe(new SimpleSuccessConsumer<QueryMerchantsApi>(mIView) {
//                    @Override
//                    public void accept(QueryMerchantsApi baseBean) throws Exception {
//                        if (mIView == null) return;
//                        mIView.hideWaitDialog();
//                        if (baseBean.isSuccess()) {
//                            List<BaseDrugInfoBean> merchants = baseBean.getMerchants();
//                            mIView.renderMerchants(merchants);
//                        } else {
//                            List<BaseDrugInfoBean> merchants = new ArrayList<>();
//                            BaseDrugInfoBean infoBean = new BaseDrugInfoBean();
//                            infoBean.setRealName("暂无搜索结果");
//                            merchants.add(infoBean);
//                            mIView.renderMerchants(merchants);
//                        }
//                    }
//
//                    @Override
//                    public void onSuccess(QueryMerchantsApi response) {
//
//                    }
//                }, new SimpleErrorConsumer(mIView)));
    }
}
