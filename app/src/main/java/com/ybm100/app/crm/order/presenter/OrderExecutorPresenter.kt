package com.ybm100.app.crm.order.presenter

import com.ybm100.app.crm.task.bean.ExecutorLevelItem
import com.ybm100.app.crm.contract.BaseExecutorContract
import com.ybm100.app.crm.order.bean.OrderExecutorBean
import com.ybm100.app.crm.order.bean.OrderExecutorUserBean
import com.ybm100.app.crm.order.model.OrderExecutorModel
import com.ybm100.app.crm.presenter.BaseExecutorPresenter

/**
 * Created by dengmingjia on 2019/1/4
 */
class OrderExecutorPresenter(private val isTeamTask: Boolean = false, private val taskID: String = "", private val isPop: Boolean = false) : BaseExecutorPresenter<OrderExecutorBean>() {
    override fun getLevelItem(data: OrderExecutorBean, parent: ExecutorLevelItem?, showParent: Boolean): ExecutorLevelItem {
        var result = ExecutorLevelItem(data.id, data.name, false, null)
        result.child = getLevelItems(data.childGroup, data.sysUserList, result)
        return result
    }

    fun getLevelItems(data: List<OrderExecutorBean>?, users: List<OrderExecutorUserBean>?, parent: ExecutorLevelItem?): MutableList<ExecutorLevelItem> {
        var result = ArrayList<ExecutorLevelItem>()
        if (data != null) {
            for (item in data) {
                var executorLevelItem = ExecutorLevelItem(item.id, item.name, false, parent)
                executorLevelItem.child = getLevelItems(item.childGroup, item.sysUserList, executorLevelItem)
                result.add(executorLevelItem)
            }
        }
        if (users != null) {
            for (item in users) {
                var executorLevelItem = ExecutorLevelItem(item.id, item.realName, true, parent)
                result.add(executorLevelItem)
            }
        }
        return result
    }

    override fun getModel(): BaseExecutorContract.IModel<*> {
        return OrderExecutorModel(isTeamTask, taskID, isPop)
    }


}