package com.ybm100.app.crm.order.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.ybm100.app.crm.R
import com.ybm100.app.crm.order.bean.OrderBean

/**
 * 订单列表中的标签列表适配器
 */
class OrderLabelAdapter(preferentialList: List<OrderBean.PreferentialBean>?) : BaseQuickAdapter<OrderBean.PreferentialBean, BaseViewHolder>(R.layout.item_order_label, preferentialList)  {

    override fun convert(helper: BaseViewHolder?, item: OrderBean.PreferentialBean?) {
        // 标题
        helper?.setText(R.id.tv_title, item?.title)
        // 优惠
        helper?.setText(R.id.tv_preferential, item?.preferential)
    }

}