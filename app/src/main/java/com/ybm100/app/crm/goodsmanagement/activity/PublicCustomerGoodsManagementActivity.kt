package com.ybm100.app.crm.goodsmanagement.activity

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.fragment.app.Fragment
import androidx.viewpager.widget.ViewPager
import com.flyco.tablayout.listener.OnTabSelectListener
import com.xyy.userbehaviortracking.utils.UserBehaviorTrackingUtils
import com.xyy.utilslibrary.adapter.CommonPageAdapter
import com.ybm100.app.crm.R
import com.ybm100.app.crm.constant.Constants
import com.ybm100.app.crm.goodsmanagement.bean.Zone
import com.ybm100.app.crm.goodsmanagement.fragment.CustomerGoodsManagementFilterFragment
import com.ybm100.app.crm.goodsmanagement.fragment.GoodsManagementFragment
import com.ybm100.app.crm.ui.fragment.drugstore.minedrug.FrequentBuyCommodityFragment
import com.ybm100.app.crm.ui.fragment.drugstore.minedrug.ShoppingCommodityFragment
import kotlinx.android.synthetic.main.activity_customer_goods_management.*

class PublicCustomerGoodsManagementActivity : BaseDrawerActivity() {
    private var mCurrentTab: Int = 0
    private var mMerchantID: String = ""
    private var mTabTitles = arrayOf("常购商品", "购物车商品")
    private var mFragmentList = mutableListOf<Fragment>()

    override fun initTransferData() {
        super.initTransferData()
        try {
            intent?.data?.run {
                mMerchantID = getQueryParameter("shopId") ?: ""
                mCurrentTab = getQueryParameter("tab")?.toIntOrNull() ?: 0
            }
        } catch (e: Exception) {

        }
        if (mMerchantID.isNullOrEmpty()) {
            intent?.extras?.run {
                mCurrentTab = getInt(Constants.GoodsManagement.ARG_CUSTOMER_GOODS_MANAGEMENT_SELECTED_TAB, Constants.GoodsManagement.CONSTANT_CUSTOMER_GOODS_MANAGEMENT_TAB_FREQUENTLY_PURCHASED_GOODS)
                mMerchantID = getString(Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_MERCHANT_ID, "")
            }
        }
        initFragments()
    }

    override fun getContentMainLayoutID(): Int {
        return R.layout.activity_customer_goods_management
    }

    override fun initContentMain() {

        initViewPager()

        initSlidingTabLayout()

        registerListener()
    }

    private fun initSlidingTabLayout() {
        stl_tab.apply {
            setViewPager(view_pager)
        }

        stl_tab.currentTab = mCurrentTab
    }

    private fun initViewPager() {
        view_pager.apply {
            adapter = CommonPageAdapter(supportFragmentManager, mFragmentList, mTabTitles)
            offscreenPageLimit = mTabTitles.size
        }
    }

    private fun registerListener() {
        iv_back.setOnClickListener {
            finish()
        }
        iv_search.visibility = View.GONE
    }

    private fun initFragments() {
        mFragmentList.add(FrequentBuyCommodityFragment.newInstance(mMerchantID))
        mFragmentList.add(ShoppingCommodityFragment.newInstance(mMerchantID))
    }

    override fun getDrawerFragments(): List<Fragment> {
        return listOf()
    }

    companion object {
        /**
         * @param activity
         * @param selectedTab 选中的tab
         */
        @JvmStatic
        fun startActivity(activity: Activity?, merchantID: String?, selectedTab: Int? = Constants.GoodsManagement.CONSTANT_CUSTOMER_GOODS_MANAGEMENT_TAB_FREQUENTLY_PURCHASED_GOODS) {
            val intent = Intent(activity, PublicCustomerGoodsManagementActivity::class.java)
            val bundle = Bundle()

            bundle.putString(Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_MERCHANT_ID, merchantID
                    ?: "")
            bundle.putInt(Constants.GoodsManagement.ARG_CUSTOMER_GOODS_MANAGEMENT_SELECTED_TAB, selectedTab
                    ?: Constants.GoodsManagement.CONSTANT_CUSTOMER_GOODS_MANAGEMENT_TAB_FREQUENTLY_PURCHASED_GOODS)

            intent.putExtras(bundle)
            activity?.startActivity(intent)
        }
    }


}