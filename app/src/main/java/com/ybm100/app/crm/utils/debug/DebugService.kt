package com.ybm100.app.crm.utils.debug

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.os.Build
import android.os.IBinder
import com.didichuxing.doraemonkit.DoraemonKit.show
import com.xyy.utilslibrary.AppManager
import com.xyy.utilslibrary.UtilsLibraryApp
import com.xyy.utilslibrary.utils.DialogUtils
import com.ybm100.app.crm.R
import java.util.concurrent.TimeUnit
import kotlin.math.abs


class DebugService : Service() {

    /**
     * 摇一摇  start
     */
    private var mSensorManager: SensorManager? = null
    private var mSensorListener: SensorEventListener? = null

    private val mMinTimeBetweenSamplesNs = TimeUnit.NANOSECONDS.convert(20, TimeUnit.MILLISECONDS)
    private val mShakingWindowNs = TimeUnit.NANOSECONDS.convert(1, TimeUnit.SECONDS)
    private val mRequiredForce = SensorManager.GRAVITY_EARTH * 1.33f

    private var mShouldShowDialog = true
    private var mAccelerationX = 0f
    private var mAccelerationY = 0f
    private var mAccelerationZ = 0f
    private var mLastTimestamp = 0L
    private var mNumShakes = 0
    private var mLastShakeTimestamp = 0L
    private val mMinNumShakes = 1


    override fun onCreate() {
        super.onCreate()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager: NotificationManager =
                getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            var channel: NotificationChannel? = null
            channel = NotificationChannel(
                "debug_service",
                getString(R.string.app_name),
                NotificationManager.IMPORTANCE_HIGH
            )
            notificationManager.createNotificationChannel(channel)
            val notification = Notification.Builder(
                applicationContext, "debug_service"
            ).build()
            startForeground(1, notification)
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        /**
         * 摇一摇
         */
        mSensorListener = object : SensorEventListener {
            override fun onSensorChanged(event: SensorEvent) {
                if (event.timestamp - mLastTimestamp < mMinTimeBetweenSamplesNs) {
                    return
                }
                val ax = event.values[0]
                mLastTimestamp = event.timestamp
                if (atLeastRequiredForce(ax) && ax * mAccelerationX <= 0) {
                    recordShake(event.timestamp)
                    mAccelerationX = ax
                }
                maybeDispatchShake(event.timestamp)
            }

            override fun onAccuracyChanged(sensor: Sensor, accuracy: Int) {}
        }
        mSensorManager = getSystemService(SENSOR_SERVICE) as SensorManager
        mSensorManager?.registerListener(mSensorListener, mSensorManager?.getDefaultSensor(Sensor.TYPE_ACCELEROMETER), SensorManager.SENSOR_DELAY_UI)
        return super.onStartCommand(intent, flags, startId)
    }

    private fun atLeastRequiredForce(a: Float): Boolean {
        return abs(a) > mRequiredForce
    }

    private fun recordShake(timestamp: Long) {
        mLastShakeTimestamp = timestamp
        mNumShakes++
    }

    private fun maybeDispatchShake(currentTimestamp: Long) {
        if (currentTimestamp - mLastShakeTimestamp < mShakingWindowNs) {
            if (mNumShakes >= 6 * mMinNumShakes) {
                reset()
                if (mShouldShowDialog) {
                    mShouldShowDialog = false
                    val currentActivity = AppManager.getAppManager().currentActivity();
                    if (currentActivity == null || currentActivity.isDestroyed) {
                        return
                    }
                    DialogUtils.showCommonDialog(currentActivity, "打开 Debug 工具？", "取消", "确定", { dialog, which ->
                        UtilsLibraryApp.initDoraemon()
                        show()
                        mShouldShowDialog = true
                    }) { dialog, which -> mShouldShowDialog = true }
                }
            }
        } else {
            reset()
        }
    }

    private fun reset() {
        mNumShakes = 0
        mAccelerationX = 0f
        mAccelerationY = 0f
        mAccelerationZ = 0f
    }


    override fun onBind(intent: Intent?): IBinder? {
        return null
    }
}