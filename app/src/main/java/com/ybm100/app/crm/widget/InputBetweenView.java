package com.ybm100.app.crm.widget;

import android.content.Context;
import androidx.annotation.StringRes;
import androidx.constraintlayout.widget.ConstraintLayout;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.EditText;
import android.widget.TextView;

import com.ybm100.app.crm.R;

import butterknife.BindView;
import butterknife.ButterKnife;

/**
 * Created by dengmingjia on 2018/12/26
 */
public class InputBetweenView extends ConstraintLayout {
    @BindView(R.id.tv_title)
    TextView title;
    @BindView(R.id.edt_start)
    EditText inputStart;
    @BindView(R.id.edt_end)
    EditText inputEnd;
    @BindView(R.id.tv_unit)
    TextView unit;

    public InputBetweenView(Context context) {
        this(context, null);
    }

    public InputBetweenView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public InputBetweenView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        LayoutInflater.from(context).inflate(R.layout.view_input_between, this);
        ButterKnife.bind(this);
    }

    public void setTitle(@StringRes int resId) {
        title.setText(resId);
    }

    public void setUnit(@StringRes int resId) {
        unit.setText(resId);
    }

    public String[] getInputStrings() {
        String[] result = new String[2];
        result[0] = inputStart.getText().toString().trim();
        result[1] = inputEnd.getText().toString().trim();
        return result;
    }
}
