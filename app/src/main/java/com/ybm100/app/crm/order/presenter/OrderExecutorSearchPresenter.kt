package com.ybm100.app.crm.order.presenter

import com.ybm100.app.crm.contract.BaseSearchContract
import com.ybm100.app.crm.order.bean.OrderExecutorSearchBean
import com.ybm100.app.crm.order.model.OrderExecutorSearchModel
import com.ybm100.app.crm.presenter.BaseSearchPresenter

/**
 * Created by dengmingjia on 2019/1/8
 */
class OrderExecutorSearchPresenter(private val isPop: Boolean) : BaseSearchPresenter<OrderExecutorSearchBean>() {
    override fun getModel(): BaseSearchContract.IModel<*> {
        return OrderExecutorSearchModel(isPop)
    }
}