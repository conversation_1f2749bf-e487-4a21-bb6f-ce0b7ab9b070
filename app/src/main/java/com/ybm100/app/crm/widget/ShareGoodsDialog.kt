package com.ybm100.app.crm.widget

import android.content.Context
import com.ybm100.app.crm.R
import com.ybm100.app.crm.widget.drug.TransparentDialog
import kotlinx.android.synthetic.main.dialog_share_goods.*

/**
 * 分享微信弹框
 */
class ShareGoodsDialog(context: Context) : TransparentDialog(context) {
    override fun getLayoutId(): Int {
        return R.layout.dialog_share_goods
    }

    init {
        ll_share_wechat.setOnClickListener {
            onClickCallBack?.invoke(1)
            dismiss()
        }
        ll_share_moment.setOnClickListener {
            onClickCallBack?.invoke(2)
            dismiss()
        }
        ll_share_ybm.setOnClickListener {
            onClickCallBack?.invoke(3)
            dismiss()
        }
        cancel.setOnClickListener { v -> dismiss() }
    }

    var onClickCallBack: ((Int?) -> Unit)? = null
}
