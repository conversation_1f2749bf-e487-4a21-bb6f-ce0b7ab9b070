package com.ybm100.app.crm.contract.hycustomer;


import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.PrivateListFilterBean;
import com.ybm100.app.crm.bean.hycustomer.HyPrivateListBean;
import com.ybm100.app.crm.task.bean.TaskAndMerchantBean;

import java.util.HashMap;

import io.reactivex.Observable;

/**
 * 荷叶健康私海客户接口
 */
public interface HyPrivateListContract {

    interface IHyPrivateListModel extends IBaseModel {
        //我的药店列表
        Observable<RequestBaseBean<HyPrivateListBean>> getPrivateListData(HashMap<String, String> map);

        /**
         * 新建拜访（客户入口）
         *
         * @param merchantId
         * @param customerType 1客户2线索
         * @return
         */
        Observable<RequestBaseBean<TaskAndMerchantBean>> toAddVisit(String merchantId, String customerType);

        /**
         * 获取筛选条件
         */
        Observable<RequestBaseBean<PrivateListFilterBean>> getFilterItems();

        /**
         * 分配至bd
         */
        Observable<RequestBaseBean> distributeToBD(String bindUserId, String customerId);
    }

    interface IHyPrivateListView extends IBaseActivity {
        void getListDataSuccess(boolean refresh, RequestBaseBean<HyPrivateListBean> baseBean);

        void toAddVisit(RequestBaseBean<TaskAndMerchantBean> requestBaseBean);

        void distributeToBDSuccess(RequestBaseBean baseBean);

        void getFilterItemsSuccess(RequestBaseBean<PrivateListFilterBean> baseBean);

        void enableLoadMore(boolean b);

        void loadMoreComplete();

        void showEmpty();
    }

}
