package com.ybm100.app.crm.presenter.contact;

import android.text.TextUtils;

import com.chad.library.adapter.base.entity.MultiItemEntity;
import com.xyy.common.util.ToastUtils;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.utils.TimeUtils;
import com.ybm100.app.crm.bean.contact.ContactBean;
import com.ybm100.app.crm.bean.contact.ContactDependBean;
import com.ybm100.app.crm.bean.contact.ContactListApi;
import com.ybm100.app.crm.bean.contact.WrapCallLogBean;
import com.ybm100.app.crm.contract.contact.ContactListContract;
import com.ybm100.app.crm.model.contact.ContactListModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;
import com.ybm100.app.crm.presenter.BaseRecyclerPresenter;
import com.ybm100.app.crm.ui.adapter.messge.ContactAdapter;
import com.ybm100.app.crm.utils.CallLogUtil;
import com.ybm100.app.crm.widget.contact.CharacterParser;
import com.ybm100.app.crm.widget.contact.PinyinComparator;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import io.reactivex.Observable;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;

/**
 * Created by XyyMvpSportTemplate on 12/24/2018 10:26
 */
public class ContactListPresenter extends BaseRecyclerPresenter<ContactListContract.IContactListModel, ContactListContract.IContactListView, ContactListApi, MultiItemEntity> {

    private String keyword;
    private String merchantId;

    public static ContactListPresenter newInstance() {
        ContactListPresenter contactListPresenter = new ContactListPresenter();
        return contactListPresenter;
    }

    @Override
    protected int getDefaultPageNo() {
        return 0;
    }

    @Override
    protected int getDefaultPageSize() {
        return Integer.MAX_VALUE;
    }

    @Override
    protected ContactListModel getModel() {
        return ContactListModel.newInstance();
    }

    @Override
    protected Observable<ContactListApi> getObservable() {
        if (!TextUtils.isEmpty(merchantId)) {
            return mIModel.getContactsWithMerchat(pageNo, pageSize, keyword, merchantId);
        }
        return mIModel.getContacts(pageNo, pageSize, keyword);
    }

    public void getContacts(final boolean refresh, String keyword, String merchantId) {
        this.keyword = keyword;
        this.merchantId = merchantId;
        getData(refresh);
    }

    @Override
    protected List<MultiItemEntity> processData(ContactListApi data) {
        if (data.getData() == null || data.getData().getRows() == null || data.getData().getRows().size() == 0) {
            return new ArrayList<>();
        }
        final int count = data.getData().getRows().size();
        final List<MultiItemEntity> multiItemEntities = proceesList(data.getData().getRows());

        queryCallLog(count, multiItemEntities);
        return multiItemEntities;
    }

    public void queryCallLog(final int count, final List<MultiItemEntity> multiItemEntities) {
        ArrayList<String> strings = new ArrayList<>();
        for (MultiItemEntity multiItemEntity : multiItemEntities) {
            if (multiItemEntity instanceof ContactDependBean)
                continue;
            ContactBean contactBean = (ContactBean) multiItemEntity;
            strings.add(contactBean.getContactMobile());
        }


        Observable<WrapCallLogBean> callLogs = CallLogUtil.getCallLogs(mIView.getContext(), strings, TimeUtils.DATE_FORMAT_YMDHM);
        Disposable subscribe = callLogs.subscribe(new Consumer<WrapCallLogBean>() {
            int index = 0;

            @Override
            public void accept(WrapCallLogBean wrapCallLogBean) throws Exception {
//                LogUtils.i(wrapCallLogBean.toString());
                for (MultiItemEntity multiItemEntity : multiItemEntities) {
                    if (multiItemEntity instanceof ContactBean) {
                        String contactMobile = ((ContactBean) multiItemEntity).getContactMobile();
                        if (TextUtils.equals(contactMobile, wrapCallLogBean.getNumber())) {

                            ContactBean entity = (ContactBean) multiItemEntity;
                            entity.setTimeDistance(wrapCallLogBean.getTotalTime());
                            ContactDependBean dependBean = entity.getSubItem(0);
                            if (dependBean != null) {
                                dependBean.setTimeDistance(wrapCallLogBean.getTotalTime());
                            }

                            if (wrapCallLogBean.getList() != null && wrapCallLogBean.getList().size() > 0) {
                                entity.setLastCallTime(wrapCallLogBean.getList().get(0).getDateStr());
                            }
                            index++;

                            continue;
                        } else {
                            index++;
                        }
                    } else {
                        index++;
                    }
                }

                if (index == count) {
                    mIView.updateData();
                }
            }
        }, new Consumer<Throwable>() {
            @Override
            public void accept(Throwable throwable) throws Exception {
                if (throwable != null) {
                    throwable.printStackTrace();
                    ToastUtils.showShort("查询通话记录失败");
                }
            }
        });

        mRxManager.register(subscribe);
    }

    /**
     * 处理成结构数据
     *
     * @param list
     * @return
     */
    public List<MultiItemEntity> proceesList(List<ContactBean> list) {


        ArrayList<MultiItemEntity> contactBeans = new ArrayList<>();

        if (list != null) {
            for (ContactBean contactBean : list) {
                setSortKey(contactBean);
            }
            PinyinComparator pinyinComparator = new PinyinComparator();
            Collections.sort(list, pinyinComparator);

            contactBeans.addAll(list);
        }


        for (ContactBean contactBean : list) {
            ArrayList<ContactDependBean> contactDependBeans = new ArrayList<>();

            ContactDependBean contactDependBean = new ContactDependBean();
            contactDependBean.setPhoneNum(contactBean.getContactMobile());
            contactDependBean.setId(contactBean.getId());
            contactDependBean.setItemType(ContactAdapter.CONTACT_DEPEND);
            contactDependBeans.add(contactDependBean);

            contactBean.setSubItems(contactDependBeans);
        }
        return contactBeans;
    }

    public void setSortKey(ContactBean contactBean) {
        CharacterParser characterParser = CharacterParser.getInstance();
        String pinyin = characterParser.getSelling(contactBean.getContactName());
        contactBean.sortKey = pinyin;
        String sortString = pinyin.substring(0, 1).toUpperCase();

        // 正则表达式，判断首字母是否是英文字母
        if (sortString.matches("[A-Z]")) {
            contactBean.setSortLetters(sortString.toUpperCase());
        } else {
            contactBean.setSortLetters("#");
        }
    }

    public void delContact(ContactBean entity, final int parentPosition) {
        mRxManager.register(mIModel.delContact(entity.getId())
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean>(mIView) {
                    @Override
                    public void onSuccess(RequestBaseBean requestBaseBean) {
                        mIView.removeContact(parentPosition);
                    }
                }, new SimpleErrorConsumer(mIView)));
    }
}
