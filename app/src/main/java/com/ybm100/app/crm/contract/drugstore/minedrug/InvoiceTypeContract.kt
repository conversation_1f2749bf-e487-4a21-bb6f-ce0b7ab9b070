package com.ybm100.app.crm.contract.drugstore.minedrug

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.ybm100.app.crm.bean.drugstore.minedrugstore.InvoiceBean
import io.reactivex.Observable
import java.util.HashMap

/**
 * Created by XyyMvpYkqTemplate on 07/29/2019 11:33
 */
interface InvoiceTypeContract {

    interface IInvoiceTypeModel : IBaseModel {
        fun reqInvoiceData(map: HashMap<String, Any>): Observable<RequestBaseBean<InvoiceBean>>
    }

    interface IInvoiceTypeView : IBaseActivity {
        fun reqInvoiceSuccess(data: InvoiceBean?, forceRefresh: Boolean)
    }

}
