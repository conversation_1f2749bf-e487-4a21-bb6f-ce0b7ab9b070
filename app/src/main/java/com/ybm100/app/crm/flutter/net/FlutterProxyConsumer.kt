package com.ybm100.app.crm.flutter.net

import com.xyy.utilslibrary.base.IBaseView
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.ybm100.app.crm.utils.GsonUtils
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer
import io.reactivex.functions.Consumer

abstract class FlutterProxyConsumer(iBaseView: IBaseView) : Consumer<String?> {

    private var result: String? = null

    private var targetConsumer = object : SimpleSuccessConsumer<RequestBaseBean<*>>(iBaseView) {
        override fun onSuccess(t: RequestBaseBean<*>?) {
            <EMAIL>(result)
        }

        override fun onFailure(baseBean: RequestBaseBean<*>?) {
            <EMAIL>(baseBean)
        }
    }

    override fun accept(result: String?) {
        this.result = result
//        val requestBaseBean = extractRequestBaseBean(result)
//        targetConsumer.accept(requestBaseBean)

        if (result is String?) {
//            val requestBean = extractRequestBaseBean(result)
            onSuccess(result)
        }
    }


    private fun extractRequestBaseBean(result: String?): RequestBaseBean<*>? {
        result?.let {
            return GsonUtils.fromJson(it, RequestBaseBean::class.java)
        }
        return null
    }


    abstract fun onSuccess(result: String?)


    abstract fun onFailure(baseBean: RequestBaseBean<*>?)


}