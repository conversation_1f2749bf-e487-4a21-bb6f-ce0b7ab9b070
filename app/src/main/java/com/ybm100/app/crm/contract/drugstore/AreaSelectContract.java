package com.ybm100.app.crm.contract.drugstore;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.AreaBean;

import java.util.List;

import io.reactivex.Observable;

/**
 * @author: zcj
 * @time:2020/3/29. Description:
 */
public interface AreaSelectContract {

    interface IAreaSelectModel extends IBaseModel {
        //公海列表
        Observable<RequestBaseBean<List<AreaBean>>> searchArea(String areaCode);
        Observable<RequestBaseBean<List<AreaBean>>> searchAreaV2(String areaCode);
    }

    interface IAreaSelectView extends IBaseActivity {
        void searchAreaSuccess(int currLevel,RequestBaseBean<List<AreaBean>> baseBean);
    }
}
