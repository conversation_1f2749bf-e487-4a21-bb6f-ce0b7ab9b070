package com.ybm100.app.crm.presenter;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.IBaseRecyclerView;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

import java.util.List;

import io.reactivex.Observable;
import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR>
 * @version 1.0
 * @file BaseRecyclerPresenter.java
 * @brief 列表Presenter
 * @date 2018/12/25
 * Copyright (c) 2018, 北京小药药
 * All rights reserved.
 */
public abstract class BaseRecyclerPresenter<M extends IBaseModel, V extends IBaseRecyclerView, T extends RequestBaseBean, K> extends BasePresenter<M, V> {


    protected int pageSize = getDefaultPageSize();
    protected int pageNo = getDefaultPageNo();

    protected int getDefaultPageNo() {
        return 0;
    }

    protected int getDefaultPageSize() {
        return 10;
    }

    protected abstract Observable<T> getObservable();

    protected void getData(final boolean refresh) {
        if (refresh) {
            pageNo = getDefaultPageNo();
            mIView.enableLoadMore(true);
        }

        Observable<T> observable = getObservable();
        if (observable != null) {
            Disposable subscribe = observable.subscribe(new SimpleSuccessConsumer<T>(mIView,"") {
                @Override
                public void onSuccess(T response) {
                    List<K> result = processData(response);

                    if (result != null) {

                        if (result.size() < pageSize) {
                            if (pageNo == getDefaultPageNo()) {
                                mIView.enableLoadMore(false);//第一页加载完成,直接禁止加载更多
                            } else {
                                mIView.loadMoreComplete();//超出一页没有更多的数据
                            }
                        }

                        mIView.renderList(refresh, result);
                    }

                    pageNo++;
                }
            }, new SimpleErrorConsumer(mIView) {
                @Override
                public void accept(Throwable throwable) throws Exception {
                    super.accept(throwable);
                    mIView.showNetError();
                }
            });
            mRxManager.register(subscribe);
        } else {

        }
    }

    protected abstract List<K> processData(T data);
}
