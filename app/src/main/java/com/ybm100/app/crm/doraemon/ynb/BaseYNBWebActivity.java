package com.ybm100.app.crm.doraemon.ynb;

import android.content.Intent;
import android.graphics.Bitmap;
import android.net.http.SslError;
import android.os.Build;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.CookieManager;
import android.webkit.SslErrorHandler;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebView;

import androidx.annotation.ColorInt;
import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;

import com.just.ynbweb.DefaultWebClient;
import com.just.ynbweb.HandlerInterface;
import com.just.ynbweb.IAgentWebSettings;
import com.just.ynbweb.IWebLayout;
import com.just.ynbweb.JsHandlerFactory;
import com.just.ynbweb.MiddlewareWebChromeBase;
import com.just.ynbweb.MiddlewareWebClientBase;
import com.just.ynbweb.PermissionInterceptor;
import com.just.ynbweb.WebChromeClient;
import com.just.ynbweb.WebViewClient;
import com.just.ynbweb.YNBWeb;
import com.just.ynbweb.YNBWebConfig;
import com.just.ynbweb.YNBWebSettingsImpl;
import com.just.ynbweb.YNBWebUIControllerImplBase;
import com.just.ynbweb.handler.AppJsHandler;
import com.just.ynbweb.handler.ChooseImgHandler;
import com.just.ynbweb.handler.CloseYNBHandler;
import com.just.ynbweb.handler.DeviceJsHandler;
import com.just.ynbweb.handler.DownloadFileHandler;
import com.just.ynbweb.handler.LocationHandler;
import com.just.ynbweb.handler.MultipleChooseImageHandler;
import com.just.ynbweb.handler.OpenWebViewHandler;
import com.just.ynbweb.handler.ResetNightButtonHandler;
import com.just.ynbweb.handler.SavePhotoToAlbumsHandler;
import com.just.ynbweb.handler.ShareForWeChatHandler;
import com.just.ynbweb.handler.TitleBarHandler;
import com.just.ynbweb.handler.TitleBarRightHandler;
import com.just.ynbweb.handler.UploadImageHandler;
import com.just.ynbweb.handler.UserJsHandler;
import com.xyy.utilslibrary.base.activity.BaseCompatActivity;
import com.ybm100.app.crm.BuildConfig;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.api.ApiUrl;
import com.ybm100.app.crm.constant.AppNetConfig;
import com.ybm100.app.crm.utils.SharedPrefManager;

public abstract class BaseYNBWebActivity extends BaseCompatActivity implements HandlerInterface {

    protected YNBWeb mAgentWeb;
    private YNBWebUIControllerImplBase mAgentWebUIController;
    private ErrorLayoutEntity mErrorLayoutEntity;
    private MiddlewareWebChromeBase mMiddleWareWebChrome;
    private MiddlewareWebClientBase mMiddleWareWebClient;

    protected abstract void uesWebTitle(String title);

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

    }

    @Override
    public void setContentView(@LayoutRes int layoutResID) {
        super.setContentView(layoutResID);
        buildAgentWeb();
    }

    @Override
    public void setContentView(View view) {
        super.setContentView(view);
        buildAgentWeb();
    }

    protected void buildAgentWeb() {
        //设置错误页面
        ErrorLayoutEntity mErrorLayoutEntity = getErrorLayoutEntity();
        mErrorLayoutEntity.setReloadId(R.id.bt_afreshLoad);
        mAgentWeb = YNBWeb.with(this)
                .setAgentWebParent(getAgentWebParent(), new ViewGroup.LayoutParams(-1, -1))
                .useDefaultIndicator(getIndicatorColor(), getIndicatorHeight())
                .setWebChromeClient(getWebChromeClient())
                .setWebViewClient(mWebViewClient)
                .setWebView(getWebView())
                .setPermissionInterceptor(getPermissionInterceptor())
                .setWebLayout(getWebLayout())
                .setAgentWebUIController(getAgentWebUIController())
                .interceptUnkownUrl()
                .additionalHttpHeader(getUrl(), "TGC", SharedPrefManager.getInstance().getUserInfo().getToken())
                .setOpenOtherPageWays(getOpenOtherAppWay())
                .useMiddlewareWebChrome(getMiddleWareWebChrome())
                .useMiddlewareWebClient(getMiddleWareWebClient())
                .setAgentWebWebSettings(getAgentWebSettings())
                .setMainFrameErrorView(mErrorLayoutEntity.layoutRes, mErrorLayoutEntity.reloadId)
                .setSecurityType(YNBWeb.SecurityType.STRICT_CHECK)
                .createAgentWeb()
                .ready()
                .addJsInterFaceHolder(this)//注入注定桥
                .go(getUrl());
        resetCacheMode();
        String cookies = "TGC=" + SharedPrefManager.getInstance().getUserInfo().getToken();
        syncCookie(ApiUrl.getH5BaseUrl(), cookies);
        syncCookie(ApiUrl.getH5Domain(), "sourceType=crm");
        try {
            JsHandlerFactory.registerHandlerWithName("getAppInfo", AppJsHandler.getClassName);
            JsHandlerFactory.registerHandlerWithName("getDeviceInfo", DeviceJsHandler.getClassName);
            JsHandlerFactory.registerHandlerWithName("getUserInfo", UserJsHandler.getClassName);
            JsHandlerFactory.registerHandlerWithName("getLocation", LocationHandler.getClassName);
            JsHandlerFactory.registerHandlerWithName("chooseImage", ChooseImgHandler.getClassName);
            JsHandlerFactory.registerHandlerWithName("setTitle", TitleBarHandler.getClassName);
            JsHandlerFactory.registerHandlerWithName("setNavRightButton", TitleBarRightHandler.getClassName);
            JsHandlerFactory.registerHandlerWithName("resetNavRightButton", ResetNightButtonHandler.getClassName);
            JsHandlerFactory.registerHandlerWithName("openWebView", OpenWebViewHandler.getClassName);
            JsHandlerFactory.registerHandlerWithName("closeWebView", CloseYNBHandler.getClassName);
            JsHandlerFactory.registerHandlerWithName("multipleChooseImage", MultipleChooseImageHandler.getClassName);
            JsHandlerFactory.registerHandlerWithName("uploadImage", UploadImageHandler.getClassName);
            JsHandlerFactory.registerHandlerWithName("savePhotoToAlbums", SavePhotoToAlbumsHandler.getClassName);
            JsHandlerFactory.registerHandlerWithName("shareForWeChat", ShareForWeChatHandler.getClassName);
            JsHandlerFactory.registerHandlerWithName("downloadFile", DownloadFileHandler.getClassName);
            JsHandlerFactory.registerHandlerWithName("previewFile", PreviewPdfHandler.getClassName);
        } catch (Exception e) {
            e.printStackTrace();
        }
        mAgentWeb.getJsAccessEntrace().quickCallJs("");
        if (!AppNetConfig.FlavorType.PROD.equals(BuildConfig.FLAVOR) || BuildConfig.DEBUG) {
            YNBWebConfig.debug();
        }
    }


    // 设置cacheMode为不使用缓存，防止h5发版导致的页面不可用
    private void resetCacheMode() {
        if (mAgentWeb == null
                || mAgentWeb.getAgentWebSettings() == null
                || mAgentWeb.getAgentWebSettings().getWebSettings() == null) {
            return;
        }
        WebSettings webSettings = mAgentWeb.getAgentWebSettings().getWebSettings();
        webSettings.setCacheMode(WebSettings.LOAD_NO_CACHE);
    }

    private void syncCookie(String url, String cookies) {
        CookieManager cookieManager = CookieManager.getInstance();
        cookieManager.setAcceptCookie(true);
        cookieManager.setCookie(url, cookies + ";Domain=" + ApiUrl.getH5Domain());
        cookieManager.flush();
    }

    /**
     * 注意，重写WebViewClient的方法,super.xxx()请务必正确调用， 如果没有调用super.xxx(),则无法执行DefaultWebClient的方法
     * 可能会影响到AgentWeb自带提供的功能,尽可能调用super.xxx()来完成洋葱模型
     */
    protected WebViewClient mWebViewClient = new WebViewClient() {

        @Override
        public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
            super.onReceivedError(view, request, error);
        }

        @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
        @Override
        public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
            return super.shouldOverrideUrlLoading(view, request);
        }

        @Nullable
        @Override
        public WebResourceResponse shouldInterceptRequest(WebView view, WebResourceRequest request) {
            return super.shouldInterceptRequest(view, request);
        }

        @Override
        public void onPageStarted(WebView view, String url, Bitmap favicon) {
            super.onPageStarted(view, url, favicon);
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            super.onPageFinished(view, url);

        }

        @Override
        public void onReceivedHttpError(WebView view, WebResourceRequest request, WebResourceResponse errorResponse) {
            super.onReceivedHttpError(view, request, errorResponse);
        }

        @Override
        public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
            handler.proceed();
            super.onReceivedSslError(view, handler, error);
        }
    };


    protected @NonNull
    ErrorLayoutEntity getErrorLayoutEntity() {
        if (this.mErrorLayoutEntity == null) {
            this.mErrorLayoutEntity = new ErrorLayoutEntity();
        }
        return mErrorLayoutEntity;
    }

    protected YNBWeb getAgentWeb() {
        return this.mAgentWeb;
    }


    protected static class ErrorLayoutEntity {
        private int layoutRes = R.layout.platform_status_view_layout_error_no_network;
        private int reloadId;

        public void setLayoutRes(int layoutRes) {
            this.layoutRes = layoutRes;
            if (layoutRes <= 0) {
                layoutRes = -1;
            }
        }

        public void setReloadId(int reloadId) {
            this.reloadId = reloadId;
            if (reloadId <= 0) {
                reloadId = -1;
            }
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {

        if (mAgentWeb != null && mAgentWeb.handleKeyEvent(keyCode, event)) {
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onPause() {
        if (mAgentWeb != null) {
            mAgentWeb.getWebLifeCycle().onPause();
        }
        super.onPause();

    }

    @Override
    protected void onResume() {
        if (mAgentWeb != null) {
            mAgentWeb.getWebLifeCycle().onResume();
        }
        super.onResume();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
    }


    @Override
    protected void onDestroy() {
        if (mAgentWeb != null) {
            mAgentWeb.getWebLifeCycle().onDestroy();
            JsHandlerFactory.clearHandler();
        }
        super.onDestroy();
    }


    protected
    @Nullable
    String getUrl() {
        return null;
    }

    public @Nullable
    IAgentWebSettings getAgentWebSettings() {
        return YNBWebSettingsImpl.getInstance();
    }

    protected abstract @NonNull
    ViewGroup getAgentWebParent();

    protected @Nullable
    WebChromeClient getWebChromeClient() {
        return null;
    }

    protected @ColorInt
    int getIndicatorColor() {
        return -1;
    }

    protected int getIndicatorHeight() {
        return -1;
    }

    protected @Nullable
    WebViewClient getWebViewClient() {
        return null;
    }


    protected @Nullable
    WebView getWebView() {
        return null;
    }

    protected @Nullable
    IWebLayout getWebLayout() {
        return null;
    }

    protected @Nullable
    PermissionInterceptor getPermissionInterceptor() {
        return null;
    }

    public @Nullable
    YNBWebUIControllerImplBase getAgentWebUIController() {
        return null;
    }

    public @Nullable
    DefaultWebClient.OpenOtherPageWays getOpenOtherAppWay() {
        return null;
    }

    protected @NonNull
    MiddlewareWebChromeBase getMiddleWareWebChrome() {
        return this.mMiddleWareWebChrome = new MiddlewareWebChromeBase() {
            @Override
            public void onReceivedTitle(WebView view, String title) {
                super.onReceivedTitle(view, title);
                setTitle(view, title);
            }
        };
    }

    protected void setTitle(WebView view, String title) {
        uesWebTitle(title);
    }

    protected @NonNull
    MiddlewareWebClientBase getMiddleWareWebClient() {
        return this.mMiddleWareWebClient = new MiddlewareWebClientBase() {
        };
    }
}
