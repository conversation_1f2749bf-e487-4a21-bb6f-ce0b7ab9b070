package com.ybm100.app.crm.utils.deviceInfo.collector

import android.content.Context
import android.net.LocalServerSocket


class SandBoxCollector : BaseCollector() {
    override fun internalCollect(context: Context): String? {
        return checkByCreateLocalServerSocket()
    }

    companion object {
        @Volatile
        private var localServerSocket: LocalServerSocket? = null
    }

    private fun checkByCreateLocalServerSocket(): String {
        return if (localServerSocket != null) "normal" else try {
            localServerSocket = LocalServerSocket("#6*ZZLu6")
            "normal"
        } catch (e: Throwable) {
            return e.message ?: "error"
        }
    }

}
