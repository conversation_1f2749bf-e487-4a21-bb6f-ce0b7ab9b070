package com.ybm100.app.crm.presenter.home;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.home.NotificationBean;
import com.ybm100.app.crm.bean.home.PwdFlagBean;
import com.ybm100.app.crm.bean.lzcustomer.LzRoleBean;
import com.ybm100.app.crm.bean.message.MessageReadCountBean;
import com.ybm100.app.crm.contract.home.MainContract;
import com.ybm100.app.crm.model.home.MainModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

import java.util.HashMap;
import java.util.List;

/**
 * Created by XyyMvpSportTemplate on 12/19/2018 18:18
 */
public class MainPresenter extends BasePresenter<MainContract.IMainModel, MainContract.IMainView> {

    public static MainPresenter newInstance() {
        return new MainPresenter();
    }

    @Override
    protected MainModel getModel() {
        return MainModel.newInstance();
    }

    public void getMsgReadCount() {
        mRxManager.register(getModel().getMsgCount().subscribe(new SimpleSuccessConsumer<MessageReadCountBean>(mIView) {
            @Override
            public void onSuccess(MessageReadCountBean bean) {
                mIView.renderCountSuccess(bean);
            }
        }, new SimpleErrorConsumer(mIView)));
    }

    public void requestNotification(HashMap<String, Object> map) {
        if (mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.requestNotification(map).subscribe(new SimpleSuccessConsumer<RequestBaseBean<NotificationBean>>(mIView) {
            @Override
            public void onSuccess(RequestBaseBean<NotificationBean> bean) {
                mIView.onRequestNotificationSuccess(bean.getData());
            }
        }, new SimpleErrorConsumer(mIView)));
    }

    public void changePwdFlag() {
        if (mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.changePwdFlag().subscribe(new SimpleSuccessConsumer<RequestBaseBean<PwdFlagBean>>(mIView) {
            @Override
            public void onSuccess(RequestBaseBean<PwdFlagBean> bean) {
                mIView.changePwdFlagSuccess(bean.getData());
            }
        }, new SimpleErrorConsumer(mIView)));
    }

    public void getRoles() {
        if (mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.getRoles().subscribe(new SimpleSuccessConsumer<RequestBaseBean<List<LzRoleBean>>>(mIView) {
            @Override
            public void onSuccess(RequestBaseBean<List<LzRoleBean>> listRequestBaseBean) {
                mIView.getRolesSuccess(listRequestBaseBean.getData());
            }
        }, new SimpleErrorConsumer(mIView)));
    }
}
