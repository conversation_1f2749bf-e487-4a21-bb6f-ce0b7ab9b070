package com.ybm100.app.crm.global

import android.content.Context
import androidx.fragment.app.FragmentActivity
import com.xyy.common.util.ToastUtils
import com.xyy.flutter.container.container.bridge.IBridgeImpl
import com.xyy.flutter.container.container.bridge.callback.LocationCallback
import com.xyy.flutter.container.container.bridge.callback.RequestCallback
import com.xyy.flutter.container.container.bridge.callback.UploadCallback
import com.xyy.flutter.container.container.ui.FlutterRunnerActivity
import com.ybm100.app.crm.flutter.channel.LocationHandler
import com.ybm100.app.crm.flutter.channel.UploadPhotoHandler
import com.ybm100.app.crm.flutter.net.NetworkHandler
import com.ybm100.app.crm.utils.BuglyUtil

class FlutterBridgeImpl : IBridgeImpl {
    override fun locate(context: Context, callback: LocationCallback) {
        LocationHandler().handle(context, callback)
    }

    override fun toast(context: Context, msg: String?, type: String?) {
        ToastUtils.showShortSafe(msg)
    }

    override fun uploadImage(
        activity: FragmentActivity,
        uploadUrl: String,
        localPaths: List<String>,
        limitWidth: Int,
        limitHeight: Int,
        param: UploadCallback,
        extraParams: Map<String, Any?>,
        isUploadOrigin: Boolean
    ) {
        UploadPhotoHandler().handle(activity, uploadUrl, localPaths, limitWidth, limitHeight, param, extraParams, isUploadOrigin)
    }

    override fun request(
            activity: Context,
            method: String,
            path: String,
            contentType: String,
            requestParams: Any?,
            headerMap: Map<String, String>,
            param: RequestCallback
    ) {
        NetworkHandler().handle(
                activity,
                method,
                path,
                contentType,
                requestParams,
                headerMap,
                param
        )
    }

    override fun handleError(activity: Context, errorDetail: String?) {
        BuglyUtil.manuallyPostException(errorDetail)
    }

}
