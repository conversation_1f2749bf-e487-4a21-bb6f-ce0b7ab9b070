package com.ybm100.app.crm.presenter.lzcustomer;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.lzcustomer.LzPublicListBean;
import com.ybm100.app.crm.contract.lzcustomer.LzPublicListContract;
import com.ybm100.app.crm.model.lzcustomer.LzPublicListModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * <AUTHOR>
 * Created by XyyMvpSportTemplate on 12/29/2018 16:48
 * 公海客户
 */
public class LzPublicListPresenter extends BasePresenter<LzPublicListContract.ILzPublicListModel, LzPublicListContract.ILzPublicListView> {
    int pageSize = 10;
    int pageNo = 0;

    public static LzPublicListPresenter newInstance() {
        return new LzPublicListPresenter();
    }

    @Override
    protected LzPublicListModel getModel() {
        return LzPublicListModel.newInstance();
    }

    /**
     * 获取未认领药店列表
     */
    public void searchOpenSea(final boolean refresh, HashMap<String, String> map) {
        if (mIView == null || mIModel == null) return;
        if (refresh) {
            pageNo = 0;
            mIView.enableLoadMore(true);
        }
        if (map == null) {
            map = new HashMap<>();
        }
        map.put("offset", String.valueOf(pageNo));
        map.put("limit", String.valueOf(pageSize));
        mRxManager.register(mIModel.searchOpenSea(map)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean<LzPublicListBean>>(mIView, true) {
                    @Override
                    public void onSuccess(RequestBaseBean<LzPublicListBean> listRequestBaseBean) {
                        if (mIView == null) return;
                        if (listRequestBaseBean.getData() == null || listRequestBaseBean.getData().getRows() == null) {
                            mIView.showEmpty();
                            return;
                        }
                        ArrayList<LzPublicListBean.RowBean> result = listRequestBaseBean.getData().getRows();
                        if (result != null) {
                            if (listRequestBaseBean.getData().getLastPage()) {
                                mIView.loadMoreComplete();//超出一页没有更多的数据
                            }
                            mIView.searchOpenSeaSuccess(refresh, listRequestBaseBean);
                        }
                        pageNo++;
                    }

                    @Override
                    public void onFailure(int errorCode) {
                        super.onFailure(errorCode);
                        if (mIView == null) return;
                        mIView.showNetError();
                    }
                }, new SimpleErrorConsumer(mIView) {
                    @Override
                    protected void onError(Throwable throwable, String msg) {
                        super.onError(throwable, msg);
                        if (mIView == null) return;
                        mIView.showNetError();
                    }
                }));
    }

    /**
     * 认领药店
     */
    public void receive(String merchantId) {
        if (mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.receive(merchantId)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean>(mIView, "") {
                    @Override
                    public void accept(RequestBaseBean baseBean) throws Exception {
                        if (mIView == null) return;
                        mIView.hideWaitDialog();
                        if (baseBean.isSuccess()) {
                            mIView.receiveSuccess(baseBean);
                        } else if (!baseBean.isSuccess() && (baseBean.getErrorCode() == 405 || baseBean.getErrorCode() == 406)) {
                            mIView.receiveSuccess(baseBean);
                        } else {
                            mIView.showToast(baseBean.getErrorMsg());
                        }
                    }

                    @Override
                    public void onSuccess(RequestBaseBean baseBean) {

                    }
                }, new SimpleErrorConsumer(mIView) {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        if (mIView == null) return;
                        mIView.hideWaitDialog();
                        mIView.showToast("认领失败:" + throwable.getMessage());
                    }
                }));
    }

}
