package com.ybm100.app.crm.contract.pdf;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;

import io.reactivex.Observable;
import okhttp3.ResponseBody;

public interface PdfDisplayContract {

    interface IPdfDisplayModel extends IBaseModel {

        Observable<ResponseBody> downloadPdfFile(String url);
    }

    interface IPdfDisplayView extends IBaseActivity {

        void downloadSuccess(String path, boolean isPdf);

        void downloadFailure(String errorMsg);

    }
}
