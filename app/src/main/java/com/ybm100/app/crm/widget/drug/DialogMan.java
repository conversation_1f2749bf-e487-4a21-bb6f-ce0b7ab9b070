package com.ybm100.app.crm.widget.drug;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.ybm100.app.crm.R;


public class DialogMan extends TransparentDialog {

    private final LinearLayout buttonContainer;

    private final TextView confirm1;
    private final TextView confirm;
    private final TextView cancel;
    private final TextView content;
    private final TextView title;

    private DialogMan(@NonNull Context context) {
        super(context);

        buttonContainer = findViewById(R.id.buttonContainer);
        confirm1 = findViewById(R.id.confirm1);
        confirm = findViewById(R.id.confirm);
        cancel = findViewById(R.id.cancel);
        content = findViewById(R.id.content);
        title = findViewById(R.id.title);

        //默认点取消对话框消失
        cancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (listener != null) {
                    if (listener.cancel(DialogMan.this)) {
                        dismiss();
                    }
                } else {
                    dismiss();
                }
            }
        });

        //只有一个确定按钮的时候点击对话框也消失
        confirm1.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (listener != null) {
                    if (listener.confirm(DialogMan.this)) {
                        dismiss();
                    }
                } else {
                    dismiss();
                }
            }
        });

        confirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (listener != null) {
                    if (listener.confirm(DialogMan.this)) {
                        dismiss();
                    }
                } else {
                    dismiss();
                }
            }
        });
    }


    @Override
    public int getLayoutId() {

        return R.layout.dialog_man;
    }


    private OnClickListener listener;

    public void setOnClickListener(@NonNull OnClickListener listener) {
        this.listener = listener;
    }

    public interface OnClickListener {

        //return true if need dismiss;
        boolean confirm(DialogMan dialogMan);

        //return true if need dismiss;
        boolean cancel(DialogMan dialogMan);

    }

    @Override
    public boolean centerInWindow() {
        return true;
    }

    public static class Builder {

        private String cancelText;

        private String confirmText;

        private String content;

        private String title;

        private final DialogMan dialogMan;

        private int cancelTextColor;

        private int confirmTextColor;

        private DialogMan.OnClickListener listener;

        public Builder(Context context) {

            dialogMan = new DialogMan(context);

        }

        public Builder setTitle(String text) {
            title = text;
            return this;

        }

        public Builder setCancelable(boolean flag) {

            dialogMan.setCancelable(flag);

            return this;
        }

        public Builder setContent(String text) {
            content = text;
            return this;
        }

        public Builder setCancelText(String text) {
            cancelText = text;
            return this;
        }

        public Builder setConfirmText(String text) {
            confirmText = text;
            return this;
        }

        public Builder setListener(DialogMan.OnClickListener listener) {
            this.listener = listener;
            return this;
        }

        public Builder setCancelTextColor(int cancelTextColor) {
            this.cancelTextColor = cancelTextColor;
            return this;
        }

        public Builder setConfirmTextColor(int confirmTextColor) {
            this.confirmTextColor = confirmTextColor;
            return this;
        }
        public DialogMan buildAndShow() {
            if (TextUtils.isEmpty(cancelText)) {
                //仅显示确定按钮
                dialogMan.confirm1.setVisibility(View.VISIBLE);
                dialogMan.confirm1.setText(confirmText);
            } else {
                //显示两个按钮
                dialogMan.buttonContainer.setVisibility(View.VISIBLE);
                dialogMan.cancel.setText(cancelText);
                dialogMan.confirm.setText(confirmText);
            }

            //是否显示内容
            if (!TextUtils.isEmpty(content)) {
                dialogMan.content.setVisibility(View.VISIBLE);
                dialogMan.content.setText(content);
            }
            if (cancelTextColor != 0) {
                dialogMan.cancel.setTextColor(cancelTextColor);
            }
            if (confirmTextColor != 0) {
                dialogMan.confirm.setTextColor(confirmTextColor);
                dialogMan.confirm1.setTextColor(confirmTextColor);
            }
            dialogMan.title.setText(title);
            dialogMan.setOnClickListener(listener);
            dialogMan.show();
            return dialogMan;
        }

    }


}
