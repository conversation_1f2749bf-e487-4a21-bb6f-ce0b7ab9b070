package com.ybm100.app.crm.utils;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RelativeLayout.LayoutParams;
import android.widget.TextView;

import com.xyy.common.util.Abase;
import com.xyy.utilslibrary.utils.GlideLoadUtils;
import com.ybm100.app.crm.R;

public class ShowBigBitmapPopPublish {
    private final PopupWindow popwindow;
    private final Context context;
    private final ImageView ivBigImage;
    private int mPosition;

    public ShowBigBitmapPopPublish(String picPath) {
        this(picPath, 0, null);
    }

    public ShowBigBitmapPopPublish(String picPath, final int pos, final RefreshGvListener listener) {
        this(picPath, pos, listener, Abase.getContext());
    }

    public ShowBigBitmapPopPublish(String picPath, final int pos, final RefreshGvListener listener, Context context) {
        if (context != null) {
            this.context = context;
        } else {
            this.context = Abase.getContext();
        }
        View viewBigBitmap = LayoutInflater.from(this.context).inflate(R.layout.view_big_bitmap_publish, null);
        ivBigImage = viewBigBitmap.findViewById(R.id.iv_big_bitmap);
        TextView tvDelImg = viewBigBitmap.findViewById(R.id.tv_del_img);
        LinearLayout llDelPic = viewBigBitmap.findViewById(R.id.ll_del_pic);
        if (listener != null) {
            llDelPic.setVisibility(View.VISIBLE);
        } else {
            llDelPic.setVisibility(View.GONE);
        }
        mPosition = pos;
        popwindow = new PopupWindow(viewBigBitmap, LayoutParams.FILL_PARENT, LayoutParams.FILL_PARENT, true);
        popwindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        popwindow.setAnimationStyle(R.style.AnimBottom);

        setData(picPath, pos, false, null);

        tvDelImg.setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View arg0) {
                popwindow.dismiss();
                if (listener != null) {
                    listener.refreshGv(mPosition);
                }
            }
        });
        ivBigImage.setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View arg0) {
                popwindow.dismiss();
            }
        });
    }

    /**
     * 加载本地资源
     *
     * @param res
     */
    public ShowBigBitmapPopPublish(int res) {
        this.context = Abase.getContext();
        View viewBigBitmap = LayoutInflater.from(this.context).inflate(R.layout.view_big_bitmap_publish, null);
        ivBigImage = viewBigBitmap.findViewById(R.id.iv_big_bitmap);
        viewBigBitmap.findViewById(R.id.ll_del_pic).setVisibility(View.GONE);
        popwindow = new PopupWindow(viewBigBitmap, LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT, true);
        //sdk > 21 解决 标题栏没有办法遮罩的问题
        popwindow.setClippingEnabled(false);
        popwindow.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        popwindow.setAnimationStyle(R.style.AnimBottom);
        ivBigImage.setImageResource(res);
        ivBigImage.setOnClickListener(arg0 -> popwindow.dismiss());
    }

    public void setData(String path, int position, boolean show, View token) {
        if (TextUtils.isEmpty(path) || ivBigImage == null) {
            return;
        }
        mPosition = position;
        GlideLoadUtils.loadImg(Abase.getContext(), ivBigImage, path);
        if (show && token != null) {
            show(token);
        }
    }

    public void show(View parent) {
        if (popwindow == null) {
            return;
        }
        if (popwindow.isShowing()) popwindow.dismiss();
        else {
            popwindow.showAtLocation(parent, Gravity.BOTTOM, 0, 0);
            popwindow.update();
        }
    }

    public interface RefreshGvListener {
        void refreshGv(int pos);
    }
}
