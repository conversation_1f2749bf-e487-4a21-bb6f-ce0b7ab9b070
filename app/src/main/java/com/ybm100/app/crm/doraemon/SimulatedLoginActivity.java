package com.ybm100.app.crm.doraemon;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.xyy.common.util.ToastUtils;
import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.activity.BaseMVPCompatActivity;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.api.ApiService;
import com.ybm100.app.crm.bean.SimulatedLoginBean;
import com.ybm100.app.crm.bean.user.UserInfoBean;
import com.ybm100.app.crm.net.RetrofitCreateHelper;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;
import com.ybm100.app.crm.utils.SharedPrefManager;
import com.ybm100.app.crm.widget.EditTextWithDel;

import java.util.Objects;

import io.reactivex.Observable;
import io.reactivex.disposables.Disposable;

/**
 * @author: zcj
 * @time:2021/3/25. Description:
 */
public class SimulatedLoginActivity extends BaseMVPCompatActivity {


    @NonNull
    @Override
    public BasePresenter initPresenter() {
        return null;
    }

    @Override
    public void showNetError() {

    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_simulated_login;
    }

    @Override
    protected void initView(Bundle savedInstanceState) {
        String sysUserId = SharedPrefManager.getInstance().getUserInfo().getSysUserId();
        EditTextWithDel oaIdView = findViewById(R.id.et_login_pwd);
        EditTextWithDel nameView = findViewById(R.id.et_login_phone);
        TextView login = findViewById(R.id.tv_login_button);
        oaIdView.setText(sysUserId);
        login.setOnClickListener(v -> {
            String name = Objects.requireNonNull(nameView.getText()).toString();
            if (TextUtils.isEmpty(name)) {
                ToastUtils.showLong("请输入账号");
            } else {
                login(name);
            }
        });


    }

    void login(String name) {
        Observable<RequestBaseBean<SimulatedLoginBean>> compose = RetrofitCreateHelper.createApi(ApiService.class).simulatedLogin(name).compose(RxHelper.rxSchedulerHelper());

        Disposable subscribe = compose.subscribe(new SimpleSuccessConsumer<RequestBaseBean<SimulatedLoginBean>>(this) {
            @Override
            public void onSuccess(RequestBaseBean<SimulatedLoginBean> bean) {
                String token = bean.getData().getToken();
                String oaId = bean.getData().getOaId();
                UserInfoBean infoBean = SharedPrefManager.getInstance().getUserInfo();
                infoBean.setToken(token);
                infoBean.setSysUserId(oaId);
                SharedPrefManager.getInstance().setUserInfo(infoBean);
                ToastUtils.showLong("两秒后自动重启app");
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        final Intent intent = getPackageManager().getLaunchIntentForPackage(getPackageName());
                        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                        startActivity(intent);
                        //杀掉以前进程
                        android.os.Process.killProcess(android.os.Process.myPid());
                    }
                }, 2000);

            }
        }, new SimpleErrorConsumer(this) {
            @Override
            public void accept(Throwable throwable) throws Exception {
                super.accept(throwable);

            }
        });
        addDisposable(subscribe);
    }
}
