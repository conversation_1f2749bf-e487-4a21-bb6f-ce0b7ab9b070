package com.ybm100.app.crm.order.adapter

import android.annotation.SuppressLint
import android.graphics.Paint
import android.text.TextUtils
import android.view.MotionEvent
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.xyy.common.util.ToastUtils
import com.xyy.flutter.container.container.ContainerRuntime.router
import com.xyy.utilslibrary.utils.GlideLoadUtils
import com.xyy.utilslibrary.utils.MathUtils
import com.ybm100.app.crm.R
import com.ybm100.app.crm.constant.AppNetConfig
import com.ybm100.app.crm.order.bean.OrderBeanV2
import com.ybm100.app.crm.utils.FormatUtils
import com.ybm100.app.crm.widget.drug.CopyTextDialog

/**
 * 订单列表数据适配器
 */
class OrderListAdapterV2 : BaseQuickAdapter<OrderBeanV2.ListBeanV2, BaseViewHolder>(R.layout.item_order) {

    @SuppressLint("SetTextI18n", "ClickableViewAccessibility")
    override fun convert(helper: BaseViewHolder?, item: OrderBeanV2.ListBeanV2?) {
        // 订单编号
        helper?.setText(R.id.tv_order_ID, item?.orderNo)
        helper?.setText(R.id.tv_order_time, "下单时间：" + (item?.createTime ?: "--"))
        helper?.setText(R.id.tv_order_form, "订单来源：" + FormatUtils.textFormat(item?.branchName))
        // 订单编号点击事件
        helper?.getView<TextView>(R.id.tv_order_ID)?.setOnClickListener {
            val copyTextDialog = CopyTextDialog(mContext)
            copyTextDialog.setText(item?.orderNo)
            copyTextDialog.show()
        }
        // 订单状态
        val textView = helper?.getView<TextView>(R.id.tv_order_status)
        /**
         * 订单状态 0-未审核，1-审核中，2-配送中，3-已完成，4-已取消，5-已删除,6-已拆单,7-出库中,9-审核结束，10-未支付,11-已支付，90-退款审核中,91-已退款,20-已送达,21-已拒签；
         * 状态颜色：
         * 红色：未审核、未支付、已拒签 ，
         * 绿色：审核中、配送中、出库中、退款审核中、已支付、已送达、 已拆单、审核结束，
         * 灰色：已完成、已取消、删除、已退款
         */
        when (item?.status) {
            0, 10, 21 -> textView?.setTextColor(ContextCompat.getColor(mContext, R.color.text_color_FE3D3D))
            1, 2, 7, 90, 11, 20, 6, 9 -> textView?.setTextColor(ContextCompat.getColor(mContext, R.color.text_color_35C561)) // 已完成
            3, 4, 5, 91 -> textView?.setTextColor(ContextCompat.getColor(mContext, R.color.text_color_8E8E93))
            else -> textView?.setTextColor(ContextCompat.getColor(mContext, R.color.text_color_333333))
        }
        helper?.setText(R.id.tv_order_status, item?.statusName)
        // 商品种类数
        helper?.setText(R.id.tv_goodsCount, "${item?.varietyNum}件商品")
        // 药店名称
        helper?.setText(R.id.tv_shopName, item?.merchantName)
        // 商品图
        val ivGoodsCover = helper?.getView<ImageView>(R.id.iv_goodsCover)
        GlideLoadUtils.loadImg(mContext, AppNetConfig.getProductImgMin(item?.imageUrl), ivGoodsCover, R.drawable.icon_load_failed)
        // 优惠列表
        val preferentialList = item?.preferentialList
        val rvLabel = helper?.getView<androidx.recyclerview.widget.RecyclerView>(R.id.rv_label_list)
        rvLabel?.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(mContext, androidx.recyclerview.widget.LinearLayoutManager.VERTICAL, false)
        val orderLabelAdapter = OrderLabelAdapter(preferentialList)
        rvLabel?.adapter = orderLabelAdapter
        rvLabel?.setOnTouchListener { _, event ->
            if (event.action == MotionEvent.ACTION_UP) {
//                OrderDetailActivity.startActivity(mContext, item?.id, item?.merchantId)
                if (TextUtils.isEmpty(item?.id)) {
                    ToastUtils.showShortSafe("订单id为空！")
                } else {
                    router.open(mContext, String.format("/order_detail_page?orderId=%s&merchantId=%s",
                            item?.id, item?.merchantId.toString()), null)
                }
            }
            return@setOnTouchListener false
        }
        // 实付金额
        val money = "¥${MathUtils.getFormatNum(item?.money!!)}"
        var index = money.indexOfLast { it == '.' }
        if (index == -1) {
            index = money.length
        }
        helper?.setText(R.id.tv_realPayAmount, FormatUtils.setTextSize(money, 13, 16, 1, index))
        // 订单金额
        val orderAmount = helper?.getView<TextView>(R.id.tv_orderAmount)
        orderAmount?.paint?.flags = Paint.STRIKE_THRU_TEXT_FLAG or Paint.ANTI_ALIAS_FLAG//中划线并加清晰
        orderAmount?.text = "¥${MathUtils.getFormatNum(item.totalAmount)}"
    }

}