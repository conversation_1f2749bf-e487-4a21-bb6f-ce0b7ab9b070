package com.ybm100.app.crm.contract.lzcustomer;


import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.PrivateListFilterBean;
import com.ybm100.app.crm.bean.lzcustomer.LzPrivateListBean;

import java.util.HashMap;

import io.reactivex.Observable;

/**
 * 私海客户
 */
public interface LzPrivateListContract {

    interface ILzPrivateListModel extends IBaseModel {
        //我的药店列表
        Observable<RequestBaseBean<LzPrivateListBean>> getPrivateListData(HashMap<String, String> map);

        /**
         * 获取筛选条件
         */
        Observable<RequestBaseBean<PrivateListFilterBean>> getFilterItems();
    }

    interface ILzPrivateListView extends IBaseActivity {
        void getListDataSuccess(boolean refresh, RequestBaseBean<LzPrivateListBean> baseBean);

        void getFilterItemsSuccess(RequestBaseBean<PrivateListFilterBean> baseBean);

        void enableLoadMore(boolean b);

        void loadMoreComplete();

        void showEmpty();
    }

}
