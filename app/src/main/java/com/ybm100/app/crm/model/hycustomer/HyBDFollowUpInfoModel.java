package com.ybm100.app.crm.model.hycustomer;


import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.HyApiService;
import com.ybm100.app.crm.bean.hycustomer.HyBDFollowUpInfoBean;
import com.ybm100.app.crm.contract.hycustomer.HyBDFollowUpInfoContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import java.util.HashMap;
import java.util.List;

import io.reactivex.Observable;

/**
 * 荷叶健康bd跟进信息model
 */
public class HyBDFollowUpInfoModel extends BaseModel implements HyBDFollowUpInfoContract.IHyBDFollowUpInfoModel {

    public static HyBDFollowUpInfoModel newInstance() {
        return new HyBDFollowUpInfoModel();
    }

    @Override
    public Observable<RequestBaseBean<List<HyBDFollowUpInfoBean>>> getBDFollowUpInfoList(HashMap<String, String> map) {
        return RetrofitCreateHelper.createApi(HyApiService.class).getBDFollowUpInfoList(map)
                .compose(RxHelper.rxSchedulerHelper());
    }
}