package com.ybm100.app.crm.contract.message;

import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.IBaseRecyclerView;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.message.MessageApiBean;
import com.ybm100.app.crm.bean.message.MessageBean;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/22/2018 11:47
 */
public interface MsgListContract {

    interface IMsgListView extends IBaseRecyclerView<MessageBean> {
        void renderUnreadCount(int unread);

        void updateRead(int position, int type);
    }

    interface IMsgShareModel extends IBaseModel {

        Observable<RequestBaseBean<MessageApiBean>> getMsg(int type, int pageNo, int pageSize);

    }

}
