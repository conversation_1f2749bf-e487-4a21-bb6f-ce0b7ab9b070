package com.ybm100.app.crm.utils.deviceInfo.collector

import android.content.Context
import androidx.core.app.NotificationManagerCompat
import com.xyy.utilslibrary.global.GlobalApplication

class NotificationCollector: BaseCollector() {
    override fun internalCollect(context: Context): String? {
        return "${NotificationManagerCompat.from(GlobalApplication.getContext()).areNotificationsEnabled()}"
    }
}