package com.ybm100.app.crm.flutter.channel

import android.content.Context
import androidx.core.app.NotificationManagerCompat
import androidx.fragment.app.FragmentActivity
import com.xyy.flutter.container.container.bridge.BaseHandler

class CheckNotificationSettingHandler : BaseHandler() {

    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {
        result?.success(isNotificationEnabled(activity))
    }


    /**
     * 判断是否开启通知权限
     *
     * @param context
     * @return
     */
    private fun isNotificationEnabled(context: Context?): Bo<PERSON>an {
        var isEnabled = true
        if (context != null) {
            isEnabled = NotificationManagerCompat.from(context).areNotificationsEnabled()
        }
        return isEnabled
    }


}
