package com.ybm100.app.crm.contract.lzcustomer;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.lzcustomer.LzPublicListBean;

import java.util.HashMap;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/29/2018 16:48
 * 未认领药店
 */
public interface LzPublicListContract {

    interface ILzPublicListModel extends IBaseModel {
        //公海列表
        Observable<RequestBaseBean<LzPublicListBean>> searchOpenSea(HashMap<String, String> map);

        //认领药店
        Observable<RequestBaseBean> receive(String id);
    }

    interface ILzPublicListView extends IBaseActivity {
        void searchOpenSeaSuccess(boolean refresh, RequestBaseBean<LzPublicListBean> baseBean);

        void receiveSuccess(RequestBaseBean baseBean);

        void enableLoadMore(boolean b);

        void loadMoreComplete();

        void showEmpty();
    }

}
