package com.ybm100.app.crm.presenter.contact;

import com.xyy.utilslibrary.base.BasePresenter;
import com.ybm100.app.crm.contract.contact.ContactContract;
import com.ybm100.app.crm.model.contact.ContactModel;

/**
 * Created by XyyMvpSportTemplate on 12/24/2018 10:04
 */
public class ContactPresenter extends BasePresenter<ContactContract.IContactModel, ContactContract.IContactView> {

    public static ContactPresenter newInstance() {
        return new ContactPresenter();
    }

    @Override
    protected ContactModel getModel() {
        return ContactModel.newInstance();
    }

}
