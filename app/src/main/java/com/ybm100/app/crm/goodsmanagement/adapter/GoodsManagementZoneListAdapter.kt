package com.ybm100.app.crm.goodsmanagement.adapter

import android.view.View
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.xyy.common.widget.RoundTextView
import com.ybm100.app.crm.R
import com.ybm100.app.crm.goodsmanagement.bean.GoodsManagementListBean


class GoodsManagementZoneListAdapter(data: List<String?>) : BaseQuickAdapter<String?, BaseViewHolder>(R.layout.layout_goods_zone_item, data) {

    override fun convert(helper: BaseViewHolder, item: String?) {
        val view = helper.getView<TextView>(R.id.tv_content)
        if (item.isNullOrEmpty()) {
            view.visibility = View.GONE
        } else {
            view.visibility = View.VISIBLE
            view.text = item
        }
    }

}