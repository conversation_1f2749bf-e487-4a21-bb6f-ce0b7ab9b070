package com.ybm100.app.crm.constant;

public class RxBusCode {

    /**
     * 通知tab显示小红点
     */
    public final static int RX_BUS_SHOW_RED_POINT = 10001;
    /**
     * 通知首页执行人更新
     */
    public final static int RX_BUS_UPDATE_EXECUTOR = 10002;
    /**
     * 通知已经选择了对象(药店)
     */
    public final static int RX_BUS_SELECTED_TARGET = 10003;
    /**
     * 通知已经选择了对象(药店)(从新建线索中返回的)
     */
    public final static int RX_BUS_SELECTED_TARGET_BY_CLUE = 10004;
    /**
     * 新建日程后更新日程
     */
    public final static int RX_BUS_UPDATE_NEW_SCHEDULE = 10005;
    /**
     * 筛选条件更改后更新日程列表
     */
    public final static int RX_BUS_UPDATE_SCHEDULE_LIST = 10006;
    /**
     * 筛选条件更改后关闭页面，回传条件
     */
    public final static int RX_BUS_COMES_BACK_SCHEDULE = 10007;
    /**
     * 选择人物关联
     */
    public final static int RX_BUS_SELECT_TASK = 10008;

    /**
     * 编辑界面 备注返回
     */
    public final static int RX_BUS_EDIT_REMARK = 10009;
    /**
     * 编辑界面 缺失品种返回
     */
    public final static int RX_BUS_EDIT_MISSING = 10010;
    /**
     * 编辑界面 核心供应商返回
     */
    public final static int RX_BUS_EDIT_CORE = 10011;
    /**
     * 编辑界面 客户需求返回
     */
    public final static int RX_BUS_EDIT_DEMAND = 10012;
    /**
     * 线索更新
     */
    public final static int RX_BUS_UPDATE_CLUE_LIST = 10013;
    /**
     * 药店更新
     */
    public final static int RX_BUS_UPDATE_PRIVATE_LIST = 10014;
    /**
     * 打开抽屉
     */
    public final static int RX_BUS_OPEN_DRAWER = 10015;

    /**
     * 新建日程
     **/
    public final static int RX_BUS_CREATE_SCHEDULE = 10016;

    /**
     * 用于接收 排名 时间 字段
     */
    public final static int RX_BUS_RANK_TIME = 10017;
    /**
     * 业绩筛选
     */
    public final static int RX_BUS_ACHIEVEMENT = 10018;
    /**
     * 点击通知栏，进入客户列表，并传递资质筛选条件
     */
    public final static int RX_BUS_ENTER_CLIENT_WITH_CONDITIONS = 10019;
    /**
     * 通讯录更新
     */
    public final static int RX_BUS_UPDATE_CONTACT_BOOK = 10020;
    /**
     * 资质更新
     */
    public final static int RX_BUS_UPDATE_LICENCEDTAIL = 10021;

    /**
     * 新建联系人成功后刷新 选择联系人列表
     */
    public final static int RX_BUS_UPDATE_SELECT_CONTACT_LIST = 10022;
    /**
     * 提交发票申请后刷新列表
     **/
    public final static int RX_BUS_UPDATE_INVOICE_APPLY_LIST = 10023;

    /**
     * 资质变更完成
     */
    public final static int RX_BUS_UPDATE_LICENCEDTAIL_CHANGE = 10024;
    /**
     * 刷新历史记录
     */
    public final static int RX_BUS_REFRESH_HISTORY = 10025;
    /**
     * 对象经营状况
     */
    public final static int RX_BUS_CREATESCHEDULETARGETCONDITION = 10026;
    /**
     * 修改地图
     */
    public final static int RX_BUS_CHANGE_MAP = 10027;

    /**
     * 筛选条件更改后更新精准推荐列表
     */
    public final static int RX_BUS_UPDATE_SHARE_GOODS_LIST = 10028;

    /**
     * 区域选择
     */
    public final static int RX_BUS_SELECT_AREA = 10029;
    /**
     * 分配
     */
    public final static int RX_BUS_DISTRIBUTE = 10030;
    /**
     * 区域选择重置
     */
    public final static int RX_BUS_SELECT_AREA_reset = 10031;
    /**
     * 通知私海客户区域选择
     */
    public final static int RX_BUS_UPDATE_EXECUTOR_SINGLE = 10032;
    /**
     * 陪访人选择
     */
    public final static int RX_BUS_ACCOMPANYING = 10033;
    /**
     * 灵芝问诊 药店释放后刷新 私海列表
     */
    public final static int RX_BUS_RELEASE_UPDATE_PRIVATE_LIST = 10034;
    /**
     * 任务商品搜索 更新列表和购物车
     */
    public final static int RX_BUS_UPDATE_GOODS_LIST_AND_CART = 10035;
    public final static int RX_BUS_UPDATE_CUSTOMER_LIST_AND_CART = 10036;
    public final static int RX_BUS_UPDATE_TEAM_TASK = 10037;

    /**
     * 商品管理
     */
    public final static int RX_BUS_GOODS_MANAGEMENT_UPDATE_GOODS_LIST_AND_CART = 10038;

    public final static int RX_BUS_ENTER_CLIENT_WITH_CONDITIONS_NEXT = 10100;

    /**
     * 自定义标签
     */
    public final static int RX_BUS_TAG_ADD = 10060;
    /**
     * 自定义卖点
     */
    public final static int RX_BUS_SELL_POINT_ADD = 10061;
    /**
     * 进入客户列表
     */
    public final static int RX_BUS_ENTER_CLIENT = 10062;

    /**
     * 进入消息tab中的会话消息
     */
    public final static int RX_BUS_MESSAGE_SELECT = 10070;

    /**
     * 刷新消息红点
     */
    public final static int RX_BUS_REFRESH_MESSAG = 10071;

    /**
     * 药店更新
     */
    public final static int RX_BUS_REMOVE_PUBLIC_LIST_ITEM = 10072;
    /**
     * hy分配
     */
    public final static int RX_BUS_HY_DISTRIBUTE = 10080;
    /**
     * hy通知私海客户区域选择
     */
    public final static int RX_BUS_HY_UPDATE_EXECUTOR_SINGLE = 10081;
}
