package com.ybm100.app.crm.model.lzcustomer;

import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.LZApiService;
import com.ybm100.app.crm.bean.lzcustomer.LzPublicListBean;
import com.ybm100.app.crm.contract.lzcustomer.LzPublicListContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import java.util.HashMap;

import io.reactivex.Observable;


public class LzPublicListModel implements LzPublicListContract.ILzPublicListModel {

    public static LzPublicListModel newInstance() {
        return new LzPublicListModel();
    }


    @Override
    public Observable<RequestBaseBean<LzPublicListBean>> searchOpenSea(HashMap<String, String> map) {
        return RetrofitCreateHelper.createApi(LZApiService.class).getOpenSeaList(map).compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean> receive(String id) {
        return RetrofitCreateHelper.createApi(LZApiService.class).receiveCustomer(id).compose(RxHelper.rxSchedulerHelper());
    }
}