package com.ybm100.app.crm.goodsmanagement.activity

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.Fragment
import com.xyy.common.util.FragmentUtils
import com.xyy.utilslibrary.base.fragment.BaseCompatFragment
import com.ybm100.app.crm.R
import com.ybm100.app.crm.constant.Constants
import com.ybm100.app.crm.constant.Constants.GoodsManagement.ACTIVITY_REQUEST_CODE_SELECT_CUSTOMER
import com.ybm100.app.crm.goodsmanagement.bean.GoodsManagementAreaListBean
import com.ybm100.app.crm.goodsmanagement.bean.GoodsManagementListBean
import com.ybm100.app.crm.goodsmanagement.bean.Zone
import com.ybm100.app.crm.goodsmanagement.fragment.CustomerGoodsManagementFilterFragment
import com.ybm100.app.crm.goodsmanagement.fragment.GoodsManagementFilterFragment
import com.ybm100.app.crm.goodsmanagement.fragment.GoodsManagementFragment

/**
 * 商品管理 - 选择商品
 */
class GoodsManagementSelectGoodsActivity : BaseDrawerActivity(),
    GoodsManagementFilterFragment.DrawerListener,
    CustomerGoodsManagementFilterFragment.CustomerDrawerListener {
    private lateinit var mFragment: BaseCompatFragment
    private lateinit var mCustomerDrawerFragment: CustomerGoodsManagementFilterFragment
    private var mFragmentType = -1
    private var mAreaCode = ""
    private var mMerchantID = ""

    override fun initTransferData() {
        super.initTransferData()
        intent?.extras?.run {
            mFragmentType = getInt(Constants.GoodsManagement.ARG_FRAGMENT_TYPE, -1)
            mAreaCode = getString(Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_AREA_CODE, "")
            mMerchantID = getString(Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_MERCHANT_ID, "")
        }
    }

    override fun getContentMainLayoutID(): Int {
        return R.layout.activity_fragment_container
    }

    override fun initContentMain() {
        mFragment = GoodsManagementFragment.newInstance(intent?.extras)
        FragmentUtils.replaceFragment(
            supportFragmentManager,
            mFragment,
            R.id.fragment_container,
            false
        )
    }

    override fun getDrawerFragments(): List<Fragment> {
        return when (intent?.extras?.getInt(Constants.GoodsManagement.ARG_FRAGMENT_TYPE, -1)) {
            Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION_RECOMMENDATION -> {
                listOf<Fragment>(
                    GoodsManagementFilterFragment.newInstance(mFragmentType, mAreaCode).apply {
                        setDrawerListener(this@GoodsManagementSelectGoodsActivity)
                    })
            }
            Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_RECOMMENDATION -> {
                mCustomerDrawerFragment =
                    CustomerGoodsManagementFilterFragment.newInstance(mMerchantID).apply {
                        setCustomerDrawerListener(this@GoodsManagementSelectGoodsActivity)
                    }
                listOf<Fragment>(mCustomerDrawerFragment)
            }
            else -> {
                listOf<Fragment>()
            }
        }

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK && requestCode == ACTIVITY_REQUEST_CODE_SELECT_CUSTOMER) {
            finish()
        }
    }


    override fun onConfirmPressed(
        itemArea: GoodsManagementAreaListBean.Row?,
        pos: Int,
        selectedZone: Zone?
    ) {
        closeDrawer()
        (mFragment as GoodsManagementFragment).onDrawerConfirmPressed(itemArea, selectedZone)
    }

    fun syncCustomerDrawerFilterData(YBMFilterPos: Int) {
        if (::mCustomerDrawerFragment.isInitialized) {
            (mCustomerDrawerFragment).syncCustomerDrawerFilterData(YBMFilterPos)
        }
    }

    override fun onCustomerConfirmPressed(
        YBMFilterPos: Int,
        collectionStatusFilterPos: Int,
        selectedZone: Zone?
    ) {
        closeDrawer()
        (mFragment as GoodsManagementFragment).onCustomerDrawerConfirmPressed(
            YBMFilterPos,
            collectionStatusFilterPos,
            selectedZone
        )
    }

    companion object {
        val sGoodsCartList: MutableList<GoodsManagementListBean.Row> = mutableListOf()

        @JvmStatic
        fun startActivity(activity: Activity?, bundle: Bundle?) {

            sGoodsCartList.clear()

            val intent = Intent(activity, GoodsManagementSelectGoodsActivity::class.java).apply {
                putExtras(bundle)
            }

            activity?.startActivity(intent)
        }
    }
}
