package com.ybm100.app.crm.model.drugstore.minedrug;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiDrugstoreInfo;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.DrugstoreVisitBean;
import com.ybm100.app.crm.contract.drugstore.minedrug.VisitRecordContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:10
 * <AUTHOR>
 */
public class VisitRecordModel extends BaseModel implements VisitRecordContract.IVisitRecordModel {

    public static VisitRecordModel newInstance() {
        return new VisitRecordModel();
    }

    @Override
    public Observable<RequestBaseBean<DrugstoreVisitBean>> followInRecords(int limit, String merchantId, int offset) {
        return RetrofitCreateHelper.createApi(ApiDrugstoreInfo.class).followInRecords(limit, merchantId, offset)
                .compose(RxHelper.rxSchedulerHelper());
    }
}