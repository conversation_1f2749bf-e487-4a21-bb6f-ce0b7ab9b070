package com.ybm100.app.crm.goodsmanagement.adapter

import androidx.core.content.ContextCompat
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.ybm100.app.crm.R
import com.ybm100.app.crm.goodsmanagement.bean.GoodsManagementAreaListBean


class GoodsManagementFilterAdapter : BaseQuickAdapter<GoodsManagementAreaListBean.Row, BaseViewHolder>(R.layout.item_goods_management_filter) {

    override fun convert(helper: BaseViewHolder, item: GoodsManagementAreaListBean.Row?) {
        helper.setText(R.id.rtv_content, item?.orgName ?: "")
        if (item?.isSelected == true) {
            helper.setTextColor(R.id.rtv_content, ContextCompat.getColor(mContext, R.color.color_00B377))
                    .setBackgroundColor(R.id.rtv_content, ContextCompat.getColor(mContext, R.color.color_E5F7F1))
        } else {
            helper.setTextColor(R.id.rtv_content, ContextCompat.getColor(mContext, R.color.color_676773))
                    .setBackgroundColor(R.id.rtv_content, ContextCompat.getColor(mContext, R.color.color_F7F7F8))
        }
    }

}