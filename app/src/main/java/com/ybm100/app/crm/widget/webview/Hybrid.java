package com.ybm100.app.crm.widget.webview;

import android.content.Context;
import android.graphics.Color;
import android.os.Looper;
import android.text.ClipboardManager;
import android.text.TextUtils;
import android.webkit.JavascriptInterface;

import com.xyy.common.util.Abase;
import com.xyy.common.util.ToastUtils;
import com.xyy.utilslibrary.utils.DisplayUtils;
import com.xyy.utilslibrary.utils.LogUtils;
import com.ybm100.app.crm.R;
import com.ybm100.app.crm.utils.router.RoutersUtils;
import com.ybm100.app.crm.utils.SharedPrefManager;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.functions.Consumer;


/**
 * webveiw 注入对象，给h5接入功能
 */
public class Hybrid {
    private final YbmWebView wbH5;
    float density = 2;

    public Hybrid(YbmWebView webView) {
        wbH5 = webView;
        density = wbH5.getContext().getResources().getDisplayMetrics().density;
    }

    @JavascriptInterface
    public void setStatusColor(String color) {
        LogUtils.d("setStatusColor:" + color);
        if (wbH5 != null && wbH5.getWebViewListener() != null) {
            wbH5.getWebViewListener().setStatusColor(wbH5, getStrColor(color));
        }
    }

    @JavascriptInterface
    public boolean handlerAction(String action) {
        LogUtils.d("handlerAction:" + action);
        if (wbH5 != null && !TextUtils.isEmpty(action)) {
            return RoutersUtils.open(action);
        }
        return false;
    }


    @JavascriptInterface
    public String getClipboard() {
        LogUtils.d("getClipboard:");
        if (wbH5 != null) {
            ClipboardManager clipboardManager = (ClipboardManager) Abase.getContext().getSystemService(Context.CLIPBOARD_SERVICE);
            return clipboardManager.getText().toString();
        }
        return "";
    }

    @JavascriptInterface
    public boolean setClipboard(String text) {
        LogUtils.d("setClipboard:" + text);
        if (wbH5 != null && text != null) {
            ClipboardManager clipboardManager = (ClipboardManager) Abase.getContext().getSystemService(Context.CLIPBOARD_SERVICE);
            clipboardManager.setText(text);
            return true;
        }
        return false;
    }


    @JavascriptInterface
    public String getMerchantId() {
//        LogUtils.d("getMerchantId:"+ HttpManager.getInstance().getMerchant_id());
        if (wbH5 != null) {
            return SharedPrefManager.getInstance().getUserInfo().getSysUserId();
        } else {
            return "";
        }
    }

    @JavascriptInterface
    public int getStatusHeigth() {
        try {
            return (int) wbH5.getContext().getResources().getDimension(R.dimen.status_height);
        } catch (Throwable e) {
            return DisplayUtils.dp2px(22);
        }
    }

    @JavascriptInterface
    public void showToast(String str) {
        ToastUtils.showShort(str);
    }


    @JavascriptInterface
    public void onScroll(final int y) {
        if (wbH5 != null && wbH5.getWebViewListener() != null) {
            if (Looper.myLooper() == Looper.getMainLooper()) {
                wbH5.getWebViewListener().onScrollChanged(0, (int) (y * density), 0, 0);
            } else {
                Disposable subscribe = Observable.just(y)
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(new Consumer<Integer>() {
                            @Override
                            public void accept(Integer y) throws Exception {
                                wbH5.getWebViewListener().onScrollChanged(0, (int) (y * density), 0, 0);
                            }
                        });
            }
        }
    }

    protected int getStrColor(String color) {
        try {
            return Color.parseColor(color);
        } catch (Exception e) {
            return 0;
        }
    }
}
