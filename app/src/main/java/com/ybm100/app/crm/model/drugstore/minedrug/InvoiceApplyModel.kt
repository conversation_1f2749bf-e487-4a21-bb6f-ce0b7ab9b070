package com.ybm100.app.crm.model.drugstore.minedrug

import com.xyy.utilslibrary.base.BaseModel
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.helper.RxHelper
import com.ybm100.app.crm.api.ApiDrugstoreInfo
import com.ybm100.app.crm.api.ApiService
import com.ybm100.app.crm.bean.drugstore.minedrugstore.ApplyInitBean
import com.ybm100.app.crm.contract.drugstore.minedrug.InvoiceApplyContract
import com.ybm100.app.crm.net.RetrofitCreateHelper
import com.ybm100.app.crm.order.bean.AptitudeInitBean
import io.reactivex.Observable
import java.util.*
import kotlin.collections.HashMap

/**
 * Created by XyyMvpYkqTemplate on 07/30/2019 18:32
 */
class InvoiceApplyModel : BaseModel(), InvoiceApplyContract.IInvoiceApplyModel {
    override fun initLicenseAuditDetail(merchantId: String, from: String): Observable<RequestBaseBean<AptitudeInitBean>> {
        val map: HashMap<String, String> = HashMap()
        map["merchantId"] = merchantId
        map["from"] = from
        return RetrofitCreateHelper.createApi(ApiDrugstoreInfo::class.java).initLicenseAuditDetail(map)
                .compose(RxHelper.rxSchedulerHelper())
    }

    override fun reqInvoiceInit(map: HashMap<String, Any>): Observable<RequestBaseBean<ApplyInitBean>> {
        return RetrofitCreateHelper.createApi(ApiService::class.java).getInvoiceApplyInit(map)
                .compose(RxHelper.rxSchedulerHelper<RequestBaseBean<ApplyInitBean>>())
    }

    override fun submitInvoiceApply(map: HashMap<String, Any>): Observable<RequestBaseBean<*>> {
        return RetrofitCreateHelper.createApi(ApiService::class.java).getInvoiceApply(map)
                .compose(RxHelper.rxSchedulerHelper<RequestBaseBean<*>>())
    }

}
