package com.ybm100.app.crm.presenter.lzcustomer;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.lzcustomer.LzPrivateDetailBean;
import com.ybm100.app.crm.contract.lzcustomer.LzPrivateDetailContract;
import com.ybm100.app.crm.model.lzcustomer.LzPrivateDetailModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

import io.reactivex.disposables.Disposable;

/**
 * <AUTHOR>
 * @date 2019/1/7
 */
public class LzPrivateDetailPresenter extends BasePresenter<LzPrivateDetailContract.ILzPrivateDetailModel, LzPrivateDetailContract.ILzPrivateDetailView> {
    public static LzPrivateDetailPresenter newInstance() {
        return new LzPrivateDetailPresenter();
    }

    @Override
    protected LzPrivateDetailContract.ILzPrivateDetailModel getModel() {
        return LzPrivateDetailModel.newInstance();
    }

    /**
     * 公海详情
     *
     * @param id 客户ID
     */
    public void privateSeaCustomerDetail(String id) {
        if (mIView == null || mIModel == null) return;
        Disposable subscribe = mIModel.privateSeaCustomerDetail(id)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean<LzPrivateDetailBean>>(mIView, "") {
                    @Override
                    public void onSuccess(RequestBaseBean<LzPrivateDetailBean> baseBean) {
                        if (mIView == null) return;
                        mIView.privateSeaDetailSuccess(baseBean.getData());
                    }
                }, new SimpleErrorConsumer(mIView));
        mRxManager.register(subscribe);
    }

    /**
     * 释放
     *
     * @param id 客户ID
     */
    public void releaseCustomer(String id) {
        if (mIView == null || mIModel == null) return;
        Disposable subscribe = mIModel.releaseCustomer(id)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean>(mIView, "") {
                    @Override
                    public void accept(RequestBaseBean baseBean) throws Exception {
                        if (mIView == null) return;
                        mIView.hideWaitDialog();
                        if (baseBean.isSuccess()) {
                            mIView.releaseCustomerSuccess(baseBean);
                        } else if (!baseBean.isSuccess() && baseBean.getCode() == 405) {
                            mIView.releaseCustomerSuccess(baseBean);
                        } else {
                            mIView.showToast(baseBean.getErrorMsg());
                        }
                    }

                    @Override
                    public void onSuccess(RequestBaseBean baseBean) {

                    }
                }, new SimpleErrorConsumer(mIView));
        mRxManager.register(subscribe);
    }

}
