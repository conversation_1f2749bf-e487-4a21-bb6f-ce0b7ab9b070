package com.ybm100.app.crm.utils.deviceInfo.collector

import android.content.Context
import android.content.Context.SENSOR_SERVICE
import android.content.pm.PackageManager
import android.hardware.Sensor
import android.hardware.SensorManager
import android.text.TextUtils
import com.ybm100.app.crm.utils.deviceInfo.util.CheckResult
import com.ybm100.app.crm.utils.deviceInfo.util.CheckResult.Companion.RESULT_EMULATOR
import com.ybm100.app.crm.utils.deviceInfo.util.CheckResult.Companion.RESULT_MAYBE_EMULATOR
import com.ybm100.app.crm.utils.deviceInfo.util.CheckResult.Companion.RESULT_UNKNOWN
import com.ybm100.app.crm.utils.deviceInfo.util.CommandUtil
import java.util.*


class EmulatorCollector : BaseCollector() {
    override fun internalCollect(context: Context): String? {
        return checkEmulator(context)
    }

    fun isEmulator(context: Context): Boolean {

        var suspectCount = 0

        //检测硬件名称
        val hardwareResult: CheckResult = checkFeaturesByHardware()
        when (hardwareResult.result) {
            RESULT_MAYBE_EMULATOR -> ++suspectCount
            RESULT_EMULATOR -> {
                return true
            }
        }

        //检测渠道
        val flavorResult: CheckResult = checkFeaturesByFlavor()
        when (flavorResult.result) {
            RESULT_MAYBE_EMULATOR -> ++suspectCount
            RESULT_EMULATOR -> {
                return true
            }
        }

        //检测设备型号
        val modelResult: CheckResult = checkFeaturesByModel()
        when (modelResult.result) {
            RESULT_MAYBE_EMULATOR -> ++suspectCount
            RESULT_EMULATOR -> {
                return true
            }
        }

        //检测硬件制造商
        val manufacturerResult: CheckResult = checkFeaturesByManufacturer()
        when (manufacturerResult.result) {
            RESULT_MAYBE_EMULATOR -> ++suspectCount
            RESULT_EMULATOR -> {
                return true
            }
        }

        //检测主板名称
        val boardResult: CheckResult = checkFeaturesByBoard()
        when (boardResult.result) {
            RESULT_MAYBE_EMULATOR -> ++suspectCount
            RESULT_EMULATOR -> {
                return true
            }
        }

        //检测主板平台
        val platformResult: CheckResult = checkFeaturesByPlatform()
        when (platformResult.result) {
            RESULT_MAYBE_EMULATOR -> ++suspectCount
            RESULT_EMULATOR -> {
                return true
            }
        }

        //检测基带信息
        val baseBandResult: CheckResult = checkFeaturesByBaseBand()
        when (baseBandResult.result) {
            RESULT_MAYBE_EMULATOR -> suspectCount += 2 //模拟器基带信息为null的情况概率相当大
            RESULT_EMULATOR -> {
                return true
            }
        }

        //检测传感器数量
        val sensorNumber: Int = getSensorNumber(context)
        if (sensorNumber <= 7) ++suspectCount

        //检测已安装第三方应用数量
        val userAppNumber: Int = getUserAppNumber()
        if (userAppNumber <= 5) ++suspectCount

        //检测是否支持闪光灯
        val supportCameraFlash: Boolean = supportCameraFlash(context)
        if (!supportCameraFlash) ++suspectCount

        //检测是否支持相机
        val supportCamera: Boolean = supportCamera(context)
        if (!supportCamera) ++suspectCount

        //检测是否支持蓝牙
        val supportBluetooth: Boolean = supportBluetooth(context)
        if (!supportBluetooth) ++suspectCount

        //检测光线传感器
        val hasLightSensor: Boolean = hasLightSensor(context)
        if (!hasLightSensor) ++suspectCount

        //检测进程组信息
        val cgroupResult: CheckResult = checkFeaturesByCgroup()
        if (cgroupResult.result == RESULT_MAYBE_EMULATOR) ++suspectCount


        // 怀疑值大于7，则认为是模拟器，视情况调节
        return suspectCount > 7

    }

    private fun checkEmulator(context: Context): String {

        var suspectCount = 0

        //检测硬件名称
        val hardwareResult: CheckResult = checkFeaturesByHardware()
        when (hardwareResult.result) {
            RESULT_MAYBE_EMULATOR -> ++suspectCount
            RESULT_EMULATOR -> {
                return "hardware:${hardwareResult.value}"
            }
        }

        //检测渠道
        val flavorResult: CheckResult = checkFeaturesByFlavor()
        when (flavorResult.result) {
            RESULT_MAYBE_EMULATOR -> ++suspectCount
            RESULT_EMULATOR -> {
                return "flavor:${flavorResult.value}"
            }
        }

        //检测设备型号
        val modelResult: CheckResult = checkFeaturesByModel()
        when (modelResult.result) {
            RESULT_MAYBE_EMULATOR -> ++suspectCount
            RESULT_EMULATOR -> {
                return "model:${modelResult.value}"
            }
        }

        //检测硬件制造商
        val manufacturerResult: CheckResult = checkFeaturesByManufacturer()
        when (manufacturerResult.result) {
            RESULT_MAYBE_EMULATOR -> ++suspectCount
            RESULT_EMULATOR -> {
                return "manufacturer:${manufacturerResult.value}"
            }
        }

        //检测主板名称
        val boardResult: CheckResult = checkFeaturesByBoard()
        when (boardResult.result) {
            RESULT_MAYBE_EMULATOR -> ++suspectCount
            RESULT_EMULATOR -> {
                return "board:${boardResult.value}"
            }
        }

        //检测主板平台
        val platformResult: CheckResult = checkFeaturesByPlatform()
        when (platformResult.result) {
            RESULT_MAYBE_EMULATOR -> ++suspectCount
            RESULT_EMULATOR -> {
                return "platform:${platformResult.value}"
            }
        }

        //检测基带信息
        val baseBandResult: CheckResult = checkFeaturesByBaseBand()
        when (baseBandResult.result) {
            RESULT_MAYBE_EMULATOR -> suspectCount += 2 //模拟器基带信息为null的情况概率相当大
            RESULT_EMULATOR -> {
                return "baseBand:" + baseBandResult.value
            }
        }

        //检测传感器数量
        val sensorNumber: Int = getSensorNumber(context)
        if (sensorNumber <= 7) ++suspectCount

        //检测已安装第三方应用数量
        val userAppNumber: Int = getUserAppNumber()
        if (userAppNumber <= 5) ++suspectCount

        //检测是否支持闪光灯
        val supportCameraFlash: Boolean = supportCameraFlash(context)
        if (!supportCameraFlash) ++suspectCount

        //检测是否支持相机
        val supportCamera: Boolean = supportCamera(context)
        if (!supportCamera) ++suspectCount

        //检测是否支持蓝牙
        val supportBluetooth: Boolean = supportBluetooth(context)
        if (!supportBluetooth) ++suspectCount

        //检测光线传感器
        val hasLightSensor: Boolean = hasLightSensor(context)
        if (!hasLightSensor) ++suspectCount

        //检测进程组信息
        val cgroupResult: CheckResult = checkFeaturesByCgroup()
        if (cgroupResult.result == RESULT_MAYBE_EMULATOR) ++suspectCount

        return StringBuffer()
                .append(hardwareResult.value)
                .append(",").append(flavorResult.value)
                .append(",").append(modelResult.value)
                .append(",").append(manufacturerResult.value)
                .append(",").append(boardResult.value)
                .append(",").append(platformResult.value)
                .append(",").append(baseBandResult.value)
                .append(",").append(sensorNumber)
                .append(",").append(userAppNumber)
                .append(",").append(if (supportCamera) 1 else 0)
                .append(",").append(if (supportCameraFlash) 1 else 0)
                .append(",").append(if (supportBluetooth) 1 else 0)
                .append(",").append(if (hasLightSensor) 1 else 0)
                .append(",").append(suspectCount).toString()

    }


    /**
     * 特征参数-硬件名称
     *
     * @return 0表示可能是模拟器，1表示模拟器，2表示可能是真机
     */
    private fun checkFeaturesByHardware(): CheckResult {
        val hardware: String = getProperty("ro.hardware")
                ?: return CheckResult(RESULT_MAYBE_EMULATOR, null)
        val result: Int
        result = when (hardware.toLowerCase(Locale.getDefault())) {
            "ttvm", "nox", "cancro", "intel", "vbox", "vbox86", "android_x86" -> RESULT_EMULATOR
            else -> RESULT_UNKNOWN
        }
        return CheckResult(result, hardware)
    }

    /**
     * 特征参数-渠道
     *
     * @return 0表示可能是模拟器，1表示模拟器，2表示可能是真机
     */
    private fun checkFeaturesByFlavor(): CheckResult {
        val flavor = getProperty("ro.build.flavor")
                ?: return CheckResult(RESULT_MAYBE_EMULATOR, null)
        val result: Int
        val tempValue = flavor.toLowerCase(Locale.getDefault())
        result = when {
            tempValue.contains("vbox") -> RESULT_EMULATOR
            tempValue.contains("sdk_gphone") -> RESULT_EMULATOR
            else -> RESULT_UNKNOWN
        }
        return CheckResult(result, flavor)
    }

    /**
     * 特征参数-设备型号
     *
     * @return 0表示可能是模拟器，1表示模拟器，2表示可能是真机
     */
    private fun checkFeaturesByModel(): CheckResult {
        val model = getProperty("ro.product.model")
                ?: return CheckResult(RESULT_MAYBE_EMULATOR, null)
        val result: Int
        val tempValue = model.toLowerCase(Locale.getDefault())
        result = when {
            tempValue.contains("google_sdk") -> RESULT_EMULATOR
            tempValue.contains("emulator") -> RESULT_EMULATOR
            tempValue.contains("android sdk built for x86") -> RESULT_EMULATOR
            else -> RESULT_UNKNOWN
        }
        return CheckResult(result, model)
    }

    /**
     * 特征参数-硬件制造商
     *
     * @return 0表示可能是模拟器，1表示模拟器，2表示可能是真机
     */
    private fun checkFeaturesByManufacturer(): CheckResult {
        val manufacturer = getProperty("ro.product.manufacturer")
                ?: return CheckResult(RESULT_MAYBE_EMULATOR, null)
        val result: Int
        val tempValue = manufacturer.toLowerCase(Locale.getDefault())
        result = when {
            tempValue.contains("genymotion") -> RESULT_EMULATOR
            tempValue.contains("netease") -> RESULT_EMULATOR //网易MUMU模拟器
            else -> RESULT_UNKNOWN
        }
        return CheckResult(result, manufacturer)
    }

    /**
     * 特征参数-主板名称
     *
     * @return 0表示可能是模拟器，1表示模拟器，2表示可能是真机
     */
    private fun checkFeaturesByBoard(): CheckResult {
        val board = getProperty("ro.product.board")
                ?: return CheckResult(RESULT_MAYBE_EMULATOR, null)
        val result: Int
        val tempValue = board.toLowerCase(Locale.getDefault())
        result = when {
            tempValue.contains("android") -> RESULT_EMULATOR
            tempValue.contains("goldfish") -> RESULT_EMULATOR
            else -> RESULT_UNKNOWN
        }
        return CheckResult(result, board)
    }

    /**
     * 特征参数-主板平台
     *
     * @return 0表示可能是模拟器，1表示模拟器，2表示可能是真机
     */
    private fun checkFeaturesByPlatform(): CheckResult {
        val platform = getProperty("ro.board.platform")
                ?: return CheckResult(RESULT_MAYBE_EMULATOR, null)
        val result: Int
        val tempValue = platform.toLowerCase(Locale.getDefault())
        result = when {
            tempValue.contains("android") -> RESULT_EMULATOR
            else -> RESULT_UNKNOWN
        }
        return CheckResult(result, platform)
    }

    /**
     * 特征参数-基带信息
     *
     * @return 0表示可能是模拟器，1表示模拟器，2表示可能是真机
     */
    private fun checkFeaturesByBaseBand(): CheckResult {
        val baseBandVersion = getProperty("gsm.version.baseband")
                ?: return CheckResult(RESULT_MAYBE_EMULATOR, null)
        val result: Int
        result = when {
            baseBandVersion.contains("1.0.0.0") -> RESULT_EMULATOR
            else -> RESULT_UNKNOWN
        }
        return CheckResult(result, baseBandVersion)
    }

    /**
     * 获取传感器数量
     */
    private fun getSensorNumber(context: Context): Int {
        val sm = context.getSystemService(SENSOR_SERVICE) as SensorManager
        return sm.getSensorList(Sensor.TYPE_ALL).size
    }

    /**
     * 获取已安装第三方应用数量
     */
    private fun getUserAppNumber(): Int {
        val userApps = CommandUtil.exec("pm list package -3")
        return userApps?.split("package:")?.toTypedArray()?.size ?: 0
    }

    /**
     * 是否支持相机
     */
    private fun supportCamera(context: Context): Boolean {
        return context.packageManager.hasSystemFeature(PackageManager.FEATURE_CAMERA_ANY)
    }

    /**
     * 是否支持闪光灯
     */
    private fun supportCameraFlash(context: Context): Boolean {
        return context.packageManager.hasSystemFeature(PackageManager.FEATURE_CAMERA_FLASH)
    }

    /**
     * 是否支持蓝牙
     */
    private fun supportBluetooth(context: Context): Boolean {
        return context.packageManager.hasSystemFeature(PackageManager.FEATURE_BLUETOOTH)
    }

    /**
     * 判断是否存在光传感器来判断是否为模拟器
     * 部分真机也不存在温度和压力传感器。其余传感器模拟器也存在。
     *
     * @return false为模拟器
     */
    private fun hasLightSensor(context: Context): Boolean {
        val sensorManager = context.getSystemService(SENSOR_SERVICE) as SensorManager
        val sensor = sensorManager.getDefaultSensor(Sensor.TYPE_LIGHT) //光线传感器
        return null != sensor
    }

    /**
     * 特征参数-进程组信息
     */
    private fun checkFeaturesByCgroup(): CheckResult {
        val filter: String = CommandUtil.exec("cat /proc/self/cgroup")
                ?: return CheckResult(RESULT_MAYBE_EMULATOR, null)
        return CheckResult(RESULT_UNKNOWN, filter.replace("\n", "|"))
    }


    private fun getProperty(propName: String): String? {
        val property = CommandUtil.getProperty(propName)
        return if (TextUtils.isEmpty(property)) null else property
    }


}
