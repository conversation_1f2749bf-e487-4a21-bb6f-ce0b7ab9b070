package com.ybm100.app.crm.model.hycustomer;


import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.HyApiService;
import com.ybm100.app.crm.bean.drugstore.PrivateListFilterBean;
import com.ybm100.app.crm.bean.hycustomer.HyPrivateListBean;
import com.ybm100.app.crm.contract.hycustomer.HyPrivateListContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;
import com.ybm100.app.crm.task.bean.TaskAndMerchantBean;

import java.util.HashMap;

import io.reactivex.Observable;

/**
 *荷叶健康私海客户model
 */
public class HyPrivateListModel implements HyPrivateListContract.IHyPrivateListModel {

    public static HyPrivateListModel newInstance() {
        return new HyPrivateListModel();
    }

    @Override
    public Observable<RequestBaseBean<HyPrivateListBean>> getPrivateListData(HashMap<String, String> map) {
        return RetrofitCreateHelper.createApi(HyApiService.class).getPrivateListData(map)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean<TaskAndMerchantBean>> toAddVisit(String merchantId, String customerType) {
        return RetrofitCreateHelper.createApi(HyApiService.class).toAddVisit(merchantId, customerType)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean<PrivateListFilterBean>> getFilterItems() {
        return RetrofitCreateHelper.createApi(HyApiService.class).getFilterItems()
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean> distributeToBD(String bindUserId, String customerId) {
        return RetrofitCreateHelper.createApi(HyApiService.class).distributeToBD(bindUserId, customerId)
                .compose(RxHelper.rxSchedulerHelper());
    }
}