package com.ybm100.app.crm.contract.drugstore.minedrug;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.DrugstoreUpdateLogBean;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:15
 * 药店信息更新日志。
 */
public interface InformationChangelogContract {

    interface IInformationChangelogModel extends IBaseModel {
        Observable<RequestBaseBean<DrugstoreUpdateLogBean>> getInfoChangeLogData(int limit,
                                                                                 String merchantId,
                                                                                 int offset);
    }

    interface IInformationChangelogView extends IBaseActivity {
        void getInfoChangeLogDataSuccess(RequestBaseBean<DrugstoreUpdateLogBean> requestBaseBean);
    }

}
