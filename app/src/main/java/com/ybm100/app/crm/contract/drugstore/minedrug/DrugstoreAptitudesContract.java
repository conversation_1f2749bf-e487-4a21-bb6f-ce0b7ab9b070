package com.ybm100.app.crm.contract.drugstore.minedrug;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.AptitudesBean;
import com.ybm100.app.crm.order.bean.AptitudeInitBean;
import com.ybm100.app.crm.order.bean.InvoiceHasBean;

import java.util.HashMap;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:08
 */
public interface DrugstoreAptitudesContract {

    interface IDrugstoreAptitudesModel extends IBaseModel {
        Observable<RequestBaseBean<AptitudesBean>> getAptitudeDetailList(String merchantId);

        /**
         * 验证药店是否存在发票类型
         *
         * @param merchantId
         * @return
         */
        Observable<RequestBaseBean<InvoiceHasBean>> isHaveType(String merchantId);

        Observable<RequestBaseBean<AptitudeInitBean>> initLicenseAuditDetail(HashMap<String, String> map );

    }

    interface IDrugstoreAptitudesView extends IBaseActivity {

        void getAptitudeDetailListSuccess(RequestBaseBean<AptitudesBean> requestBaseBean);

        void isHaveTypeSuccess(RequestBaseBean<InvoiceHasBean> requestBaseBean);

        void initLicenseAuditDetailSuccess(RequestBaseBean<AptitudeInitBean> requestBaseBean);
    }

}
