package com.ybm100.app.crm.flutter.channel

import android.text.TextUtils
import android.util.Log
import androidx.fragment.app.FragmentActivity
import com.baidu.mapapi.search.Constants
import com.baidu.mapapi.search.bean.option.route.DrivingRoutePlanOptionBean
import com.baidu.mapapi.search.bean.result.route.drivingroute.BMFDrivingRouteResultBean
import com.baidu.mapapi.search.core.SearchResult
import com.baidu.mapapi.search.route.*
import com.baidu.mapapi.search.utils.GsonFactory
import com.baidu.mapapi.search.utils.ParseErrorCode
import com.google.gson.JsonSyntaxException
import com.google.gson.reflect.TypeToken
import com.xyy.flutter.container.container.bridge.BaseHandler
import com.xyy.flutter.container.container.ui.FlutterRunnerActivity

class SearchDrivingLineHandler : BaseHandler(), OnGetRoutePlanResultListener {

    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {
        handleDrivingSearch(params)
    }

    private fun handleDrivingSearch(argumentsMap: Map<String, Any?>) {
        val gson = GsonFactory.getInstance().gson
        val routePlanSearch = RoutePlanSearch.newInstance()
        routePlanSearch.setOnGetRoutePlanResultListener(this)
        val optionMap = argumentsMap["drivingRoutePlanOption"] as HashMap<String, Any>?
        if (null == optionMap) {
            sendReturnResult(false)
            return
        }
        val jsonStr: String = gson.toJson(optionMap)
        if (null == jsonStr) {
            sendReturnResult(false)
            return
        }
        val optionBean: DrivingRoutePlanOptionBean = gson.fromJson(jsonStr, DrivingRoutePlanOptionBean::class.java)
        if (null == optionBean) {
            sendReturnResult(false)
            return
        }
        val option = optionBean.toOption()
        var ret = false
        try {
            ret = routePlanSearch.drivingSearch(option)
        } catch (e: IllegalArgumentException) {
            e.printStackTrace()
        } catch (e: IllegalStateException) {
            e.printStackTrace()
        }
//        sendReturnResult(ret)
    }

    fun sendReturnResult(ret: Boolean) {
        result?.success(object : java.util.HashMap<String?, Boolean?>() {
            init {
                put(Constants.RESULT_KEY, ret)
            }
        })
    }

    fun sendSearchResult(value: Any?, errorCode: Int) {
        Log.e("guan search", "guan search sendSearchResult $value")
        try {
            result?.success(hashMapOf(Constants.RESULT_KEY to value, Constants.ERROR_KEY to errorCode))
        } catch (e: Exception) {
            Log.e("guan search", e.message)
        }
    }

    override fun onGetWalkingRouteResult(p0: WalkingRouteResult?) {
        TODO("Not yet implemented")
    }

    override fun onGetTransitRouteResult(p0: TransitRouteResult?) {
        TODO("Not yet implemented")
    }

    override fun onGetMassTransitRouteResult(p0: MassTransitRouteResult?) {
        TODO("Not yet implemented")
    }

    override fun onGetDrivingRouteResult(drivingRouteResult: DrivingRouteResult?) {
        val gson = GsonFactory.getInstance().gson
        if (null == drivingRouteResult) {
            sendSearchResult(null, -1)
            return
        }

        val bmfDrivingRouteResultBean = BMFDrivingRouteResultBean(drivingRouteResult)

        val resultJson: String = gson.toJson(bmfDrivingRouteResultBean)
        if (TextUtils.isEmpty(resultJson)) {
            sendSearchResult(null, -1)
            return
        }

        try {
            val resultMap: java.util.HashMap<String, Any> = gson.fromJson(resultJson, object : TypeToken<java.util.HashMap<String?, Any?>?>() {}.type)
            val errorStr = resultMap["error"] as String?
            if (TextUtils.isEmpty(errorStr)) {
                sendSearchResult(null, -1)
                return
            }
            val errorCode = ParseErrorCode.getInstance()
                    .getErrorCode(SearchResult.ERRORNO.valueOf(errorStr!!))
            sendSearchResult(resultMap, errorCode)
        } catch (e: JsonSyntaxException) {
            Log.e(javaClass.simpleName, e.toString())
        }
    }

    override fun onGetIndoorRouteResult(p0: IndoorRouteResult?) {
        TODO("Not yet implemented")
    }

    override fun onGetBikingRouteResult(p0: BikingRouteResult?) {
        TODO("Not yet implemented")
    }

}
