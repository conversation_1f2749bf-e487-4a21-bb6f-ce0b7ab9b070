package com.ybm100.app.crm.contract.drugstore.minedrug;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.CartBean;
import com.ybm100.app.crm.goodsmanagement.bean.EstimatedPriceListBean;

import java.util.HashMap;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:20
 */
public interface ShoppingCommodityContract {

    interface IShoppingCommodityModel extends IBaseModel {
        Observable<RequestBaseBean<CartBean>> getCartListRequest(String shopId);
        Observable<RequestBaseBean<EstimatedPriceListBean>> getEstimatedPrices(HashMap<String, String> queryMap);
    }

    interface IShoppingCommodityView extends IBaseActivity {
        void getCartListRequestSuccess(RequestBaseBean<CartBean> requestBaseBean);
        void onGetEstimatedPricesSuccess(RequestBaseBean<EstimatedPriceListBean> data);
    }

}
