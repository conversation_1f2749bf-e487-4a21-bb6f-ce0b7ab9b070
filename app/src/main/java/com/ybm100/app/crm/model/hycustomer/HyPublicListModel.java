package com.ybm100.app.crm.model.hycustomer;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.HyApiService;
import com.ybm100.app.crm.bean.hycustomer.HyPublicListBean;
import com.ybm100.app.crm.contract.hycustomer.HyPublicListContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import java.util.HashMap;

import io.reactivex.Observable;

/**
 * 荷叶健康公海客户model
 */
public class HyPublicListModel extends BaseModel implements HyPublicListContract.IHyPublicListModel {

    public static HyPublicListModel newInstance() {
        return new HyPublicListModel();
    }


    @Override
    public Observable<RequestBaseBean<HyPublicListBean>> searchOpenSea(HashMap<String, String> map) {
        return RetrofitCreateHelper.createApi(HyApiService.class).searchOpenSea(map)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean> receive(String id) {
        return RetrofitCreateHelper.createApi(HyApiService.class).receive(id)
                .compose(RxHelper.rxSchedulerHelper());
    }
}

