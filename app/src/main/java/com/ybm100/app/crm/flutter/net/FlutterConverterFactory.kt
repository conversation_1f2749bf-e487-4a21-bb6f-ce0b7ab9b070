package com.ybm100.app.crm.flutter.net

import com.google.gson.Gson
import com.google.gson.TypeAdapter
import com.google.gson.reflect.TypeToken
import okhttp3.MediaType
import okhttp3.RequestBody
import okhttp3.ResponseBody
import okio.Buffer
import retrofit2.Converter
import retrofit2.Retrofit
import java.io.IOException
import java.io.OutputStreamWriter
import java.io.Writer
import java.lang.reflect.Type
import java.nio.charset.Charset

class FlutterConverterFactory(val gson: Gson) : Converter.Factory() {


    companion object {
        fun create(): FlutterConverterFactory {
            return FlutterConverterFactory(Gson())
        }
    }

    override fun responseBodyConverter(type: Type, annotations: Array<Annotation>, retrofit: Retrofit): Converter<ResponseBody, *>? {
        return FlutterResponseBodyConverter()
    }

    override fun requestBodyConverter(type: Type?,
                                      parameterAnnotations: Array<Annotation?>?, methodAnnotations: Array<Annotation?>?, retrofit: Retrofit?): Converter<*, RequestBody?>? {
        val adapter: TypeAdapter<*> = gson.getAdapter(TypeToken.get(type))
        return MyGsonRequestBodyConverter<Any>(gson, adapter as TypeAdapter<Any>)
    }

    internal class FlutterResponseBodyConverter : Converter<ResponseBody, String?> {

        @Throws(IOException::class)
        override fun convert(value: ResponseBody): String? {
            return value.string()
        }
    }

    internal class MyGsonRequestBodyConverter<T>(
            private val gson: Gson, private val adapter: TypeAdapter<Any>) : Converter<T, RequestBody?> {
        @Throws(IOException::class)
        override fun convert(value: T): RequestBody? {
            val buffer = Buffer()
            val writer: Writer = OutputStreamWriter(buffer.outputStream(), UTF_8)
            val jsonWriter = gson.newJsonWriter(writer)
            adapter.write(jsonWriter, value)
            jsonWriter.close()
            return RequestBody.create(MEDIA_TYPE, buffer.readByteString())
        }

        companion object {
            private val MEDIA_TYPE = MediaType.parse("application/json; charset=UTF-8")
            private val UTF_8 = Charset.forName("UTF-8")
        }

    }

}