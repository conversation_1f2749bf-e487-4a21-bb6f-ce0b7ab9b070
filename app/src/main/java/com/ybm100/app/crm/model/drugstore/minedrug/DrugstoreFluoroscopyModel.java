package com.ybm100.app.crm.model.drugstore.minedrug;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiDrugstoreInfo;
import com.ybm100.app.crm.bean.drugstore.PharmacyPerspective;
import com.ybm100.app.crm.contract.drugstore.minedrug.DrugstoreFluoroscopyContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:17
 */
public class DrugstoreFluoroscopyModel extends BaseModel implements DrugstoreFluoroscopyContract.IDrugstoreFluoroscopyModel {

    public static DrugstoreFluoroscopyModel newInstance() {
        return new DrugstoreFluoroscopyModel();
    }

    @Override
    public Observable<RequestBaseBean<PharmacyPerspective>> getPerspectiveData(String shopId) {
        return RetrofitCreateHelper.createApi(ApiDrugstoreInfo.class).getPerspectiveData(shopId)
                .compose(RxHelper.rxSchedulerHelper());
    }
}