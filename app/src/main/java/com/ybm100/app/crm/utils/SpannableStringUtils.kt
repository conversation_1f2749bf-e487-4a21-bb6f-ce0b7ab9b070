package com.ybm100.app.crm.utils

import android.content.Context
import android.graphics.Typeface
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.text.style.RelativeSizeSpan
import android.text.style.StyleSpan
import androidx.core.content.ContextCompat
import com.ybm100.app.crm.R

object SpannableStringUtils {

    fun highlightStart(context: Context, highlightStartColor: Int, startText: String? = "", endText: String? = ""): SpannableStringBuilder {
        val builder = SpannableStringBuilder()
        builder.append(startText)
        builder.append(endText)
        val highlightStart = 0
        val highlightEnd: Int = startText?.length ?: 0

        //头部高亮
        builder.setSpan(ForegroundColorSpan(ContextCompat.getColor(context, highlightStartColor)), highlightStart, highlightEnd, SpannableString.SPAN_INCLUSIVE_EXCLUSIVE)
        //头部大小
        val index = startText?.split(".")?.getOrNull(0)?.length ?: 1

        builder.setSpan(RelativeSizeSpan(1.5f), 0, 1, SpannableString.SPAN_INCLUSIVE_EXCLUSIVE)
        builder.setSpan(RelativeSizeSpan(2f), 1, index, SpannableString.SPAN_INCLUSIVE_EXCLUSIVE)
        builder.setSpan(RelativeSizeSpan(1.5f), index, startText?.length
                ?: index, SpannableString.SPAN_INCLUSIVE_EXCLUSIVE)

        return builder
    }

    fun highlightMiddle(context: Context, highlightMiddleColor: Int, startText: String? = "", middleText: String? = "", endText: String? = ""): SpannableStringBuilder {
        val builder = SpannableStringBuilder()
        builder.append(startText)
        builder.append(middleText)
        builder.append(endText)
        val highlightStart = startText?.length ?: 0
        val highlightEnd = highlightStart + (middleText?.length ?: highlightStart)

        //中间高亮
        builder.setSpan(ForegroundColorSpan(ContextCompat.getColor(context, highlightMiddleColor)), highlightStart, highlightEnd, SpannableString.SPAN_INCLUSIVE_EXCLUSIVE)

        return builder
    }

    fun highlightEnd(context: Context, highlightEndColor: Int, startText: String? = "", endText: String? = ""): SpannableStringBuilder {
        val builder = SpannableStringBuilder()
        builder.append(startText)
        builder.append(endText)
        val highlightStart = startText?.length ?: 0
        val highlightEnd = highlightStart + (endText?.length ?: highlightStart)

        //尾部高亮
        builder.setSpan(ForegroundColorSpan(ContextCompat.getColor(context, highlightEndColor)), highlightStart, highlightEnd, SpannableString.SPAN_INCLUSIVE_EXCLUSIVE)

        return builder
    }

    fun goalsProgress(context: Context, startText: String? = "", middleText: String? = "", endText: String? = ""): SpannableStringBuilder{
        val builder = SpannableStringBuilder()
        builder.append(startText)
        builder.append(middleText)
        builder.append(endText)

        val startTextIndexStart = 0
        val startTextIndexEnd = (startText?.length ?: 1) - 1
        val endTextIndexStart = (startText?.length ?: 0) + 1
        val endTextIndexEnd = endTextIndexStart + (endText?.length ?: 1) - 1

        builder.setSpan(ForegroundColorSpan(ContextCompat.getColor(context, R.color.color_0D0E10)), startTextIndexStart, startTextIndexEnd, SpannableString.SPAN_INCLUSIVE_EXCLUSIVE)
        builder.setSpan(ForegroundColorSpan(ContextCompat.getColor(context, R.color.color_0D0E10)), endTextIndexStart, endTextIndexEnd, SpannableString.SPAN_INCLUSIVE_EXCLUSIVE)

        builder.setSpan(StyleSpan(Typeface.BOLD), startTextIndexStart, startTextIndexEnd, SpannableString.SPAN_INCLUSIVE_EXCLUSIVE)
        builder.setSpan(StyleSpan(Typeface.BOLD), endTextIndexStart, endTextIndexEnd, SpannableString.SPAN_INCLUSIVE_EXCLUSIVE)

        builder.setSpan(RelativeSizeSpan(2.2f), startTextIndexStart, startTextIndexEnd, SpannableString.SPAN_INCLUSIVE_EXCLUSIVE)
        builder.setSpan(RelativeSizeSpan(1.17f), endTextIndexStart, endTextIndexEnd, SpannableString.SPAN_INCLUSIVE_EXCLUSIVE)

        return builder
    }

    fun estimatedPrice(context: Context, text: String?): SpannableStringBuilder{
        val builder = SpannableStringBuilder()
        builder.append(text)

        val start = text?.indexOf(" ") ?: -1
        val end = text?.length ?: -1

        if (start != -1 && end != -1){
            builder.setSpan(StyleSpan(Typeface.BOLD), start, end, SpannableString.SPAN_INCLUSIVE_EXCLUSIVE)
        }
        return builder
    }
}