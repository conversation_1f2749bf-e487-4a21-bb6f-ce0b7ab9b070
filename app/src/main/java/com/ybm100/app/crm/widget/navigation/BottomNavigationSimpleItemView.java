package com.ybm100.app.crm.widget.navigation;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.DrawableRes;
import androidx.annotation.Nullable;

import com.xyy.utilslibrary.utils.ScreenUtils;
import com.ybm100.app.crm.R;

/**
 * 底部导航Item
 */

public class BottomNavigationSimpleItemView extends FrameLayout {

    public TextView tv_text;
    public ImageView iv_icon;
    private final TextView tv_dot;//小红点显示数字

    public BottomNavigationSimpleItemView(Context context) {
        this(context, null);
    }

    public BottomNavigationSimpleItemView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BottomNavigationSimpleItemView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        LayoutInflater.from(context).inflate(R.layout.layout_custom_bottom_navigation_simple_item, this, true);
        tv_text = findViewById(R.id.tv_item_text);
        iv_icon = findViewById(R.id.iv_item_icon);
        tv_dot = findViewById(R.id.tv_dot);
    }

    public void setText(String text) {
        if (TextUtils.isEmpty(text)) {
            tv_text.setVisibility(View.GONE);
        } else {
            tv_text.setVisibility(View.VISIBLE);
            tv_text.setText(text);
        }
    }

    public void setIcon(Context context, @DrawableRes int resId, int i) {
        // 动态设置中间按钮宽高
        if (i == 2) {
            int screenWidth = ScreenUtils.getScreenWidth(context);
            ViewGroup.LayoutParams params = iv_icon.getLayoutParams();
            params.width = screenWidth / 10;
            params.height = screenWidth / 10;
            iv_icon.setLayoutParams(params);
        }
        iv_icon.setImageResource(resId);
    }

    public void setSelected(boolean isSelected) {
        tv_text.setSelected(isSelected);
        iv_icon.setSelected(isSelected);
    }

    /**
     * 设置右上角的小红点数字值，如果为0，则只显示红点，不显示数字，如果小于0，数字红点都不显示
     *
     * @param num
     */
    public void setDotNum(int num) {
        if (num == 0) {
            tv_dot.setText("");
            tv_dot.setVisibility(VISIBLE);
        } else if (num < 0) {
            tv_dot.setVisibility(GONE);
        } else {
            tv_dot.setVisibility(VISIBLE);
            tv_dot.setText(String.valueOf(num));
        }
    }

}
