package com.ybm100.app.crm.goodsmanagement.activity

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import com.fold.dialog.listener.DismissOnBtnClickL
import com.google.android.flexbox.AlignItems
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.FlexboxLayoutManager
import com.xyy.common.util.StringUtils
import com.xyy.utilslibrary.base.activity.BaseCompatActivity
import com.ybm100.app.crm.R
import com.ybm100.app.crm.constant.Constants
import com.ybm100.app.crm.goodsmanagement.widget.searchview.SearchViewAdapter
import com.ybm100.app.crm.goodsmanagement.widget.searchview.SearchViewItemBean
import com.ybm100.app.crm.utils.DialogUtils
import kotlinx.android.synthetic.main.activity_base_search_v2.*

/**
 * 选择商品
 */
class BaseSearchActivity : BaseCompatActivity() {
    private var mSearchType = -1
    private var mAdapter: SearchViewAdapter? = null
    private var mMerchantID: String = ""
    private var mAreaCode: String = ""

    override fun initTransferData() {
        super.initTransferData()
        intent?.extras?.run {
            mSearchType = getInt(Constants.GoodsManagement.ARG_FRAGMENT_TYPE, -1)
            mMerchantID = getString(Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_MERCHANT_ID, "")
            mAreaCode = getString(Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_AREA_CODE, "")
        }
        intent?.data?.let {
            if (mMerchantID.isNotEmpty()) {
                return
            }
            mMerchantID = it.getQueryParameter("customerId") ?: ""
            mSearchType = it.getQueryParameter("androidSearchType")?.toIntOrNull() ?: -1
            mAreaCode = ""
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.activity_base_search_v2
    }

    override fun initView(savedInstanceState: Bundle?) {
        initSearchView()

        initRecyclerView()
    }

    private fun initSearchView() {
        //SearchView text hint
        when (mSearchType) {
//            else -> search_view?.textHint = "搜索"
        }
        //取消按钮点击事件
        search_view.setOnClickBack {
            finish()
        }

        search_view.searchTypeKey = "$mSearchType"

        //键盘搜索点击
        search_view.setOnClickSearch {
            val textKey: String = search_view.text.trim()
            if (StringUtils.isEmpty(textKey)) {
                when (mSearchType) {
                    /**
                     * 搜索为空吐司
                     */
                }
            } else {
                switchToResult(textKey)
            }
        }
        //清空搜索历史按钮点击事件
        ib_search_delete.setOnClickListener {
            DialogUtils.showDefaultDialog(this, "确定清空搜索历史", object : DismissOnBtnClickL() {
                override fun onBtnClick() {
                    search_view.clearSearchHistory("$mSearchType")
                    group_history.visibility = View.GONE

                    mAdapter?.setNewData(null)
                }
            })
        }
    }

    private fun switchToResult(text: String?) {
        when (mSearchType) {

            else -> {
                BaseSearchResultActivity.startActivity(
                    this,
                    mSearchType,
                    text ?: "",
                    mMerchantID,
                    mAreaCode
                )
            }

        }

    }

    private fun initRecyclerView() {

        mAdapter = SearchViewAdapter()

        recycler_view.apply {
            layoutManager = FlexboxLayoutManager(context).apply {
                flexWrap = FlexWrap.WRAP
                flexDirection = FlexDirection.ROW
                alignItems = AlignItems.STRETCH
            }
            adapter = mAdapter
        }

        mAdapter?.setOnItemClickListener { adapter, view, position ->
            val strText = (adapter.getItem(position) as? SearchViewItemBean)?.name
            search_view.text = strText
            search_view.saveSearchHistory("$mSearchType", strText)
            switchToResult(strText)
        }

        mAdapter?.setOnItemChildClickListener { adapter, view, position ->
            search_view.deleteItem("$mSearchType", mAdapter?.data?.getOrNull(position)?.name ?: "")
            getData()
        }

        getData()
    }

    override fun onResume() {
        super.onResume()
        getData()
    }

    private fun getData() {
        var list = search_view.getSearchHistory("$mSearchType")
        if (list.isNullOrEmpty()) {
            group_history.visibility = View.GONE
            return
        } else {
            group_history.visibility = View.VISIBLE
        }
        if (list.size > 10) {
            list = list.subList(0, 10)
        }
        val tagList = mutableListOf<SearchViewItemBean>()
        list.forEachIndexed { index, s ->
            tagList.add(SearchViewItemBean(s))
        }

        mAdapter?.setNewData(tagList)
    }

    companion object {
        @JvmStatic
        fun startActivity(
            activity: Activity?,
            searchType: Int,
            merchantID: String? = "",
            areaCode: String? = ""
        ) {
            val intent = Intent(activity, BaseSearchActivity::class.java)
            val bundle = Bundle()
            bundle.putInt(Constants.GoodsManagement.ARG_FRAGMENT_TYPE, searchType)
            bundle.putString(Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_MERCHANT_ID, merchantID)
            bundle.putString(Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_AREA_CODE, areaCode)
            intent.putExtras(bundle)
            activity?.startActivity(intent)
        }
    }
}
