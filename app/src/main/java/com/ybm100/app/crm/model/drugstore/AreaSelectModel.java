package com.ybm100.app.crm.model.drugstore;

import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiDrugstoreService;
import com.ybm100.app.crm.bean.drugstore.AreaBean;
import com.ybm100.app.crm.contract.drugstore.AreaSelectContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import java.util.List;

import io.reactivex.Observable;

/**
 * @author: zcj
 * @time:2020/3/29. Description:
 */
public class AreaSelectModel implements AreaSelectContract.IAreaSelectModel {
    public static AreaSelectModel newInstance() {
        return new AreaSelectModel();
    }

    @Override
    public Observable<RequestBaseBean<List<AreaBean>>> searchArea(String areaCode) {
        return RetrofitCreateHelper.createApi(ApiDrugstoreService.class).searchArea(areaCode).compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean<List<AreaBean>>> searchAreaV2(String areaCode) {
        return RetrofitCreateHelper.createApi(ApiDrugstoreService.class).searchAreaV2(areaCode).compose(RxHelper.rxSchedulerHelper());
    }
}
