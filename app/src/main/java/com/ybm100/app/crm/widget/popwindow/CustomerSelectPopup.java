package com.ybm100.app.crm.widget.popwindow;

import android.content.Context;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.Gravity;
import android.view.View;

import com.ybm100.app.crm.R;
import com.ybm100.app.crm.bean.goods.PopListBean;
import com.ybm100.app.crm.constant.Constants;
import com.ybm100.app.crm.ui.adapter.goods.GoodsCommendPopListAdapter;

import java.util.ArrayList;

import razerdp.basepopup.BasePopupWindow;

/**
 * @author: zcj
 * @time:2019/11/8. projectName: XyyBeanSproutsChannel.
 * Description:
 */
public class CustomerSelectPopup extends BasePopupWindow {
    private ArrayList<PopListBean> popList;

    public CustomerSelectPopup(Context context, ArrayList<PopListBean> popList, int height) {
        super(context);
        this.popList = popList;
        initRecyclerView();
        setOutSideTouchable(true);
        setPopupGravity(Gravity.BOTTOM);
        setAlignBackgroundGravity(Gravity.TOP);
        setAlignBackground(true);//背景是否对齐到PopupWindow
        setOutSideDismiss(true);// 点击popupwindow背景部分不隐藏
        setMaxHeight(height);

    }

    public void setPopList(ArrayList<PopListBean> popList) {
        this.popList = popList;
        initRecyclerView();
    }

    public ArrayList<PopListBean> getPopList() {
        return popList;
    }


    private void initRecyclerView() {
        RecyclerView rvPopDate = findViewById(R.id.rv_pop_goods_recommend);
        rvPopDate.setLayoutManager(new LinearLayoutManager(getContext()));
        GoodsCommendPopListAdapter popListAdapter = new GoodsCommendPopListAdapter(R.layout.item_pop_goods_recommend, popList, Constants.GoodsRecommendCheck.FLOW_TAG_CHECKED_SINGLE);
        popListAdapter.setOnItemClickListener((adapter, view, position) -> {
            if (clickListener != null) {
                ((GoodsCommendPopListAdapter) adapter).selectItem(position);
                clickListener.onItemClick(popList, position);
            }
            dismiss();
        });
        rvPopDate.setAdapter(popListAdapter);
    }

    @Override
    public View onCreateContentView() {
        return createPopupById(R.layout.list_popup_customer);
    }

    private OnPopItemClickListener clickListener;

    public void setOnPopItemClickListener(OnPopItemClickListener clickListener) {
        this.clickListener = clickListener;
    }

    public void setOnPopItemClickListener(GoodsRecommendPopup.OnPopItemClickListener onPopItemClickListener) {
    }

    public interface OnPopItemClickListener {
        void onItemClick(ArrayList<PopListBean> selectList, int position);
    }

    public static ArrayList<PopListBean> getPopListBean(Context context, int resId, int defaultIndex) {
        String[] strings = context.getResources().getStringArray(resId);
        ArrayList<PopListBean> result = new ArrayList<PopListBean>();
        for (int i = 0; i < strings.length; i++) {
            PopListBean dateBean = new PopListBean();
            dateBean.setContent(strings[i]);
            dateBean.setSelected(i == defaultIndex);
            result.add(dateBean);
        }
        return result;
    }

    public static ArrayList<PopListBean> reset(int defaultIndex, ArrayList<PopListBean> popList) {
        ArrayList<PopListBean> result = new ArrayList<PopListBean>();
        for (int i = 0; i < popList.size(); i++) {
            PopListBean dateBean = popList.get(i);
            dateBean.setSelected(i == defaultIndex);
            result.add(dateBean);
        }
        return result;
    }
}
