package com.ybm100.app.crm.flutter.net

import io.reactivex.Observable
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.http.*
import java.util.*

interface FlutterService {
    @GET
    fun requestGetAsk(
        @HeaderMap headerMap: HashMap<String, String>?,
        @Url url: String,
        @QueryMap map: HashMap<String, String>?
    ): Observable<String?>


    @POST
    @FormUrlEncoded
    fun requestFormPostAsk(
        @HeaderMap headerMap: HashMap<String, String>?,
        @Url url: String?,
        @FieldMap map: HashMap<String, String>?
    ): Observable<String?>


    @POST
    fun requestJsonPostAsk(
        @HeaderMap headerMap: HashMap<String, String>?,
        @Url url: String?,
        @Body body: RequestBody?
    ): Observable<String?>

    @POST
    fun requestMultipartPostAsk(
        @Url url: String?,
        @Body map: MultipartBody
    ): Observable<String?>

}