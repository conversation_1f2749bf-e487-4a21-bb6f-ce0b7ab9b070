package com.ybm100.app.crm.contract.contact;

import android.content.Context;

import com.chad.library.adapter.base.entity.MultiItemEntity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.IBaseRecyclerView;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.contact.ContactListApi;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/24/2018 10:26
 */
public interface ContactListContract {

    interface IContactListView extends IBaseRecyclerView<MultiItemEntity> {
        Context getContext();

        void updateData();

        void removeContact(int parentPosition);
    }

    interface IContactListModel extends IBaseModel {
        Observable<ContactListApi> getContactsWithMerchat(int pageNo, int pageSize, String keyword, String merchantId);
        Observable<ContactListApi> getContacts(int pageNo, int pageSize, String keyword);

        Observable<RequestBaseBean> delContact(String id);
    }

}
