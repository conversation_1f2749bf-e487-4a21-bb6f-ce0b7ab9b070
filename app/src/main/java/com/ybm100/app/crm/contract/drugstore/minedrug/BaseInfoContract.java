package com.ybm100.app.crm.contract.drugstore.minedrug;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.BaseInfo;
import com.ybm100.app.crm.task.bean.TaskAndMerchantBean;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:04
 */
public interface BaseInfoContract {
    interface IBaseInfoModel extends IBaseModel {
        Observable<RequestBaseBean<BaseInfo>> getBaseInfo(String merchantId,int isMerchantId);

        /**
         * 新建拜访（客户入口）
         *
         * @param merchantId
         * @param customerType 1客户2线索
         * @return
         */
        Observable<RequestBaseBean<TaskAndMerchantBean>> toAddVisit(String merchantId, String customerType);

        /**
         * 分配至bd
         */
        Observable<RequestBaseBean> distributeToBD(String bindUserId, String customerId,String skuCollectCodes);

        /**
         * 释放至公海
         */
        Observable<RequestBaseBean> releaseToPublic(String customerId,String skuCollectCodes);
    }

    interface IBaseInfoView extends IBaseActivity {
        void getBaseInfo(RequestBaseBean<BaseInfo> requestBaseBean);

        void distributeToBDSuccess(RequestBaseBean baseBean);


        void toAddVisit(RequestBaseBean<TaskAndMerchantBean> requestBaseBean);

        void releaseToPublicSuccess(RequestBaseBean baseBean);
    }
}
