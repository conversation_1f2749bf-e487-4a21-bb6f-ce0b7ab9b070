package com.ybm100.app.crm.presenter.hycustomer;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.hycustomer.HyPublicListBean;
import com.ybm100.app.crm.contract.hycustomer.HyPublicListContract;
import com.ybm100.app.crm.model.hycustomer.HyPublicListModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

import java.util.HashMap;
import java.util.List;

/**
 * 荷叶健康公海客户列表Presenter
 */
public class HyPublicListPresenter extends BasePresenter<HyPublicListContract.IHyPublicListModel, HyPublicListContract.IHyPublicListView> {
    int pageSize = 10;
    int pageNo = 0;

    public static HyPublicListPresenter newInstance() {
        return new HyPublicListPresenter();
    }

    @Override
    protected HyPublicListModel getModel() {
        return HyPublicListModel.newInstance();
    }

    /**
     * 获取未认领药店列表
     */
    public void searchOpenSea(final boolean refresh, HashMap<String, String> map) {
        if (mIView == null || mIModel == null) return;
        if (refresh) {
            pageNo = 0;
            mIView.enableLoadMore(true);
        }
        if (map == null) {
            map = new HashMap<>();
        }
        map.put("offset", String.valueOf(pageNo));
        map.put("limit", String.valueOf(pageSize));
        mRxManager.register(mIModel.searchOpenSea(map)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean<HyPublicListBean>>(mIView,true) {
                    @Override
                    public void onSuccess(RequestBaseBean<HyPublicListBean> listRequestBaseBean) {
                        if (mIView == null) return;
                        if (listRequestBaseBean.getData() == null ||listRequestBaseBean.getData().getPageData()==null|| listRequestBaseBean.getData().getPageData().getRows() == null) {
                            mIView.showEmpty();
                            return;
                        }
                        List<HyPublicListBean.RowBean> result = listRequestBaseBean.getData().getPageData().getRows();
                        if (result != null) {
                            if (listRequestBaseBean.getData().getPageData().isLastPage()) {
                                mIView.loadMoreComplete();//超出一页没有更多的数据
                            }
                            mIView.searchOpenSeaSuccess(refresh, listRequestBaseBean);
                        }
                        pageNo++;
                    }

                    @Override
                    public void onFailure(int errorCode) {
                        super.onFailure(errorCode);
                        if (mIView == null) return;
                        mIView.showNetError();
                    }
                }, new SimpleErrorConsumer(mIView) {
            @Override
            protected void onError(Throwable throwable, String msg) {
                super.onError(throwable, msg);
                if (mIView == null) return;
                mIView.showNetError();
            }
        }));
    }

    /**
     * 认领药店
     */
    public void receive(String merchantId) {
        if (mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.receive(merchantId)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean>(mIView, "") {
                    @Override
                    public void accept(RequestBaseBean baseBean) throws Exception {
                        if (mIView == null) return;
                        mIView.hideWaitDialog();
                        if (baseBean.isSuccess()) {
                            mIView.receiveSuccess(baseBean);
                        } else if (!baseBean.isSuccess() && baseBean.getCode() == 405){
                            mIView.receiveSuccess(baseBean);
                        }else {
                            mIView.showToast(baseBean.getErrorMsg());
                        }
                    }

                    @Override
                    public void onSuccess(RequestBaseBean listRequestBaseBean) {
                    }
                }, new SimpleErrorConsumer(mIView)));
    }

}
