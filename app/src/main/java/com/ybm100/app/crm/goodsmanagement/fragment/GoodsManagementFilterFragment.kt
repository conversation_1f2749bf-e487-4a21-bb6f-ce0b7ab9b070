package com.ybm100.app.crm.goodsmanagement.fragment

import android.os.Bundle
import android.view.View
import android.widget.CheckBox
import androidx.recyclerview.widget.RecyclerView
import com.google.android.flexbox.AlignItems
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.FlexboxLayoutManager
import com.xyy.userbehaviortracking.utils.UserBehaviorTrackingUtils
import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.base.fragment.BaseMVPCompatFragment
import com.ybm100.app.crm.R
import com.ybm100.app.crm.constant.Constants
import com.ybm100.app.crm.goodsmanagement.adapter.GoodsManagementFilterAdapter
import com.ybm100.app.crm.goodsmanagement.adapter.GoodsZoneFilterAdapter
import com.ybm100.app.crm.goodsmanagement.bean.AvailableZone
import com.ybm100.app.crm.goodsmanagement.bean.GoodsManagementAreaListBean
import com.ybm100.app.crm.goodsmanagement.bean.Zone
import com.ybm100.app.crm.goodsmanagement.contract.GoodsManagementAreaContract
import com.ybm100.app.crm.goodsmanagement.presenter.GoodsManagementAreaPresenter
import kotlinx.android.synthetic.main.fragment_goods_management_filter.*

class GoodsManagementFilterFragment : BaseMVPCompatFragment<GoodsManagementAreaPresenter>(), GoodsManagementAreaContract.IGoodsManagementAreaView, View.OnClickListener {
    private var mPreSelectedPosition = 0
    private var mGoodsManagementFilterAdapter: GoodsManagementFilterAdapter? = null
    private var mType = -1
    private var mAreaCode = ""
    private lateinit var mDrawerListener: DrawerListener


    private var mZoneListView: RecyclerView? = null
    private var mZoneLabelView: View? = null
    private var mAdapter: GoodsZoneFilterAdapter? = null
    private var mFoldingView: CheckBox? = null

    private var mAllAreaListData: List<GoodsManagementAreaListBean.Row?>? = null

    override fun initData() {
        super.initData()
        arguments?.run {
            mType = getInt(Constants.GoodsManagement.ARG_FRAGMENT_TYPE, -1)
            mAreaCode = getString(Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_AREA_CODE, "")
        }
    }

    override fun getLayoutId(): Int {
        return R.layout.fragment_goods_management_filter
    }

    override fun initUI(view: View?, savedInstanceState: Bundle?) {
        registerListener()

        initRecyclerView(view)

        initZoneListView(view)

        mPresenter.getAreaList()
    }

    private fun initZoneListView(view: View?) {
        mZoneListView = view?.findViewById<RecyclerView>(R.id.rv_zone_list)?.also {
            it.layoutManager = FlexboxLayoutManager(context)
            it.adapter = GoodsZoneFilterAdapter().also { adapter ->
                mAdapter = adapter
                adapter.setOnItemClickListener { adapter, view, position ->
                    if (adapter is GoodsZoneFilterAdapter) {
                        UserBehaviorTrackingUtils.track("mc-allproduct-zonesoft")
                        val preIndex = adapter.data.indexOfFirst { zone ->
                            if (zone?.isSelect == true) {
                                zone.isSelect = false
                                true
                            } else {
                                false
                            }
                        }
                        adapter.data.forEach { item ->
                            item?.isSelect = false
                        }
                        adapter.data.getOrNull(position)?.isSelect = true
                        if (preIndex != position) {
                            adapter.notifyItemChanged(preIndex)
                            adapter.notifyItemChanged(position)
                        }
                    }
                }
            }
        }
        mZoneLabelView = view?.findViewById(R.id.tv_zone_list_label)

        setZoneGroupVisible(false)
    }

    private fun registerListener() {
        rtv_reset.setOnClickListener(this)
        rtv_confirm.setOnClickListener(this)
    }

    private fun initRecyclerView(view: View?) {

        mFoldingView = view?.findViewById(R.id.cb_folding)

        mFoldingView?.setOnCheckedChangeListener { buttonView, isChecked ->
            buttonView.jumpDrawablesToCurrentState()
            if (isChecked) {
                buttonView.text = "收起"
            } else {
                buttonView.text = "更多地区"
            }
            changeAreaListData()
        }

        mGoodsManagementFilterAdapter = GoodsManagementFilterAdapter()

        recycler_view.apply {
            layoutManager = FlexboxLayoutManager(context).apply {
                flexWrap = FlexWrap.WRAP
                flexDirection = FlexDirection.ROW
                alignItems = AlignItems.STRETCH
            }
            adapter = mGoodsManagementFilterAdapter
        }

        mGoodsManagementFilterAdapter?.setOnItemClickListener { adapter, view, position ->
            mAllAreaListData?.getOrNull(mPreSelectedPosition)?.isSelected = false

            mPreSelectedPosition = position
            mAllAreaListData?.getOrNull(position)?.let { newItem ->
                newItem.isSelected = true
                requestZoneList(newItem.ecCode)
            }

            mGoodsManagementFilterAdapter?.notifyDataSetChanged()
        }
    }

    private fun setZoneGroupVisible(isVisible: Boolean) {
        (if (isVisible) View.VISIBLE else View.GONE).let {
            mZoneLabelView?.visibility = it
            mZoneListView?.visibility = it
        }
    }

    fun setDrawerListener(drawerListener: DrawerListener) {
        mDrawerListener = drawerListener
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.rtv_reset -> {
                mPreSelectedPosition = 0
                mAllAreaListData?.forEachIndexed { index, item ->
                    item?.isSelected = index == 0
                }
                mAdapter?.let {
                    requestZoneList(mAllAreaListData?.firstOrNull()?.ecCode)
                }
                mGoodsManagementFilterAdapter?.notifyDataSetChanged()
            }
            R.id.rtv_confirm -> {
                if (mType == Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_ALL_GOODS) {
                    mDrawerListener.onConfirmPressed(mGoodsManagementFilterAdapter?.data?.getOrNull(mPreSelectedPosition), 1, mAdapter?.getSelectedZone())
                } else {
                    mDrawerListener.onConfirmPressed(mGoodsManagementFilterAdapter?.data?.getOrNull(mPreSelectedPosition), 0, mAdapter?.getSelectedZone())
                }

            }
        }
    }

    override fun onGetGoodsManagementAreaListSuccess(data: RequestBaseBean<GoodsManagementAreaListBean?>?) {
        data?.data?.rows?.let {
            var selectedEcCode = ""
            if (mAreaCode.isEmpty()) {
                it.getOrNull(0)?.let { first ->
                    selectedEcCode = first.ecCode ?: ""
                    first.isSelected = true
                }
            } else {
                it.forEachIndexed { index, item ->
                    item?.isSelected = mAreaCode == item?.ecCode
                    if (mAreaCode == item?.ecCode) {
                        mPreSelectedPosition = index
                        selectedEcCode = item.ecCode
                    }
                }
            }

            mAllAreaListData = it

            if (it.size > 5) {
                mFoldingView?.visibility = View.VISIBLE
            } else {
                mFoldingView?.visibility = View.GONE
            }

            changeAreaListData()
            if (selectedEcCode.isNotEmpty()) {
                requestZoneList(selectedEcCode)
            }
        }
    }

    private fun changeAreaListData() {
        if (mFoldingView?.isChecked == true || mAllAreaListData?.size ?: 0 < 5) {
            mGoodsManagementFilterAdapter?.setNewData(mAllAreaListData)
        } else {
            mGoodsManagementFilterAdapter?.setNewData(mAllAreaListData?.subList(0, 5))
        }
    }

    override fun onGetGoodsManagementAreaListFail() {

    }

    private fun requestZoneList(ecCode: String?) {
        ecCode?.let {
            val map = HashMap<String, String>().also {
                it["branchCode"] = ecCode
            }

            mPresenter.getAvailableZone(map)
        }
    }

    override fun onGetAvailableZoneSuccess(data: RequestBaseBean<AvailableZone?>?) {
        data?.data?.rows?.let { it ->
            if (it.isNotEmpty()) {
                setZoneGroupVisible(true)
                it.find {
                    it?.zoneId == "0"
                }?.isSelect = true
                mAdapter?.setNewData(it)
            }
        }
    }

    override fun onGetAvailableZoneFail() {
        clearZoneList()
    }

    private fun clearZoneList() {
        mAdapter?.data?.clear()
        mAdapter?.notifyDataSetChanged()
        setZoneGroupVisible(false)
    }


    override fun initPresenter(): BasePresenter<*, *> {
        return GoodsManagementAreaPresenter()
    }

    override fun showNetError() {
    }

    interface DrawerListener {
        fun onConfirmPressed(itemArea: GoodsManagementAreaListBean.Row?, pos: Int, selectedZone: Zone?)
    }

    companion object {
        @JvmStatic
        fun newInstance(type: Int, areaCode: String?) =
                GoodsManagementFilterFragment().apply {
                    arguments = Bundle().apply {
                        putInt(Constants.GoodsManagement.ARG_FRAGMENT_TYPE, type)
                        putString(Constants.GoodsManagement.ARG_GOODS_MANAGEMENT_AREA_CODE, areaCode
                                ?: "")
                    }
                }
    }
}