package com.ybm100.app.crm.goodsmanagement.presenter

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.ybm100.app.crm.goodsmanagement.bean.EstimatedPriceListBean
import com.ybm100.app.crm.goodsmanagement.bean.GoodsManagementListBean
import com.ybm100.app.crm.goodsmanagement.bean.VarietyListBean
import com.ybm100.app.crm.goodsmanagement.contract.GoodsManagementContract
import com.ybm100.app.crm.goodsmanagement.model.GoodsManagementModel
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer
import com.ybm100.app.crm.task.bean.ShareConfirm
import io.reactivex.functions.Consumer

class GoodsManagementPresenter(private val isCustomerGoodsManagement: Boolean) :
    BasePresenter<GoodsManagementContract.IGoodsManagementModel, GoodsManagementContract.IGoodsManagementView>() {
    var mQueryMap: MutableMap<String, String> = mutableMapOf()
    private var mOffset: Int = 0

    init {
        initQueryMap()
    }

    private fun initQueryMap() {
        mOffset = 0
        mQueryMap["offset"] = "$mOffset"
        mQueryMap["limit"] = "10"
    }

    override fun getModel(): GoodsManagementContract.IGoodsManagementModel {
        return GoodsManagementModel(isCustomerGoodsManagement)
    }

    fun getGoodsList(isRefresh: Boolean) {
        if (isRefresh) {
            mOffset = 0
            mQueryMap["offset"] = "$mOffset"
        }
        try {

            mRxManager.register(mIModel.getGoodsManagementGoodsList(mQueryMap)
                .subscribe(object :
                    SimpleSuccessConsumer<RequestBaseBean<GoodsManagementListBean?>?>(
                        mIView,
                        ""
                    ) {
                    override fun onSuccess(requestBaseBean: RequestBaseBean<GoodsManagementListBean?>?) {

                        mQueryMap["offset"] = "${++mOffset}"
                        mIView.onGetGoodsManagementGoodsListSuccess(
                            requestBaseBean, isRefresh, requestBaseBean?.data?.lastPage
                                ?: false
                        )
                    }
                }, SimpleErrorConsumer(mIView))
            )
        } catch (ignore: Exception) {

        }
    }

    fun getVarietyList(branchCode: String) {
        mRxManager.register(mIModel.getVarietyList(branchCode)
            .subscribe(object :
                SimpleSuccessConsumer<RequestBaseBean<VarietyListBean?>?>(mIView, "") {
                override fun onSuccess(requestBaseBean: RequestBaseBean<VarietyListBean?>?) {

                    mIView.onGetVarietyListSuccess(requestBaseBean)
                }

            }, SimpleErrorConsumer(mIView))
        )
    }

    fun collect(map: Map<String, String>, position: Int) {
        mRxManager.register(mIModel.collect(map)
            .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<Any?>?>(mIView, "") {
                override fun onSuccess(requestBaseBean: RequestBaseBean<Any?>?) {

                    mIView.onCollectSuccess(requestBaseBean, position)
                }

            }, Consumer { })
        )
    }

    fun requestShareConfirm(goods: String, taskId: String = "") {
        val queryMap = mutableMapOf<String, String>()
        queryMap["skuIds"] = goods
        if (taskId.isNotEmpty()) {
            queryMap["taskId"] = taskId
        }
        mRxManager.register(mIModel.requestShareConfirm(queryMap)
            .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<ShareConfirm?>?>(mIView, "") {
                override fun onSuccess(requestBaseBean: RequestBaseBean<ShareConfirm?>?) {
                    mIView.onRequestShareConfirmSuccess(requestBaseBean)
                }

            }, SimpleErrorConsumer(mIView))
        )
    }

    fun getEstimatedPrices(goodIDs: String, merchantID: String?) {
        val queryMap = mutableMapOf<String, String>()
        queryMap["idList"] = goodIDs
        queryMap["merchantId"] = merchantID ?: ""
        mRxManager.register(
            mIModel.getEstimatedPrices(queryMap).subscribe(object :
                SimpleSuccessConsumer<RequestBaseBean<EstimatedPriceListBean?>?>(mIView) {
                override fun onSuccess(t: RequestBaseBean<EstimatedPriceListBean?>?) {
                    mIView.onGetEstimatedPricesSuccess(t)
                }

                override fun onFailure(errorCode: Int) {
//                super.onFailure(errorCode)
                }
            }, Consumer {
            })
        )
    }

}