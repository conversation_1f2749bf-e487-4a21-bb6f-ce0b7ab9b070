package com.ybm100.app.crm.goodsmanagement.widget

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.viewpager.widget.ViewPager

class CustomerViewPager : ViewPager {
    var isHorizontallyScrollable = true

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    override fun onTouchEvent(ev: MotionEvent?): <PERSON><PERSON><PERSON> {
        return isHorizontallyScrollable && super.onTouchEvent(ev)
    }

    override fun onInterceptTouchEvent(ev: MotionEvent?): <PERSON><PERSON><PERSON> {
        try {
            return isHorizontallyScrollable && super.onInterceptTouchEvent(ev)
        } catch (ex: IllegalArgumentException) {
            ex.printStackTrace()
        }
        return false
    }

}