package com.ybm100.app.crm.function.speech;

import android.content.Context;

import com.iflytek.cloud.ErrorCode;
import com.iflytek.cloud.InitListener;
import com.iflytek.cloud.RecognizerListener;
import com.iflytek.cloud.SpeechConstant;
import com.iflytek.cloud.SpeechRecognizer;
import com.xyy.common.util.ToastUtils;
import com.xyy.utilslibrary.utils.LogUtils;
import com.ybm100.app.crm.utils.AppFileUtils;

/**
 * Author ： LoveNewsweetheart
 * Date:2018/12/28
 */
public class XunFeiSpeech {


    public static XunFeiSpeech instance;

    // 引擎类型
    private final String mEngineType = SpeechConstant.TYPE_CLOUD;

    // 语音听写对象
    private final SpeechRecognizer mIat;

    private XunFeiSpeech(Context mContext) {
        mIat = SpeechRecognizer.createRecognizer(mContext.getApplicationContext(), mInitListener);
    }

    public static XunFeiSpeech getXunFeiSpeech(Context mContext){
        if (instance == null) {
            synchronized (XunFeiSpeech.class) {
                if (instance == null) {
                    instance = new XunFeiSpeech(mContext);
                }
            }
        }
        return instance;
    }

    /**
     * 初始化监听器。
     */
    private final InitListener mInitListener = new InitListener() {

        @Override
        public void onInit(int code) {
            if (code != ErrorCode.SUCCESS) {
                ToastUtils.showShort("初始化失败，错误码：" + code);
            }
        }
    };



    public void startRecoder(RecognizerListener mRecognizerListener) {
        setParam();
        // 不使用自带的听写对话框
        int ret = mIat.startListening(mRecognizerListener);
        if (ret != ErrorCode.SUCCESS) {
            LogUtils.e("听写失败,错误码：" + ret);
            ToastUtils.showShort("听写失败");
        } else {
            //ToastUtils.showShort("请开始说话…");
        }

    }

    public void stopRecoder(){
        mIat.stopListening();
    }

    /**
     * 参数设置
     *
     * @return
     */
    private void setParam() {
        // 清空参数
        mIat.setParameter(SpeechConstant.PARAMS, null);

        // 设置听写引擎
        mIat.setParameter(SpeechConstant.ENGINE_TYPE, mEngineType);
        // 设置返回结果格式
        mIat.setParameter(SpeechConstant.RESULT_TYPE, "json");

        // 设置语言 zh_cn为简体中文
        mIat.setParameter(SpeechConstant.LANGUAGE, "zh_cn");
        // 设置语言区域
        mIat.setParameter(SpeechConstant.ACCENT, "mandarin");

        //此处用于设置dialog中不显示错误码信息
        //mIat.setParameter("view_tips_plain","false");

        // 设置语音前端点:静音超时时间，即用户多长时间不说话则当做超时处理
        mIat.setParameter(SpeechConstant.VAD_BOS, "800000000");

        // 设置语音后端点:后端点静音检测时间，即用户停止说话多长时间内即认为不再输入， 自动停止录音
        mIat.setParameter(SpeechConstant.VAD_EOS, "800000000");

        //语音输入超时时间
        mIat.setParameter(SpeechConstant.KEY_SPEECH_TIMEOUT, "8000000");

        // 设置标点符号,设置为"0"返回结果无标点,设置为"1"返回结果有标点
        mIat.setParameter(SpeechConstant.ASR_PTT, "1");

        // 设置音频保存路径，保存音频格式支持pcm、wav，设置路径为sd卡请注意WRITE_EXTERNAL_STORAGE权限
        mIat.setParameter(SpeechConstant.AUDIO_FORMAT, "wav");
        mIat.setParameter(SpeechConstant.ASR_AUDIO_PATH, AppFileUtils.getAppParentFile() + "/msc/iat.wav");
    }


}
