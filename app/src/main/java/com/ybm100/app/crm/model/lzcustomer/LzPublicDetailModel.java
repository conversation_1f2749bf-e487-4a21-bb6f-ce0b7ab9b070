package com.ybm100.app.crm.model.lzcustomer;

import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.LZApiService;
import com.ybm100.app.crm.bean.lzcustomer.LzPublicDetialBean;
import com.ybm100.app.crm.contract.lzcustomer.LzPublicDetailContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import io.reactivex.Observable;

/**
 * <AUTHOR>
 * @date 2019/1/7
 */
public class LzPublicDetailModel implements LzPublicDetailContract.ILzPublicDetailModel {
    public static LzPublicDetailModel newInstance() {
        return new LzPublicDetailModel();
    }


    @Override
    public Observable<RequestBaseBean<LzPublicDetialBean>> searchOpenSeaDetail(String id) {
        return RetrofitCreateHelper.createApi(LZApiService.class).openSeaCustomerDetail(id).compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean> receive(String id) {
        return RetrofitCreateHelper.createApi(LZApiService.class).receiveCustomer(id).compose(RxHelper.rxSchedulerHelper());
    }
}
