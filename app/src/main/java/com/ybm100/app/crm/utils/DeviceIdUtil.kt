package com.ybm100.app.crm.utils

import android.content.Context
import android.database.Cursor
import android.net.Uri
import android.util.Log
import com.xiaomi.push.it
import java.util.*

object DeviceIdUtil {
    private val YBM_DEVICE_ID_URI = Uri.parse("content://com.ybmmarket20.didcprovider/#")
    private const val YBM_DEVICE_ID_SP_KEY = "ybm_device_id"

    private fun queryYBMDeviceId(context: Context?): String? {
        if (context == null) {
            return null
        }
        var cursor: Cursor? = null
        try {
            cursor = context.contentResolver.query(YBM_DEVICE_ID_URI, null, null, null, null)
            if (cursor != null) {
                cursor.moveToNext()
                return cursor.getString(0)
            } else {
                Log.e("DeviceIdUtil", "load ybm device_id:cursor is null")
            }
        } catch (ignore: Throwable) {
        } finally {
            cursor?.close()
        }
        return null
    }


    fun loadYBMDeviceId(context: Context?) {
        queryYBMDeviceId(context)?.let {
            saveYBMDeviceID(it)
            Log.e("DeviceIdUtil", "load ybm device_id:${it}")

        }
        Timer().schedule(object : TimerTask() {
            override fun run() {
                SnowGroundUtils.track("loadYBMDeviceId", hashMapOf("device_id" to (getYBMDeviceId()
                        ?: "empty")))
            }
        }, 1000)
    }

    fun getYBMDeviceId(): String? {
        return SharedPrefManager.getInstance().getString(YBM_DEVICE_ID_SP_KEY, null)
    }

    private fun saveYBMDeviceID(deviceId: String) {
        SharedPrefManager.getInstance().setString(YBM_DEVICE_ID_SP_KEY, deviceId).apply()
    }
}