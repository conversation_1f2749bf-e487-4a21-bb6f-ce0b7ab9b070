package com.ybm100.app.crm.widget.drug

import android.content.Context
import android.util.Log
import android.view.View
import android.widget.CheckBox
import android.widget.LinearLayout
import android.widget.TextView
import com.xyy.common.util.ConvertUtils
import com.ybm100.app.crm.R
import com.ybm100.app.crm.bean.drugstore.SkuCollect
import com.ybm100.app.crm.widget.CustomBottomSheetDialog

object SkuCollectDistributeSheetHelper {
    fun showSkuCollectDialog(context: Context, titleText: String, confirmText: String, skuCollectList: List<SkuCollect>?, callback: DialogCallback?) {
        val sheetHeight = ConvertUtils.dp2px(200f)
        CustomBottomSheetDialog(context, sheetHeight, sheetHeight).apply {
            setContentView(R.layout.layout_sku_collect_distribute_dialog)
            findViewById<TextView>(R.id.tv_cancel)?.setOnClickListener {
                dismiss()
            }
            findViewById<TextView>(R.id.tv_title)?.text = titleText
            findViewById<TextView>(R.id.tv_release)?.let {
                it.text = confirmText
                it.setOnClickListener { _ ->
                    dismiss()
                    callback?.confirm(arrayListOf<SkuCollect>().also { list ->
                        findViewById<LinearLayout>(R.id.ll_sku_collect_container)?.let { container ->
                            Log.e("guan", "childCount:" + container.childCount)
                            (0..container.childCount-1).forEach { index ->
                                Log.e("guan", "childCount index:" + index)
                                if (container.getChildAt(index).isSelected) {
                                    Log.e("guan", "childCount index isSelect")
                                    skuCollectList?.get(index)?.let { skuCollect ->
                                        list.add(skuCollect)
                                    }
                                }
                            }
                        }
                    }.joinToString {
                        it.skuCollectCode ?: ""
                    })
                }
            }
            findViewById<LinearLayout>(R.id.ll_sku_collect_container)?.let { container ->
                skuCollectList?.forEach {
                    val inflateView = layoutInflater.inflate(R.layout.layout_sku_collect_distribute_item, container, false)
                    updateSkuCollectData(inflateView, it)
                    container.addView(inflateView)
                }
            }
        }.show()
    }

    private fun updateSkuCollectData(inflateView: View?, skuCollect: SkuCollect) {
        inflateView?.run {
            setOnClickListener {
                findViewById<CheckBox>(R.id.cb_check)?.let { cb ->
                    cb.isChecked = !cb.isChecked
                }
            }
            findViewById<TextView>(R.id.tv_collect_name).text = skuCollect.skuCollectName ?: "--"
            findViewById<CheckBox>(R.id.cb_check)?.let {
                it.isChecked = false
                it.setOnCheckedChangeListener { _, isChecked ->
                    Log.e("guan", "isChecked:$isChecked")
                    inflateView.isSelected = isChecked
                }
            }
        }

    }

    interface DialogCallback {
        fun confirm(selectedSkuCollectListStr: String)
    }
}