package com.ybm100.app.crm.utils;

import android.content.Context;
import android.content.pm.PackageInfo;

import com.meituan.android.walle.ChannelInfo;
import com.meituan.android.walle.WalleChannelReader;
import com.tencent.bugly.Bugly;
import com.tencent.bugly.crashreport.CrashReport;
import com.xyy.common.util.DeviceUtils;
import com.xyy.common.util.PhoneUtils;
import com.xyy.utilslibrary.utils.AppUtils;
import com.xyyio.analysis.util.DeviceInfoUtils;
import com.ybm100.app.crm.BuildConfig;
import com.ybm100.app.crm.bean.user.UserInfoBean;
import com.ybm100.app.crm.constant.AppNetConfig;

import java.nio.charset.StandardCharsets;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Bugly工具类
 */

public class BuglyUtil {

    /**
     * 配置bugly，设置为只上报主线程
     */
    public static void init(Context c) {
        CrashReport.setIsDevelopmentDevice(c, BuildConfig.DEBUG);
        final Context context = c.getApplicationContext();
        String packageName = context.getPackageName();
        // 获取当前进程名
        String processName = AppUtils.getProcessName();
        // 设置是否为上报进程
        CrashReport.UserStrategy strategy = new CrashReport.UserStrategy(context);
        strategy.setUploadProcess(processName == null || processName.equals(packageName));
        // 初始化Bugly,第三个参数为isDebug，测试阶段建议开启,false=上传
        if (!BuildConfig.DEBUG && AppNetConfig.FlavorType.PROD.equals(BuildConfig.FLAVOR)) {
            Bugly.init(c, "48f89a75bb", BuildConfig.DEBUG);
        } else {
            Bugly.init(c, "c52f2cef8a", BuildConfig.DEBUG);
        }

        final UserInfoBean userInfo = SharedPrefManager.getInstance().getUserInfo();
        strategy.setCrashHandleCallback(new CrashReport.CrashHandleCallback() {
            @Override
            public Map<String, String> onCrashHandleStart(int crashType, String errorType, String errorMessage, String errorStack) {
                LinkedHashMap<String, String> map = new LinkedHashMap<>();
                boolean login = SharedPrefManager.getInstance().isLogin();
                if (login && userInfo != null) {
                    map.put("sysUserId", userInfo.getSysUserId());
                }
                return map;
            }

            @Override
            public byte[] onCrashHandleStart2GetExtraDatas(int crashType, String errorType, String errorMessage, String errorStack) {
                try {
                    return "Extra data.".getBytes(StandardCharsets.UTF_8);
                } catch (Exception e) {
                    return null;
                }
            }
        });
        //设置渠道
        PackageInfo packageInfo = AppUtils.getVersionInfo(context);
        if (packageInfo != null) {
            strategy.setAppVersion(packageInfo.versionName);
        }
        strategy.setAppPackageName(BuildConfig.APPLICATION_ID);
        //改为20s
        strategy.setAppReportDelay(10000);
        // 上传用户信息
        if (userInfo != null) {
            CrashReport.putUserData(context, "id", userInfo.getSysUserId());
            CrashReport.putUserData(context, "realName", userInfo.getRealName());
            CrashReport.putUserData(context, "jobNumber", userInfo.getJobNumber());
            CrashReport.putUserData(context, "name", userInfo.getName());
            CrashReport.putUserData(context, "phone", userInfo.getPhone());
            CrashReport.putUserData(context, "deviceId", DeviceInfoUtils.getDeviceId(context));
            CrashReport.putUserData(context, "deviceId1", DeviceUtils.getXyyDeviceUid());
            CrashReport.putUserData(context, "deviceId2", PhoneUtils.getSimOperatorName());
            CrashReport.putUserData(context, "deviceId3", getChannelExtra(c));
            CrashReport.setUserId(userInfo.getSysUserId());
        }
    }


    public static String getChannelExtra(Context c) {
        try {
            ChannelInfo channelInfo = WalleChannelReader.getChannelInfo(c.getApplicationContext());
            if (channelInfo != null) {
                return channelInfo.getExtraInfo().get("crm_extra");
            }
        } catch (Exception ignore) {
        }
        return "empty";
    }

    /**
     * 手动上报异常
     */
    public static void manuallyPostException(String exception) {
        CrashReport.postCatchedException(new Throwable(exception));
    }
}
