package com.ybm100.app.crm.goodsmanagement.activity

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import androidx.core.view.GravityCompat
import androidx.drawerlayout.widget.DrawerLayout
import androidx.fragment.app.Fragment
import com.xyy.common.bar.ImmersionBar
import com.xyy.utilslibrary.base.activity.BaseCompatActivity
import com.ybm100.app.crm.R
import kotlinx.android.synthetic.main.activity_base_drawer.*

abstract class BaseDrawerActivity : BaseCompatActivity() {
    private var mCurrentDrawerFragmentPosition = -1
    private var mDrawerFragments: List<Fragment> = listOf()
    private var bar: ImmersionBar? = null

    final override fun getLayoutId(): Int {
        return R.layout.activity_base_drawer
    }

    override fun initImmersionBar() {
        super.initImmersionBar()
        // 这玩意会导致内存泄漏
        try {
            bar = ImmersionBar.with(this)
                    .titleBar(fl_content_main)
                    .transparentStatusBar()
                    .statusBarDarkFont(true, 0.2f)
                    .statusBarColor(R.color.transparent)
                    .flymeOSStatusBarFontColor("#333333")

            bar?.init()
        } catch (e: Exception) {
        }
    }


    override fun onDestroy() {
        super.onDestroy()
        bar?.destroy()
    }

    override fun initView(savedInstanceState: Bundle?) {
        /**
         * 测滑菜单初始化
         */
        initDrawLayout()
        /**
         * 加载主界面
         */
        val view = LayoutInflater.from(mContext).inflate(getContentMainLayoutID(), null, false)
        fl_content_main.addView(view)

        /**
         * 初始化主界面
         */
        initContentMain()
    }

    private fun initDrawLayout() {
        /**
         * 初始化测滑菜单 Fragments
         */
        mDrawerFragments = getDrawerFragments()

        drawer_layout.apply {
            setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED)
            //TODO 菜单关闭的时候 可以优化
            addDrawerListener(object : DrawerLayout.SimpleDrawerListener() {
                override fun onDrawerOpened(drawerView: View) {
                    super.onDrawerOpened(drawerView)
                    setDrawerLockMode(DrawerLayout.LOCK_MODE_UNLOCKED)
                }

                override fun onDrawerClosed(drawerView: View) {
                    super.onDrawerClosed(drawerView)
                    setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED)
                }
            })
        }
    }

    fun openDrawer(position: Int = 0) {
        if (mDrawerFragments.isEmpty()) {
            return
        }

        val transaction = supportFragmentManager.beginTransaction()

        val fragmentListSize = mDrawerFragments.size

        transaction.hide(mDrawerFragments[fragmentListSize - 1 - position])

        if (mDrawerFragments[position].isAdded) {
            transaction.show(mDrawerFragments[position]).commitNowAllowingStateLoss()
        } else {
            transaction.add(R.id.drawer_fragment_container, mDrawerFragments[position]).show(mDrawerFragments[position]).commitNowAllowingStateLoss()
        }

        drawer_layout.openDrawer(GravityCompat.END)
    }

    fun closeDrawer() {
        drawer_layout.closeDrawer(GravityCompat.END)
    }

    abstract fun getContentMainLayoutID(): Int

    abstract fun initContentMain()

    abstract fun getDrawerFragments(): List<Fragment>
}