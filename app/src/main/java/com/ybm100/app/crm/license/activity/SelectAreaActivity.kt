package com.ybm100.app.crm.license.activity

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import android.view.ViewGroup
import com.chad.library.adapter.base.BaseQuickAdapter
import com.xyy.common.util.ToastUtils
import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.activity.BaseMVPCompatActivity
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.ybm100.app.crm.R
import com.ybm100.app.crm.bean.license.DeliveryAddressBean
import com.ybm100.app.crm.contract.license.LicenseSelectAreaContract
import com.ybm100.app.crm.license.adapter.SelectAreaGroupAdapter
import com.ybm100.app.crm.license.adapter.SelectAreaListAdapter
import com.ybm100.app.crm.license.fragment.SelectAreaListFragment
import com.ybm100.app.crm.presenter.license.SelectAreaPresenter
import kotlinx.android.synthetic.main.activity_select_area.*

/**
 * @author: zcj
 * @time:2020/7/1. Description:
 */
class SelectAreaActivity : BaseMVPCompatActivity<SelectAreaPresenter>(), LicenseSelectAreaContract.ILicenseSelectAreaView, SelectAreaListAdapter.ActionListener {
    override fun initPresenter(): BasePresenter<*, *> = SelectAreaPresenter()
    override fun getLayoutId(): Int = R.layout.activity_select_area
    override fun showNetError() {
        loading = false
    }

    private var selectAreaGroupAdapter: SelectAreaGroupAdapter? = null
    private var provinceBean: DeliveryAddressBean? = null
    private var cityBean: DeliveryAddressBean? = null
    private var areaBean: DeliveryAddressBean? = null
    private var streetBean: DeliveryAddressBean? = null
    override fun initView(savedInstanceState: Bundle?) {
        mPresenter!!.getAddress(null)
        iv_close.setOnClickListener { finish() }
        rv_group.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(this, androidx.recyclerview.widget.LinearLayoutManager.HORIZONTAL, false)
        selectAreaGroupAdapter = SelectAreaGroupAdapter()
        window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
    }

    override fun getAddressSuccess(requestBaseBean: RequestBaseBean<List<DeliveryAddressBean>?>?) {
        loading = false
        //data为空数组时，认为选择到了最后一级，直接返回
        if (requestBaseBean == null || requestBaseBean.data == null) {
            ToastUtils.showShort("网络异常，请重试")
            return
        }
        if (requestBaseBean.data.isNullOrEmpty()) {
            setResultAndBack()
            return
        }
        //设置默认值
        val deliveryAddressBean = DeliveryAddressBean()
        deliveryAddressBean.areaName = "请选择"
        selectAreaGroupAdapter?.addData(deliveryAddressBean)
        //设置adapter
        rv_group.adapter = selectAreaGroupAdapter
        selectAreaGroupAdapter?.onItemClickListener = BaseQuickAdapter.OnItemClickListener { adapter, _, position ->
            val count = adapter.itemCount
            if (position == count - 1) return@OnItemClickListener
            val manger = supportFragmentManager
            for (i in count - 1 downTo position + 1) {
                adapter.data.removeAt(i)
                manger.popBackStack(selectAreaGroupAdapter?.getItem(i)?.areaCode, 0)
            }
            adapter.notifyDataSetChanged()
            refreshAllList()
        }
        //创建列表
        val deliveryAddressListBean = requestBaseBean.data ?: emptyList()
        createFragment(deliveryAddressListBean)
    }

    private fun refreshAllList() {
        val fragments = supportFragmentManager.fragments
        for (fragment in fragments) {
            (fragment as SelectAreaListFragment).refreshData()
        }
    }

    /**
     * 创建列表fragment
     */
    private fun createFragment(bean: List<DeliveryAddressBean>) {
        val fragment = SelectAreaListFragment()
        fragment.setData(bean, this)
        val id = bean[0].id
        val fragmentManager = supportFragmentManager
        val transaction = fragmentManager.beginTransaction()
        transaction.add(R.id.fl_content, fragment, id).addToBackStack(id).commitAllowingStateLoss()
    }

    private var loading = false
    /**
     * 省市区街道点击事件
     */
    override fun onItemClick(item: DeliveryAddressBean) {
        when (item.level) {
            "1" -> {
                provinceBean = item
                selectAreaGroupAdapter?.setData(0, item)
                if (!loading) {
                    loading = true
                    mPresenter?.getAddress(item.areaCode)
                }
            }
            "2" -> {
                cityBean = item
                selectAreaGroupAdapter?.setData(1, item)
                if (!loading) {
                    loading = true
                    mPresenter?.getAddress(item.areaCode)
                }
            }
            "3" -> {
                areaBean = item
                selectAreaGroupAdapter?.setData(2, item)
                if (!loading) {
                    loading = true
                    mPresenter?.getAddress(item.areaCode)
                }
            }
            "4" -> {
                streetBean = item
                selectAreaGroupAdapter?.setData(3, item)
                setResultAndBack()
                //返回结果
            }
            else -> {
                ToastUtils.showLong("操作异常")
            }
        }
    }

    private fun setResultAndBack() {
        val intent = Intent()
        intent.putExtra("provinceBean", provinceBean)
        intent.putExtra("cityBean", cityBean)
        intent.putExtra("areaBean", areaBean)
        intent.putExtra("streetBean", streetBean)
        setResult(Activity.RESULT_OK, intent)
        finish()
    }

    override fun onBackPressedSupport() {

    }

    companion object {
        var INTENT_REQUEST = 10000
        fun startActivity(context: Activity) {
            if (context != null) {
                val intent = Intent(context, SelectAreaActivity::class.java)
                context.startActivityForResult(intent, INTENT_REQUEST)
                context.overridePendingTransition(R.anim.abc_slide_in_bottom, R.anim.abc_slide_out_bottom)
            }
        }
    }

}