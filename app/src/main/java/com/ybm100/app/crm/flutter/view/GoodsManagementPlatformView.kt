package com.ybm100.app.crm.flutter.view

import android.content.Context
import android.content.ContextWrapper
import android.graphics.Color
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.fragment.app.FragmentActivity
import com.ybm100.app.crm.R
import com.ybm100.app.crm.constant.Constants
import io.flutter.plugin.platform.PlatformView

class GoodsManagementPlatformView(
    val context: Context,
    id: Int,
    creationParams: Map<String?, Any?>?
) :
    PlatformView {
    private var containerLayout: FrameLayout?
    private var merchantId: String? = null
    private var flutterView: View? = null

    override fun getView(): View {
        return containerLayout!!
    }

    override fun dispose() {}

    init {
        merchantId = creationParams?.get("merchantId") as String?
        containerLayout = FrameLayout(context).also {
            it.setBackgroundColor(Color.WHITE)
        }
    }

    private fun getActivity(context: Context?): FragmentActivity? {
        var viewContext = context
        while (viewContext is ContextWrapper) {
            if (viewContext is FragmentActivity) {
                return viewContext
            }
            viewContext = viewContext.baseContext
        }
        return null
    }



    override fun onFlutterViewAttached(flutterView: View) {
        this.flutterView = flutterView
        containerLayout?.addView(GoodsManagementContentView(
            merchantId ?: "",
            Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS,
            "",
            "",
            flutterView.context
        ).also {
            it.layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
        })
        flutterView.viewTreeObserver.addOnGlobalLayoutListener {
            detectTargetView("onFlutterViewAttached")
        }
    }


    fun detectTargetView(flag: String) {
        val targetView =
            getActivity(flutterView?.context)?.findViewById<View>(R.id.fl_fragment_container);
        Log.e(
            "guan",
            "${flag} activity:${getActivity(flutterView?.context)},targetView:${targetView}"
        )
    }


    override fun onFlutterViewDetached() {

    }
}