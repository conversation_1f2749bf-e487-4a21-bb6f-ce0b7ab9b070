package com.ybm100.app.crm.model.drugstore.minedrug;

import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiDrugstoreInfo;
import com.ybm100.app.crm.api.ApiDrugstoreService;
import com.ybm100.app.crm.bean.drugstore.BaseInfo;
import com.ybm100.app.crm.contract.drugstore.minedrug.BaseInfoContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;
import com.ybm100.app.crm.task.bean.TaskAndMerchantBean;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/20/2018 20:04
 */
public class BaseInfoModel extends BaseModel implements BaseInfoContract.IBaseInfoModel {

    public static BaseInfoModel newInstance() {
        return new BaseInfoModel();
    }

    @Override
    public Observable<RequestBaseBean<BaseInfo>> getBaseInfo(String merchantId, int isMerchantId) {
        return RetrofitCreateHelper.createApi(ApiDrugstoreInfo.class).getBasicDetail(merchantId, isMerchantId)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean<TaskAndMerchantBean>> toAddVisit(String merchantId, String customerType) {
        return RetrofitCreateHelper.createApi(ApiDrugstoreService.class).toAddVisit(merchantId, customerType)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean> distributeToBD(String bindUserId, String customerId, String skuCollectCodes) {
        return RetrofitCreateHelper.createApi(ApiDrugstoreService.class).distributeToBD(bindUserId, customerId, skuCollectCodes)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean> releaseToPublic(String customerId, String skuCollectCodes) {
        return RetrofitCreateHelper.createApi(ApiDrugstoreService.class).releaseToPublic(customerId, skuCollectCodes)
                .compose(RxHelper.rxSchedulerHelper());
    }
}