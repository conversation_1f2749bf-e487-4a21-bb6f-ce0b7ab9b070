package com.ybm100.app.crm.goodsmanagement.model

import com.xyy.utilslibrary.base.BaseModel
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.helper.RxHelper
import com.ybm100.app.crm.api.ApiService
import com.ybm100.app.crm.goodsmanagement.bean.AvailableZone
import com.ybm100.app.crm.goodsmanagement.bean.GoodsManagementAreaListBean
import com.ybm100.app.crm.goodsmanagement.contract.GoodsManagementAreaContract
import com.ybm100.app.crm.net.RetrofitCreateHelper
import io.reactivex.Observable
import java.util.HashMap

class GoodsManagementAreaModel : BaseModel(), GoodsManagementAreaContract.IGoodsManagementAreaModel {

    override fun getGoodsManagementAreaList(): Observable<RequestBaseBean<GoodsManagementAreaListBean?>?> {
        return RetrofitCreateHelper.createApi(ApiService::class.java).goodsManagementAreaList
                .compose(RxHelper.rxSchedulerHelper<RequestBaseBean<GoodsManagementAreaListBean?>?>())
    }

    override fun getAvailableZone(queryMap: Map<String, String>): Observable<RequestBaseBean<AvailableZone?>?> {
        return RetrofitCreateHelper.createApi(ApiService::class.java).getAvailableZone(queryMap as HashMap<String, String>?)
                .compose(RxHelper.rxSchedulerHelper<RequestBaseBean<AvailableZone?>?>())
    }
}