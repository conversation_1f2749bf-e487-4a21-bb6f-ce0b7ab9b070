package com.ybm100.app.crm.contract.lbs;


import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;

import java.util.HashMap;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/22/2018 16:53
 */
public interface LBSSelectContract {

    interface ILBSSelectModel extends IBaseModel {
        /**
         * 线索修改地图
         * address	    地址	string
         * lat	        地图坐标-经度	string
         * lon	        地图坐标-纬度	string
         * merchantId	药店id	number
         * sysUserId	系统销售人员标号	number
         */
        Observable<RequestBaseBean> addMerchantMap(HashMap<String, String> map);

        /**
         * 非线索修改地图
         * address	    地址	string
         * lat	        地图坐标-经度	string
         * lon	        地图坐标-纬度	string
         * merchantId	药店id	number
         */
        Observable<RequestBaseBean> openSeaEdit(HashMap<String, String> map);
    }

    interface ILBSSelectView extends IBaseActivity {
        void addMerchantMapSuccess(RequestBaseBean baseBean);

        void openSeaEditSuccess(RequestBaseBean baseBean);
    }

}
