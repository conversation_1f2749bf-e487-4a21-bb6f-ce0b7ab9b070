package com.ybm100.app.crm.utils.module.module

import android.content.Context
import com.ybm100.app.crm.R
import com.ybm100.app.crm.utils.module.presenter.BaseModulePresenter

class EmptyModule(context: Context) : BaseModule<BaseModulePresenter<Any>>(context) {
    override fun getContentLayoutId(): Int {
        return R.layout.module_empty
    }

    override fun onInit() {
    }

    override fun onRefresh() {
    }

    override fun getPresenter(): Class<BaseModulePresenter<Any>>? {
        return null
    }

}