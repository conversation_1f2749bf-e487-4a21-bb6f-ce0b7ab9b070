package com.ybm100.app.crm.flutter.channel

import androidx.fragment.app.FragmentActivity
import com.xyy.flutter.container.container.bridge.BaseHandler
import com.xyy.utilslibrary.dialog.JYDialog
import com.ybm100.app.crm.R
import com.ybm100.app.crm.permission.PermissionUtil
import com.ybm100.app.crm.ui.dialog.MapDialogManager
import com.ybm100.app.crm.utils.MapUtils

class MapNavigationHandler : BaseHandler() {

    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {
        val latitude = params["latitude"] as Double?
        val longitude = params["longitude"] as Double?
        val address = params["address"] as String?
        if (latitude == null || longitude == null) {
            error("经纬度为空","经纬度为空")
        }
        val showBaidu = MapUtils.isInstalled(activity, MapUtils.baiduPackageName)
        val showGaode = MapUtils.isInstalled(activity, MapUtils.gaodePackageName)
        val showTencent = MapUtils.isInstalled(activity, MapUtils.tencentPackageName)
        if (!showBaidu && !showGaode && !showTencent) {
            val dialog = JYDialog(activity, null, true)
            dialog.setContent("请检查是否已安装“百度地图、腾讯地图、高德地图”，若已安装请前往“设置 -> 权限管理”开启该应用的“读取已安装应用列表”权限，便可使用导航功能！")
            dialog.setTitleIsVisible(false)
            dialog.setRightButtonTextColor(
                activity.resources.getColor(R.color.text_color_E02E24)
            )
            dialog.setRightButtonTextStyle()
            dialog.setRightText("去授权") {
                PermissionUtil.openAppSettingDetail(activity)
                dialog.dismiss()
            }
            dialog.setLeftText("确定") { dialog.dismiss() }
            dialog.show()
            return
        }
        MapDialogManager.show(activity, showBaidu, showGaode, showTencent,
            MapDialogManager.OnClickListener { v ->
                if (activity.isFinishing || activity.isDestroyed) return@OnClickListener
                when (v.id) {
                    R.id.tv_map_qq -> MapUtils.goToTencentMap(
                        activity,
                        latitude!!,
                        longitude!!,
                        address
                    )
                    R.id.tv_map_baidu -> MapUtils.goToBaiduMap(
                        activity,
                        latitude!!,
                        longitude!!,
                        address
                    )
                    R.id.tv_map_gaod -> MapUtils.goToGaodeMap(
                        activity,
                        latitude!!,
                        longitude!!,
                        address
                    )
                }
            })
        success("")
    }


}
