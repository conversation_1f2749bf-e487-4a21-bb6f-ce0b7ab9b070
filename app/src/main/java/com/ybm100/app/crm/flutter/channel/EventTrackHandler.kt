package com.ybm100.app.crm.flutter.channel

import androidx.fragment.app.FragmentActivity
import com.xyy.flutter.container.container.bridge.BaseHandler
import com.xyy.userbehaviortracking.utils.UserBehaviorTrackingUtils

class EventTrackHandler : BaseHandler() {

    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {
        val actionType = (params["action_type"] ?: "") as String
        val extras = HashMap<String, String>().also { map ->
            map.putAll(params.mapValues {
                it.value?.toString() ?: ""
            })
        }
        extras.remove("action_type")
        if (actionType.isEmpty()) {
            return
        }
        UserBehaviorTrackingUtils.track(actionType, extras)
    }

}
