package com.ybm100.app.crm.ui.activity

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import com.baidu.location.BDAbstractLocationListener
import com.baidu.location.BDLocation
import com.baidu.location.LocationClient
import com.baidu.location.LocationClientOption
import com.tbruyelle.rxpermissions2.RxPermissions
import com.xyy.canary.AppUpdate
import com.xyy.common.navigationbar.AbsNavigationBar
import com.xyy.common.navigationbar.DefaultNavigationBar
import com.xyy.common.util.AppUtils
import com.xyy.common.util.ToastUtils
import com.xyy.flutter.container.container.ContainerRuntime
import com.xyy.utilslibrary.AppManager
import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.activity.BaseMVPCompatActivity
import com.xyy.utilslibrary.dialog.JYDialog
import com.xyy.utilslibrary.utils.ColorUtils
import com.ybm100.app.crm.R
import com.ybm100.app.crm.bean.update.VersionInfo
import com.ybm100.app.crm.constant.AppNetConfig
import com.ybm100.app.crm.permission.PermissionUtil
import com.ybm100.app.crm.platform.RuntimeEnv.env
import com.ybm100.app.crm.presenter.MineAndSettingPresenter
import com.ybm100.app.crm.ui.activity.lbs.LocationManager
import com.ybm100.app.crm.ui.activity.login.LoginActivity
import com.ybm100.app.crm.ui.activity.personal.AboutActivity
import com.ybm100.app.crm.utils.SharedPrefManager
import kotlinx.android.synthetic.main.activity_setting.*
import ly.count.android.sdk.XyyApmCly

class SettingActivity : BaseMVPCompatActivity<MineAndSettingPresenter>(), View.OnClickListener, LocationManager.LocationListener, MineAndSettingPresenter.Setting {

    override fun initHead(): AbsNavigationBar<*> {
        return DefaultNavigationBar.Builder(this).setTitle("设置").builder()
    }

    override fun onCheckForUpdateSuccess(versionInfo: VersionInfo?) {
        if (versionInfo?.isUpgrade == true) { // 需要升级
            checkUpdate()
        } else {
            ToastUtils.showLongSafe("您当前已为最新版本")
        }
    }

    @SuppressLint("CheckResult")
    private fun checkUpdate() {
        RxPermissions(this).requestEach(Manifest.permission.ACCESS_FINE_LOCATION).subscribe { permission ->
            if (permission.granted) {
                //声明LocationClient类实例并配置定位参数
                val locationOption = LocationClientOption()
                //可选，默认高精度，设置定位模式，高精度，低功耗，仅设备
                locationOption.locationMode = LocationClientOption.LocationMode.Hight_Accuracy
                //可选，默认gcj02，设置返回的定位结果坐标系，如果配合百度地图使用，建议设置为bd09ll;
                locationOption.setCoorType("bd09ll")
                //可选，默认0，即仅定位一次，设置发起连续定位请求的间隔需要大于等于1000ms才是有效的
                //locationOption.setScanSpan(1000);
                //可选，设置是否需要地址信息，默认不需要
                locationOption.setIsNeedAddress(true)
                //可选，设置是否需要地址描述
                locationOption.setIsNeedLocationDescribe(false)
                //可选，设置是否需要设备方向结果
                locationOption.setNeedDeviceDirect(false)
                //可选，默认false，设置是否当gps有效时按照1S1次频率输出GPS结果
                locationOption.isLocationNotify = false
                //可选，默认true，定位SDK内部是一个SERVICE，并放到了独立进程，设置是否在stop的时候杀死这个进程，默认不杀死
                locationOption.setIgnoreKillProcess(true)
                //可选，默认false，设置是否需要位置语义化结果，可以在BDLocation.getLocationDescribe里得到，结果类似于“在北京天安门附近”
                locationOption.setIsNeedLocationDescribe(false)
                //可选，默认false，设置是否需要POI结果，可以在BDLocation.getPoiList里得到
                locationOption.setIsNeedLocationPoiList(false)
                //可选，默认false，设置是否收集CRASH信息，默认收集
                locationOption.SetIgnoreCacheException(false)
                //可选，默认false，设置是否开启Gps定位
                locationOption.isOpenGps = true
                //可选，默认false，设置定位时是否需要海拔信息，默认不需要，除基础定位版本都可用
                locationOption.setIsNeedAltitude(false)
                //设置打开自动回调位置模式，该开关打开后，期间只要定位SDK检测到位置变化就会主动回调给开发者
                locationOption.setOpenAutoNotifyMode(3000, 1, LocationClientOption.LOC_SENSITIVITY_HIGHT)

                //定位服务的客户端。宿主程序在客户端声明此类，并调用，目前只支持在主线程中启动
                val locationClient = LocationClient(applicationContext)
                locationClient.locOption = locationOption
                //注册监听函数
                locationClient.registerLocationListener(object : BDAbstractLocationListener() {
                    override fun onReceiveLocation(bdLocation: BDLocation?) {
                        var lat = ""
                        var lng = ""
                        if (null != bdLocation) {
                            lat = bdLocation.latitude.toString()
                            lng = bdLocation.longitude.toString()
                        }
                        XyyApmCly.getInstance().updateLocation(bdLocation?.country
                                ?: "", bdLocation?.city ?: "", lat, lng)
                        locationClient.stop()
                        AppUpdate.getInstance()
                                .setAppUserId(SharedPrefManager.getInstance().userInfo.sysUserId)
                                .setAppUserToken(SharedPrefManager.getInstance().userInfo.token)
                                .start(lat, lng, false)
                    }
                })

                //开始定位
                locationClient.start()
            } else if (permission.shouldShowRequestPermissionRationale) {
                ToastUtils.showShort(resources.getString(R.string.please_open_location_permission))
            } else {
                PermissionUtil.showPermissionDialog(this, resources.getString(R.string.location_permission_name), true, null)
            }
        }
    }

    override fun onCheckForUpdateFail() {

    }

    override fun initPresenter(): BasePresenter<*, *> {
        return MineAndSettingPresenter()
    }

    override fun showNetError() {

    }

    override fun getLayoutId(): Int {
        return R.layout.activity_setting
    }

    override fun initView(savedInstanceState: Bundle?) {
        cl_change_password.setOnClickListener(this)
        cl_about_us.setOnClickListener(this)
        cl_check_update.setOnClickListener(this)
        rtv_logout.setOnClickListener(this)

        tv_version?.text = "版本号 v${AppUtils.getAppVersionName()}"
    }

    override fun onReceiveLocation(bd: BDLocation?) {
        val lat = bd?.latitude.toString()
        val lng = bd?.longitude.toString()

        AppUpdate.getInstance()
                .setAppUserId(SharedPrefManager.getInstance().userInfo.sysUserId)
                .setAppUserToken(SharedPrefManager.getInstance().userInfo.token)
                .start(lat, lng, true)

        LocationManager.getInstance().unRegisterLocationListener(this)
    }


    override fun onDestroy() {
        super.onDestroy()
        LocationManager.getInstance().unRegisterLocationListener(this)
    }

    private fun isAppProd(): Boolean {
        var currFlavor = SharedPrefManager.getInstance().currFlavor
        if (TextUtils.isEmpty(currFlavor) || AppNetConfig.FlavorType.CUSTOM == currFlavor) {
            currFlavor = env
        }
        return AppNetConfig.FlavorType.PROD.equals(currFlavor, ignoreCase = true)
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.cl_change_password -> {
                // startNewActivity(TempActivity.class);
                /*val mPwdIntent = FlutterActivity.CachedEngineIntentBuilder(FlutterRunnerActivity::class.java, "engine")
                        .build(this)
                mPwdIntent.putExtra("uri_path", "/change_password")
                startActivity(mPwdIntent)*/

//                ContainerRuntime.getFlutterRouter().open(this, "/change_password")
                var url = "https://ybs-support.test.ybm100.com/#/app/moditifyPass"
                if (isAppProd()) {
                    url = "https://ybs-support.ybm100.com/#/app/moditifyPass"
                }
                ContainerRuntime.getFlutterRouter().open(this, "xyy://crm-app.ybm100.com/crm/web_view?url=${Uri.encode(url)}");
            }
            R.id.cl_about_us -> {
                startActivity(Intent(this, AboutActivity::class.java))
            }
            R.id.cl_check_update -> {
                // mPresenter.checkForUpdate()
                // 采用新的升级
                LocationManager.getInstance().locationPermissions(this, this, true, PermissionUtil.OnCancelCallBack { ToastUtils.showShort("应用没有位置权限") })
            }
            R.id.rtv_logout -> {
                val jyDialog = JYDialog(mContext, null, true)
                jyDialog.setContent("确认退出登录吗？")
                jyDialog.setTitleIsVisible(false)
                jyDialog.setRightButtonTextColor(ColorUtils.getColor(mContext, R.color.color_03A9F4))
                jyDialog.setRightButtonTextStyle()
                jyDialog.setLeftText("取消") { arg0: View? -> jyDialog.dismiss() }
                jyDialog.setRightText("确定") { v: View? ->
                val sharedPreferences = getSharedPreferences("douya_shop", Context.MODE_PRIVATE)
                sharedPreferences.edit().clear().apply()
                    jyDialog.dismiss()
                    
                    startNewActivity(LoginActivity::class.java)
                    AppManager.getAppManager().finishAllActivity()
                    finish()
                }.show()
            }
        }
    }
}