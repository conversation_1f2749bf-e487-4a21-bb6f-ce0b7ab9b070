package com.ybm100.app.crm.flutter.channel

import androidx.fragment.app.FragmentActivity
import com.xyy.common.util.FileUtils
import com.xyy.flutter.container.container.bridge.callback.UploadCallback
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.xyy.utilslibrary.helper.RxHelper
import com.xyy.utilslibrary.utils.BitmapUtils
import com.ybm100.app.crm.flutter.CustomFlutterActivity
import com.ybm100.app.crm.flutter.ErrorCode
import com.ybm100.app.crm.flutter.net.FlutterConverterFactory
import com.ybm100.app.crm.flutter.net.FlutterProxyConsumer
import com.ybm100.app.crm.flutter.net.FlutterService
import com.ybm100.app.crm.net.BaseUrl
import com.ybm100.app.crm.net.RetrofitCreateHelper
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer
import com.ybm100.app.crm.utils.GsonUtils
import okhttp3.MediaType
import okhttp3.MultipartBody
import okhttp3.RequestBody
import java.io.File
import java.net.URLEncoder
import java.util.*

class UploadPhotoHandler {

    var callback: UploadCallback? = null

    fun handle(
        activity: FragmentActivity,
        uploadUrl: String,
        localPaths: List<String>,
        limitWidth: Int,
        limitHeight: Int,
        callback: UploadCallback,
        extraParams: Map<String, Any?>,
        isUploadOrigin: Boolean
    ) {
        this.callback = callback
        //检查参数
        if (uploadUrl.isBlank()) {
            error(ErrorCode.INVALID_URL)
            return
        }
        if (localPaths.isEmpty()) {
            error(ErrorCode.PHOTO_LIST_EMPTY)
            return
        }
        val linkedList = LinkedList(localPaths)
        val list = arrayListOf<String>()
        uploadFile(activity, uploadUrl, linkedList, limitWidth, limitHeight, list, extraParams, isUploadOrigin)
    }

    private fun uploadFile(
            activity: FragmentActivity,
            uploadUrl: String,
            linkedList: LinkedList<String>,
            limitWidth: Int,
            limitHeight: Int,
            list: ArrayList<String>,
            extraParams: Map<String, Any?>,
            isUploadOrigin: Boolean
    ) {
        if (linkedList.isEmpty()) {
            callback?.success(list)
            return
        }
        val filePath = linkedList.removeFirst()
        val file = File(filePath)
        if (file.exists()) {
            val tempFile: File = if (isUploadOrigin) {
                val pathArr = filePath.split("/")
                val fileName = pathArr[pathArr.size - 1]
                val tempFilePath = filePath.replace(fileName, "temp$fileName")
                val tFile = File(tempFilePath)
                FileUtils.copyFile(file, tFile)
                tFile
            } else file
            val pathList = BitmapUtils.compress(tempFile, limitWidth * limitHeight)
            if (pathList.isNullOrEmpty()) {
                error(ErrorCode.PHOTO_COMPRESS_FAIL)
                return
            }
            //构建body
            val builder =
                    MultipartBody.Builder().setType(MultipartBody.FORM)
            extraParams.forEach {
                if (it.key != "isUploadOrigin") {
                    builder.addFormDataPart(it.key, it.value?.toString() ?: "")
                }
            }
            builder.addFormDataPart(
                    "file",
                    URLEncoder.encode(file.name, "UTF-8"),
                    RequestBody.create(MediaType.parse("image/*"), file)
            )
            val body = builder.build()
            if (activity is CustomFlutterActivity) {
                val subscribe = RetrofitCreateHelper.createApi(FlutterService::class.java,
                        BaseUrl.getBaseUrl(), FlutterConverterFactory.create())
                        .requestMultipartPostAsk(uploadUrl, body)
                        .compose(RxHelper.rxSchedulerHelper())
                        .subscribe(object : FlutterProxyConsumer(activity) {
                            override fun onSuccess(result: String?) {
                                GsonUtils.toMap<Any?>(result)?.let { resultMap ->
                                    var key = ""
                                    if (resultMap["data"] != null) {
                                        key = "data"
                                    } else if (resultMap["fileName"] != null) {
                                        key = "fileName"
                                    }
                                    resultMap[key].let { data ->
                                        if (data is List<*>) {
                                            val url: String = data[0] as String
                                            list.add(data[0] as String)
                                            uploadFile(activity,
                                                    uploadUrl,
                                                    linkedList,
                                                    limitWidth,
                                                    limitHeight,
                                                    list, extraParams, isUploadOrigin)
                                        }
                                    }
                                }
                            }

                            override fun onFailure(baseBean: RequestBaseBean<*>?) {

                            }
                        }, object : SimpleErrorConsumer(activity) {
                            override fun onError(throwable: Throwable?, msg: String?) {
                                callback?.error(
                                        ErrorCode.REQUEST_ERROR.errorCode, msg
                                        ?: ErrorCode.REQUEST_ERROR.errorMsg
                                )
                            }
                        })
                activity.mRxManager.register(subscribe)
            } else {
                error(ErrorCode.NO_EXIST)
                return
            }
        }
    }
}