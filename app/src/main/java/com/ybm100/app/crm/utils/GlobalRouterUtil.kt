package com.ybm100.app.crm.utils

import android.content.Context
import android.net.Uri
import android.os.Handler
import android.util.Log
import com.xyy.flutter.container.container.ContainerRuntime
import com.xyy.utilslibrary.AppManager

class GlobalRouterUtil {
    companion object {
        // 主要来自Push等外部协议
        fun openUrl(context: Context, url: String?) {
            url?.let {
                try {
                    var mainPageStarting = false;
                    var targetUrl = it
                    // 如果是H5，则需要追加豆芽定义的h5协议
                    if (it.startsWith("http://") || it.startsWith("https://")) {
                        targetUrl = "xyy://crm-app.ybm100.com/crm/web_view?url=${Uri.encode(it)}"
                    }
                    // 如果app不在前台，需要追加 MainActivity 作为落地页
                    if (AppManager.getAppManager().isAppExit) {
                        ContainerRuntime.router.open(context, "/main", null)
                        mainPageStarting = true
                    } else {
                        // 退出除首页外所有页面
                        AppManager.getAppManager().finishActivityUntil(0)
                    }
                    var delayMillis = 0L
                    if (mainPageStarting) {
                        delayMillis = 1000L
                    }

                    Handler().postDelayed({
                        if (it.contains("/main")) {
                            //跳其他子页面
                            val subPage = Uri.parse(url).getQueryParameter("subPage")
                            openSubPage(context, subPage)
                        } else if (it.contains("/crm/message/addCart")) {
                            // 跳转消息二级页
                            ContainerRuntime.router.open(
                                context,
                                "/message_list_page?messageType=5&messageTitle=%E5%95%86%E5%93%81%E5%8A%A0%E8%B4%AD%E9%80%9A%E7%9F%A5",
                                null
                            )
                        } else {
                            openSubPage(context, targetUrl)
                        }
                    }, delayMillis)
                } catch (ex: Exception) {
                    Log.e("router", "router open failed: ${ex.message}")
                }
            }
        }

        // 应用内隐式协议跳转
        fun openSubPage(context: Context, subPage: String?) {
            subPage?.let {
                var targetUrl = it
                // 如果是H5，则需要追加豆芽定义的h5协议
                if (it.startsWith("http") || it.startsWith("https")) {
                    targetUrl = "xyy://crm-app.ybm100.com/crm/web_view?url=${Uri.encode(it)}"
                }

                try {
                    ContainerRuntime.router.open(context, targetUrl, null)
                } catch (ex: Exception) {
                    Log.e("router", "router open failed: ${ex.message}")
                }
            }
        }


    }
}