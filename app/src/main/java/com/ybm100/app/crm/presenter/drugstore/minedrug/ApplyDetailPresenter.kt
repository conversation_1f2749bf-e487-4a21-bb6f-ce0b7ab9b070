package com.ybm100.app.crm.presenter.drugstore.minedrug

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.ybm100.app.crm.bean.drugstore.minedrugstore.ApplyDetailBean
import com.ybm100.app.crm.contract.drugstore.minedrug.ApplyDetailContract
import com.ybm100.app.crm.model.drugstore.minedrug.ApplyDetailModel
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer

/**
 * Created by XyyMvpYkqTemplate on 07/30/2019 18:07
 */
class ApplyDetailPresenter : BasePresenter<ApplyDetailContract.IApplyDetailModel, ApplyDetailContract.IApplyDetailView>() {

    override fun getModel(): ApplyDetailModel = ApplyDetailModel()

    fun getInvoiceDetail(map: HashMap<String, Any>) {
        mRxManager.register(mIModel.reqInvoiceDetail(map)
                .subscribe(object : SimpleSuccessConsumer<RequestBaseBean<ApplyDetailBean>>(mIView) {
                    override fun onSuccess(t: RequestBaseBean<ApplyDetailBean>?) {
                        if (t != null) {
                            mIView.reqInvoiceDetailSuccess(t.data)
                        }
                    }

                }, object : SimpleErrorConsumer(mIView) {
                    override fun onError(throwable: Throwable?, msg: String?) {
                        super.onError(throwable, msg)
                        mIView.showNetError()
                    }
                }))
    }
}
