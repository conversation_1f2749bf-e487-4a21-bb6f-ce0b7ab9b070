package com.ybm100.app.crm.model.lzcustomer;


import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiDrugstoreService;
import com.ybm100.app.crm.api.LZApiService;
import com.ybm100.app.crm.bean.drugstore.PrivateListFilterBean;
import com.ybm100.app.crm.bean.lzcustomer.LzPrivateListBean;
import com.ybm100.app.crm.contract.lzcustomer.LzPrivateListContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import java.util.HashMap;

import io.reactivex.Observable;

/**
 * <AUTHOR>
 * Created by XyyMvpSportTemplate on 12/20/2018 10:43
 * 我的药店
 */
public class LzPrivateListModel implements LzPrivateListContract.ILzPrivateListModel {

    public static LzPrivateListModel newInstance() {
        return new LzPrivateListModel();
    }

    @Override
    public Observable<RequestBaseBean<LzPrivateListBean>> getPrivateListData(HashMap<String, String> map) {
        return RetrofitCreateHelper.createApi(LZApiService.class).privateSeaList(map).compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean<PrivateListFilterBean>> getFilterItems() {
        return RetrofitCreateHelper.createApi(ApiDrugstoreService.class).getFilterItems()
                .compose(RxHelper.rxSchedulerHelper());
    }
}