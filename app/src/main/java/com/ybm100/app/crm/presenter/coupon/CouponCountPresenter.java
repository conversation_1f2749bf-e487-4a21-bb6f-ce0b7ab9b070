package com.ybm100.app.crm.presenter.coupon;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.coupon.VoucherBean;
import com.ybm100.app.crm.contract.coupon.CouponCountContract;
import com.ybm100.app.crm.model.coupon.CouponCountModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

import java.util.HashMap;
import java.util.List;

/**
 * Created by XyyMvpSportTemplate on 04/16/2019 11:26
 */
public class CouponCountPresenter extends BasePresenter<CouponCountContract.ICouponCountModel, CouponCountContract.ICouponCountView> {
    int pageSize = 10;
    int pageNo = 0;

    public static CouponCountPresenter newInstance() {
        return new CouponCountPresenter();
    }

    @Override
    protected CouponCountModel getModel() {
        return CouponCountModel.newInstance();
    }

    public void getVoucherData(final boolean refresh, String shopId, String type) {
        if (mIView == null || mIModel == null) return;
        if (refresh) {
            pageNo = 0;
        }
        HashMap<String, String> map = new HashMap<>();
        map.put("offset", String.valueOf(pageNo));
        map.put("limit", String.valueOf(pageSize));
        map.put("shopId", shopId);
        map.put("type", String.valueOf(type));
        mRxManager.register(mIModel.getVoucherData(map).subscribe(new SimpleSuccessConsumer<RequestBaseBean<VoucherBean>>(mIView) {
            @Override
            public void onSuccess(RequestBaseBean<VoucherBean> voucherBean) {
                if (mIView == null) return;
                List<VoucherBean.VoucherListBean.RowsBean> result = voucherBean.getData().getList().getRows();
                if (result != null) {
                    if (voucherBean.getData().getList().isLastPage()) {
                        mIView.loadMoreComplete();//超出一页没有更多的数据
                    }
                    mIView.getVoucherDataSuccess(refresh, voucherBean.getData());
                }else{
                    mIView.showEmpty();
                }
                pageNo++;
            }
        }, new SimpleErrorConsumer(mIView)));
    }
}
