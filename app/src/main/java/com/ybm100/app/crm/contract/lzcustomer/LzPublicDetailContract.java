package com.ybm100.app.crm.contract.lzcustomer;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.lzcustomer.LzPublicDetialBean;

import io.reactivex.Observable;

/**
 * <AUTHOR>
 * @date 2019/1/7
 */
public interface LzPublicDetailContract {

    interface ILzPublicDetailModel extends IBaseModel {
        //我的药店列表
        Observable<RequestBaseBean<LzPublicDetialBean>> searchOpenSeaDetail(String id);

        //认领
        Observable<RequestBaseBean> receive(String id);
    }

    interface ILzPublicDetailView extends IBaseActivity {

        void searchOpenSeaDetailSuccess(LzPublicDetialBean baseBean);

        void receiveSuccess(RequestBaseBean requestBaseBean);
    }
}
