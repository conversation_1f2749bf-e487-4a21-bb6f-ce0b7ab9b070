package com.ybm100.app.crm.presenter.message;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.message.SessionMSGBean;
import com.ybm100.app.crm.contract.message.MessageContract;
import com.ybm100.app.crm.model.message.MessageModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

/**
 * <AUTHOR>
 * @version 1.0
 * @file MessagePresenter2.java
 * @brief
 * @date 2018/12/22
 * Copyright (c) 2018, 北京小药药
 * All rights reserved.
 */
public class MSGSessionPresenter extends BasePresenter<MessageContract.ISessionMessageModel, MessageContract.ISessionMessageView> {
    @Override
    protected MessageContract.ISessionMessageModel getModel() {
        return new MessageModel();
    }

    public static MSGSessionPresenter newInstance() {
        return new MSGSessionPresenter();
    }

    public void getSessionMSGList(int pageNo, int limit) {
        mRxManager.register(mIModel.getSessionMSGList(pageNo, limit)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean<SessionMSGBean>>(mIView) {
                    @Override
                    public void onSuccess(RequestBaseBean<SessionMSGBean> sessionMSGBeanRequestBaseBean) {
                        mIView.onSessionMSGListBack(pageNo, sessionMSGBeanRequestBaseBean);
                    }
                }, new SimpleErrorConsumer(mIView)));
    }
}
