package com.ybm100.app.crm.contract.share;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.share.BranchBean;
import java.util.List;

import io.reactivex.Observable;

/**
 * <AUTHOR>
 * @date 2019/3/29
 */
public class ShareRootContract {

    public interface IShareRootModel extends IBaseModel {
        Observable<RequestBaseBean<List<BranchBean>>> getTopGroups();
    }

    public interface IShareRootView extends IBaseActivity {
        void getTopGroupsSuccess(RequestBaseBean<List<BranchBean>> baseBean);
    }
}
