package com.ybm100.app.crm.flutter.channel

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.fragment.app.FragmentActivity
import com.xyy.flutter.container.container.bridge.BaseHandler

class OpenNotificationSettingHandler : BaseHandler() {

    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {
        goToNotificationSet(activity)
        result?.success("")
    }


    /**
     * 跳转到通知设置页面
     *
     * @param context
     */
    private fun goToNotificationSet(context: Context?) {
        if (null == context) {
            return
        }
        val intent = Intent()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            intent.action = "android.settings.APP_NOTIFICATION_SETTINGS"
            intent.putExtra("android.provider.extra.APP_PACKAGE", context.packageName)
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            intent.action = "android.settings.APP_NOTIFICATION_SETTINGS"
            intent.putExtra("app_package", context.packageName)
            intent.putExtra("app_uid", context.applicationInfo.uid)
        } else if (Build.VERSION.SDK_INT === Build.VERSION_CODES.KITKAT) {
            intent.action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
            intent.addCategory(Intent.CATEGORY_DEFAULT)
            intent.data = Uri.parse("package:" + context.packageName)
        }
        if (intent.resolveActivity(context.packageManager) != null) {
            context.startActivity(intent)
        }
    }


}
