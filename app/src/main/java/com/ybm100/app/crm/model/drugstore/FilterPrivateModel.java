package com.ybm100.app.crm.model.drugstore;



import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.ApiDrugstoreService;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.FilterPrivateBean;
import com.ybm100.app.crm.contract.drugstore.FilterPrivateContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;


import io.reactivex.Observable;

/**
 * @author: zcj
 * @time:2020/4/2. Description:
 */
public class FilterPrivateModel implements FilterPrivateContract.IFilterPrivateModel {
    public static FilterPrivateModel newInstance() {
        return new FilterPrivateModel();
    }
    @Override
    public Observable<RequestBaseBean<FilterPrivateBean>> getOtherFilterItems() {
        return RetrofitCreateHelper.createApi(ApiDrugstoreService.class).getOtherFilterItems()
                .compose(RxHelper.rxSchedulerHelper());
    }

}
