package com.ybm100.app.crm.model.hycustomer;

import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.HyApiService;
import com.ybm100.app.crm.bean.hycustomer.HyPublicDetailBean;
import com.ybm100.app.crm.contract.hycustomer.HyPublicDetailContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import io.reactivex.Observable;

/**
 * 荷叶健康公海客户详情
 */

public class HyPublicDetailModel implements HyPublicDetailContract.IHyPublicDetailModel {
    public static HyPublicDetailModel newInstance() {
        return new HyPublicDetailModel();
    }


    @Override
    public Observable<RequestBaseBean<HyPublicDetailBean>> searchOpenSeaDetail(String id) {
        return RetrofitCreateHelper.createApi(HyApiService.class).searchOpenSeaDetail(id)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean> receive(String id) {
        return RetrofitCreateHelper.createApi(HyApiService.class).receive(id)
                .compose(RxHelper.rxSchedulerHelper());
    }
}
