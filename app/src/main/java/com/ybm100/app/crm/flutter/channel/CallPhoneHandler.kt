package com.ybm100.app.crm.flutter.channel

import androidx.fragment.app.FragmentActivity
import com.xyy.flutter.container.container.bridge.BaseHandler
import com.ybm100.app.crm.utils.CallUtil

class CallPhoneHandler : BaseHandler() {
    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {

        val mobile = params["mobile"] as String
        val isOnlyCallPhone = params["isOnlyCallPhone"] as Boolean?
        if (isOnlyCallPhone == true) {
            CallUtil.call(activity, mobile)
        } else {
            val merchantId = params["merchantId"] as String
            val merchantName = params["merchantName"] as String
            val addSource = params["addSource"] as String
            CallUtil.call(activity, mobile, merchantId, merchantName, addSource)
        }
        result?.success("")
    }

}