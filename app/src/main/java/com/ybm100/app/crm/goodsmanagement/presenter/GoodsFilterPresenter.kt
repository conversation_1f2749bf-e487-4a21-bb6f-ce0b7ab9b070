package com.ybm100.app.crm.goodsmanagement.presenter

import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.ybm100.app.crm.goodsmanagement.bean.AvailableZone
import com.ybm100.app.crm.goodsmanagement.contract.GoodsFilterContract
import com.ybm100.app.crm.goodsmanagement.model.GoodsFilterModel
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer

class GoodsFilterPresenter :
    BasePresenter<GoodsFilterContract.IGoodsFilterModel, GoodsFilterContract.IGoodsFilterView>() {

    override fun getModel(): GoodsFilterContract.IGoodsFilterModel {
        return GoodsFilterModel()
    }


    fun getAvailableZone(params: HashMap<String, String>) {
        try {
            mRxManager.register(mIModel.getAvailableZone(params).subscribe(object :
                SimpleSuccessConsumer<RequestBaseBean<AvailableZone?>?>(mIView) {
                override fun onSuccess(t: RequestBaseBean<AvailableZone?>?) {
                    mIView.onGetAvailableZoneSuccess(t)
                }

                override fun onFailure(errorCode: Int) {
                }
            }, object : SimpleErrorConsumer(mIView) {
                override fun onError(throwable: Throwable?, msg: String?) {
                    mIView.onGetAvailableZoneFail()
                }
            }))
        } catch (e: Exception) {

        }
    }
}