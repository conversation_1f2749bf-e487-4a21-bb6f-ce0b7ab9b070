package com.ybm100.app.crm.model.hycustomer;


import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.ybm100.app.crm.api.HyApiService;
import com.ybm100.app.crm.bean.hycustomer.HyPrivateDetailBean;
import com.ybm100.app.crm.contract.hycustomer.HyPrivateDetailContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;
import com.ybm100.app.crm.task.bean.TaskAndMerchantBean;

import io.reactivex.Observable;

/**
 * 荷叶健康私海客户详情model
 */
public class HyPrivateDetailModel extends BaseModel implements HyPrivateDetailContract.IHyPrivateDetailModel {

    public static HyPrivateDetailModel newInstance() {
        return new HyPrivateDetailModel();
    }

    @Override
    public Observable<RequestBaseBean<HyPrivateDetailBean>> getBaseInfo(String merchantId) {
        return RetrofitCreateHelper.createApi(HyApiService.class).getBasicDetail(merchantId)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean<TaskAndMerchantBean>> toAddVisit(String merchantId, String customerType) {
        return RetrofitCreateHelper.createApi(HyApiService.class).toAddVisit(merchantId, customerType)
                .compose(RxHelper.rxSchedulerHelper());
    }

    @Override
    public Observable<RequestBaseBean> distributeToBD(String bindUserId, String customerId) {
        return RetrofitCreateHelper.createApi(HyApiService.class).distributeToBD(bindUserId, customerId)
                .compose(RxHelper.rxSchedulerHelper());
    }
}