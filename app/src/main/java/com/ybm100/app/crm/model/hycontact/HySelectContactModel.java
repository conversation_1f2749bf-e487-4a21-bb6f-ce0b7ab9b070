package com.ybm100.app.crm.model.hycontact;



import com.xyy.utilslibrary.base.BaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.xyy.utilslibrary.helper.RxHelper;
import com.xyy.utilslibrary.helper.datafactory.MyConverterFactory;
import com.ybm100.app.crm.api.HyApiService;
import com.ybm100.app.crm.api.HyBaseUrl;
import com.ybm100.app.crm.bean.schedule.ContactListBean;
import com.ybm100.app.crm.contract.schedule.SelectContactContract;
import com.ybm100.app.crm.net.RetrofitCreateHelper;

import java.util.List;

import io.reactivex.Observable;

/**
 * HySelectContactModel
 */
public class HySelectContactModel extends BaseModel implements SelectContactContract.ISelectContactModel {

    public static HySelectContactModel newInstance() {
        return new HySelectContactModel();
    }

    @Override
    public Observable<RequestBaseBean<List<ContactListBean>>> getContactList(String merchantId) {
        return RetrofitCreateHelper.createApi(HyApiService.class,HyBaseUrl.getBaseUrl(), MyConverterFactory.create()).getContactListBySelect(merchantId)
                .compose(RxHelper.rxSchedulerHelper());
    }
}
