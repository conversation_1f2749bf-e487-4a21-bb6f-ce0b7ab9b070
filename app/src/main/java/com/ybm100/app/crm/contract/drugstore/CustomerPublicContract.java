package com.ybm100.app.crm.contract.drugstore;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.CustomerPublicBean;
import com.ybm100.app.crm.bean.drugstore.minedrugstore.RegisterParams;

import java.util.HashMap;
import java.util.List;

import io.reactivex.Observable;

/**
 * Created by XyyMvpSportTemplate on 12/29/2018 16:48
 * 未认领药店
 */
public interface CustomerPublicContract {

    interface ICustomerPublicModel extends IBaseModel {
        //公海列表
        Observable<RequestBaseBean<CustomerPublicBean>> searchOpenSea(HashMap<String, String> map);

        //认领药店
        Observable<RequestBaseBean> receive(String id,String skuCollectCode);


        Observable<RequestBaseBean<List<RegisterParams>>> getRegisterParams();
    }

    interface ICustomerPublicView extends IBaseActivity {
        void searchOpenSeaSuccess(boolean refresh, RequestBaseBean<CustomerPublicBean> baseBean);

        void receiveSuccess(RequestBaseBean baseBean,String merchantId,String skuCollectCode);

        void getRegisterParamsSuccess(List<RegisterParams> resultList);

        void enableLoadMore(boolean b);

        void loadMoreComplete();

        void showEmpty();
    }

}
