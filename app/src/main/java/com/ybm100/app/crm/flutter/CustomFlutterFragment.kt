package com.ybm100.app.crm.flutter

import com.google.gson.Gson
import com.xyy.flutter.container.container.ContainerRuntime
import com.xyy.flutter.container.container.bridge.MethodCallDispatcher
import com.xyy.flutter.container.container.route.IOpenCallback
import io.flutter.embedding.android.FlutterFragment
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.BasicMessageChannel
import io.flutter.plugin.common.StringCodec

class CustomFlutterFragment : FlutterFragment() {
    var routerChannel: BasicMessageChannel<String>? = null
    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        MethodCallDispatcher.bind(activity, flutterEngine)
        initRouterChannel(flutterEngine)
    }

    private fun initRouterChannel(flutterEngine: FlutterEngine) {
        if (routerChannel == null) {
            routerChannel = BasicMessageChannel<String>(
                flutterEngine.dartExecutor.binaryMessenger,
                "router_channel",
                StringCodec.INSTANCE
            ).also {
                it.setMessageHandler { message, reply ->
                    if (!message.isNullOrEmpty() && activity != null) {
                        ContainerRuntime.getFlutterRouter().open(activity, message, object :
                            IOpenCallback {
                            override fun result(resultData: Map<String, Any?>?) {
                                val toJson = Gson().toJson(resultData)
                                reply.reply(toJson)
                            }
                        })
                    }
                }
            }
        }
    }


    override fun onDestroy() {
        super.onDestroy()
        flutterEngine?.let { engine ->
            engine.dartExecutor.binaryMessenger.let {
                //页面销毁时，需要注销这些已注册的channel，防止引擎携带fragment的实例，从而导致内存泄漏
                it.setMessageHandler("router_channel", null)
                MethodCallDispatcher.unBind(engine)
            }
        }

    }
}