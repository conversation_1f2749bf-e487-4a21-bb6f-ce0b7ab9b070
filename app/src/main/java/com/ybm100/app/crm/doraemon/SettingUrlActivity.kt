package com.ybm100.app.crm.doraemon

import android.os.Bundle
import android.text.TextUtils
import android.view.View
import com.xyy.common.navigationbar.AbsNavigationBar
import com.xyy.common.navigationbar.DefaultEditNavigationBar
import com.xyy.common.util.ToastUtils
import com.xyy.utilslibrary.AppManager
import com.xyy.utilslibrary.base.activity.BaseCompatActivity
import com.ybm100.app.crm.BuildConfig
import com.ybm100.app.crm.R
import com.ybm100.app.crm.constant.AppNetConfig
import com.ybm100.app.crm.ui.activity.login.LoginActivity
import com.ybm100.app.crm.utils.SharedPrefManager
import butterknife.OnClick
import kotlinx.android.synthetic.main.activity_url.*

/**
 * <AUTHOR>
 */
class SettingUrlActivity : BaseCompatActivity() {
    private var currFlavor: String? = null

    override fun getLayoutId(): Int {
        return R.layout.activity_url
    }

    override fun initHead(): AbsNavigationBar<*>? {
        return DefaultEditNavigationBar.Builder(this).setTitle(getString(R.string.setting)).setRightText("确定").setRightClickListener { v ->
            if (TextUtils.isEmpty(et_api.text)) {
                ToastUtils.showShort("请输入正确host")
                return@setRightClickListener
            }
            if (currFlavor == AppNetConfig.FlavorType.CUSTOM) {
                SharedPrefManager.getInstance().customeUrl = et_api.text.toString()
                SharedPrefManager.getInstance().customerH5Url = et_h5_api.text.toString()
                SharedPrefManager.getInstance().customerH5Domain = et_h5_domain.text.toString()
            }
            SharedPrefManager.getInstance().currFlavor = currFlavor
            startActivity(LoginActivity::class.java)
            AppManager.getAppManager().finishAllActivity()
        }.builder()
    }

    override fun initView(savedInstanceState: Bundle?) {
        currFlavor = SharedPrefManager.getInstance().currFlavor
        if (TextUtils.isEmpty(currFlavor)) {
            currFlavor = BuildConfig.FLAVOR
        }
        setViewUI()
        setClickListener()
    }

    private fun setClickListener() {
        //开发
        btn_1.setOnClickListener {
            currFlavor = AppNetConfig.FlavorType.DEV
            setViewUI()
        }
        //测试
        btn_2.setOnClickListener {
            currFlavor = AppNetConfig.FlavorType.BETA
            setViewUI()
        }
        //预发
        btn_3.setOnClickListener {
            currFlavor = AppNetConfig.FlavorType.STAGE
            setViewUI()
        }
        //线上&自定义
        btn_4.setOnClickListener {
            currFlavor = AppNetConfig.FlavorType.CUSTOM
            setViewUI()
        }
    }


    private fun setViewUI() {
        when (currFlavor) {
            AppNetConfig.FlavorType.DEV -> {
                et_api.setText(BuildConfig.API_URL_DEV)
                et_h5_api.setText(BuildConfig.H5_URL_TEST)
                btn_1.setBackgroundResource(R.color.text_color_35C561)
                btn_2.setBackgroundResource(R.color.bg_gray)
                btn_3.setBackgroundResource(R.color.bg_gray)
                btn_4.setBackgroundResource(R.color.bg_gray)
                tips1.visibility = View.GONE
                tips2.visibility = View.GONE
            }
            AppNetConfig.FlavorType.BETA -> {
                et_api.setText(BuildConfig.API_URL_TEST)
                et_h5_api.setText(BuildConfig.H5_URL_TEST)
                btn_1.setBackgroundResource(R.color.bg_gray)
                btn_2.setBackgroundResource(R.color.text_color_35C561)
                btn_3.setBackgroundResource(R.color.bg_gray)
                btn_4.setBackgroundResource(R.color.bg_gray)
                tips1.visibility = View.GONE
                tips2.visibility = View.GONE
            }
            AppNetConfig.FlavorType.STAGE -> {
                et_api.setText(BuildConfig.API_URL_STAGE)
                et_h5_api.setText(BuildConfig.H5_URL_STAGE)
                btn_1.setBackgroundResource(R.color.bg_gray)
                btn_2.setBackgroundResource(R.color.bg_gray)
                btn_3.setBackgroundResource(R.color.text_color_35C561)
                btn_4.setBackgroundResource(R.color.bg_gray)
                tips1.visibility = View.GONE
                tips2.visibility = View.GONE
            }
            AppNetConfig.FlavorType.CUSTOM -> {
                val url = SharedPrefManager.getInstance().customeUrl
                val h5Url = SharedPrefManager.getInstance().customerH5Url
                val domain = SharedPrefManager.getInstance().customerH5Domain
                et_api.setText(if (TextUtils.isEmpty(url)) BuildConfig.API_URL_PROD else url)
                et_h5_api.setText(if (TextUtils.isEmpty(h5Url)) BuildConfig.H5_URL else h5Url)
                et_h5_domain.setText(if (TextUtils.isEmpty(domain)) ".ybm100.com" else domain)
                btn_1.setBackgroundResource(R.color.bg_gray)
                btn_2.setBackgroundResource(R.color.bg_gray)
                btn_3.setBackgroundResource(R.color.bg_gray)
                btn_4.setBackgroundResource(R.color.text_color_35C561)
                tips1.visibility = View.VISIBLE
                tips2.visibility = View.VISIBLE
            }
            else -> {
            }
        }
    }
}
