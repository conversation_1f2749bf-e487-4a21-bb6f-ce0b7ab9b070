package com.ybm100.app.crm.utils.module.normal

import android.content.Context
import android.graphics.Rect
import android.util.Log
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.xyy.common.util.ConvertUtils
import com.ybm100.app.crm.utils.module.IModuleItem
import com.ybm100.app.crm.utils.module.ModulesManager
import com.ybm100.app.crm.utils.module.factory.IModuleFactory
import com.ybm100.app.crm.utils.module.module.BaseModule

class DefaultModuleGroupView(context: Context) : ModulesManager.ModuleGroupView, RecyclerView(context) {
    private lateinit var modulesAdapter: DefaultModulesAdapter

    override fun initView(moduleFactory: IModuleFactory<out BaseModule<*>>) {
        layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false).also {
            it.recycleChildrenOnDetach = true
        }
        adapter = DefaultModulesAdapter(context, moduleFactory).also { adapter ->
            modulesAdapter = adapter
        }
        layoutParams = MarginLayoutParams(
                MarginLayoutParams.MATCH_PARENT,
                MarginLayoutParams.MATCH_PARENT)
        addItemDecoration(object : ItemDecoration() {
            override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: State) {
                val childAdapterPosition = parent.getChildAdapterPosition(view)
                if (childAdapterPosition == modulesAdapter.itemCount - 1) {
                    outRect.bottom = ConvertUtils.dp2px(5f)
                } else {
                    outRect.bottom = 0
                }
            }
        })
    }

    override fun setModulesData(moduleItems: List<IModuleItem>) {
        modulesAdapter.setModulesData(moduleItems)
    }

    override fun refresh() {
        getItemViews().forEach { view ->
            view?.let {
                if (it is BaseModule<*>) {
                    it.onRefresh()
                }
            }
        }
    }

    private fun getItemViews(): List<View?> {
        return ArrayList<View?>().also {
            for (index in 0 until modulesAdapter.itemCount) {
                it.add(findViewHolderForAdapterPosition(index)?.itemView)
            }
        }
    }

    override fun refresh(typeId: Int) {
        for (index in 0 until modulesAdapter.itemCount) {
            if (adapter?.getItemViewType(index) == typeId) {
                val itemView = findViewHolderForAdapterPosition(index)?.itemView
                if (itemView is BaseModule<*>) {
                    itemView.onRefreshSpecial()
                    return
                }
            }
        }
    }


    override fun hasModule(typeId: Int): Boolean {
        for (index in 0 until modulesAdapter.itemCount) {
            if (adapter?.getItemViewType(index) == typeId) {
                return true
            }
        }
        return false
    }

    override fun getCurrentItems(): List<IModuleItem> {
        return modulesAdapter.getCurrentItems()
    }

    override fun notifyDataSetChanged() {
        modulesAdapter.notifyDataSetChanged()
    }

}