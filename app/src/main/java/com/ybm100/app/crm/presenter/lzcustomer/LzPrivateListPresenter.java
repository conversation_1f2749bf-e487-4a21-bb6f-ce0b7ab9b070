package com.ybm100.app.crm.presenter.lzcustomer;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.drugstore.PrivateListFilterBean;
import com.ybm100.app.crm.bean.lzcustomer.LzPrivateListBean;
import com.ybm100.app.crm.contract.lzcustomer.LzPrivateListContract;
import com.ybm100.app.crm.model.lzcustomer.LzPrivateListModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

import java.util.HashMap;

/**
 * <AUTHOR>
 * Created by XyyMvpSportTemplate on 12/20/2018 10:43
 * 我的药店列表 - Fragment
 */
public class LzPrivateListPresenter extends BasePresenter<LzPrivateListContract.ILzPrivateListModel, LzPrivateListContract.ILzPrivateListView> {
    int pageSize = 10;
    int pageNo = 0;

    public static LzPrivateListPresenter newInstance() {
        return new LzPrivateListPresenter();
    }

    @Override
    protected LzPrivateListModel getModel() {
        return LzPrivateListModel.newInstance();
    }

    //获取我的药店列表
    public void getListData(final boolean refresh, HashMap<String, String> map) {
        if (mIView == null || mIModel == null) return;
        if (refresh) {
            pageNo = 0;
            mIView.enableLoadMore(true);
        }
        map.put("offset", String.valueOf(pageNo));
        map.put("limit", String.valueOf(pageSize));
        mRxManager.register(mIModel.getPrivateListData(map)
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean<LzPrivateListBean>>(mIView, true) {
                    @Override
                    public void onSuccess(RequestBaseBean<LzPrivateListBean> listRequestBaseBean) {
                        if (mIView == null) return;
                        if (listRequestBaseBean.getData() == null) {
                            mIView.showEmpty();
                            return;
                        }
                        LzPrivateListBean result = listRequestBaseBean.getData();
                        if (result != null) {
                            if (listRequestBaseBean.getData().getLastPage()) {
                                mIView.loadMoreComplete();//超出一页没有更多的数据
                            }
                            mIView.getListDataSuccess(refresh, listRequestBaseBean);
                        }
                        pageNo++;
                    }
                }, new SimpleErrorConsumer(mIView)));
    }
    public void getFilterItems() {
        if (mIView == null || mIModel == null) return;
        mRxManager.register(mIModel.getFilterItems()
                .subscribe(new SimpleSuccessConsumer<RequestBaseBean<PrivateListFilterBean>>(mIView, true) {
                    @Override
                    public void onSuccess(RequestBaseBean<PrivateListFilterBean> listRequestBaseBean) {
                        if (mIView == null) return;
                        mIView.getFilterItemsSuccess(listRequestBaseBean);
                    }
                }, new SimpleErrorConsumer(mIView) {
                    @Override
                    public void accept(Throwable throwable) throws Exception {
                        mIView.showToast(throwable.getMessage());
                    }
                }));
    }
}

