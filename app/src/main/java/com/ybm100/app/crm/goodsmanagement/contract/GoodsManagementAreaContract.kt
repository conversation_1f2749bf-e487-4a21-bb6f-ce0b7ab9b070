package com.ybm100.app.crm.goodsmanagement.contract

import com.xyy.utilslibrary.base.IBaseActivity
import com.xyy.utilslibrary.base.IBaseModel
import com.xyy.utilslibrary.base.bean.RequestBaseBean
import com.ybm100.app.crm.goodsmanagement.bean.AvailableZone
import com.ybm100.app.crm.goodsmanagement.bean.GoodsManagementAreaListBean
import io.reactivex.Observable


class GoodsManagementAreaContract {
    interface IGoodsManagementAreaView : IBaseActivity {
        fun onGetGoodsManagementAreaListSuccess(data: RequestBaseBean<GoodsManagementAreaListBean?>?)
        fun onGetGoodsManagementAreaListFail()
        fun onGetAvailableZoneSuccess(data: RequestBaseBean<AvailableZone?>?)
        fun onGetAvailableZoneFail()
    }

    interface IGoodsManagementAreaModel : IBaseModel {
        fun getGoodsManagementAreaList(): Observable<RequestBaseBean<GoodsManagementAreaListBean?>?>
        fun getAvailableZone(queryMap: Map<String, String>): Observable<RequestBaseBean<AvailableZone?>?>
    }
}