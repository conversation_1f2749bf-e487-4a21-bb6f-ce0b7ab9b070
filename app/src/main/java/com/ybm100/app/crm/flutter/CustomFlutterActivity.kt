package com.ybm100.app.crm.flutter

import android.Manifest
import android.annotation.SuppressLint
import android.app.ActivityManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.os.Bundle
import android.util.Log
import com.baidu.location.BDAbstractLocationListener
import com.baidu.location.BDLocation
import com.baidu.location.LocationClient
import com.baidu.location.LocationClientOption
import com.gyf.barlibrary.ImmersionBar
import com.tbruyelle.rxpermissions2.RxPermissions
import com.umeng.message.PushAgent
import com.umeng.socialize.UMShareAPI
import com.xyy.canary.AppUpdate
import com.xyy.common.util.ToastUtils
import com.xyy.flutter.container.container.ContainerRuntime
import com.xyy.flutter.container.container.ui.FlutterRunnerActivity
import com.xyy.userbehaviortracking.utils.UserBehaviorTrackingUtils
import com.xyy.utilslibrary.AppManager
import com.xyy.utilslibrary.RxManager
import com.xyy.utilslibrary.base.BasePresenter
import com.xyy.utilslibrary.base.IBaseView
import com.xyy.utilslibrary.net.NetworkUtils
import com.ybm100.app.crm.R
import com.ybm100.app.crm.flutter.view.GoodsManagementPlatformViewFactory
import com.ybm100.app.crm.permission.PermissionUtil
import com.ybm100.app.crm.schedule.service.CallRecordManager.handleCallRecord
import com.ybm100.app.crm.utils.SharedPrefManager
import io.flutter.embedding.engine.FlutterEngine
import ly.count.android.sdk.XyyApmCly

class CustomFlutterActivity : FlutterRunnerActivity(), IBaseView {

    var mRxManager = RxManager()

    private var netBroadcastReceiver: BroadcastReceiver? = null
    private var isRegistered = false

    override fun onCreate(savedInstanceState: Bundle?) {
        UserBehaviorTrackingUtils.track("CustomFlutterActivity-onCreate")
        super.onCreate(savedInstanceState)
        ImmersionBar.with(this).statusBarColor(R.color.transparent).statusBarDarkFont(true).init();
        checkIntentEvent()
        overridePendingTransition(
            R.anim.platform_activity_start_zoom_in,
            R.anim.platform_activity_start_zoom_out
        )
//        checkMemoryInfo(savedInstanceState)
        if (isMainPage()) {
            // 首页
            initMainPage()
        }
        AppManager.getAppManager().addActivity(this)
    }


    private fun isMainPage(): Boolean {
        return intent.getStringExtra("uri_path") == "/main";
    }

    private fun addPushAlias() {
        val mPushAgent = PushAgent.getInstance(applicationContext)
        var oaId = ""
        val infoBean = SharedPrefManager.getInstance().userInfo
        if (infoBean != null) {
            oaId = infoBean.sysUserId
        }
        mPushAgent.addAlias(oaId, "CRMACCOUNT") { isSuccess, message -> }
    }


    private fun checkMemoryInfo(savedInstanceState: Bundle?) {
        if (savedInstanceState != null) {
            try {
                val mi = ActivityManager.MemoryInfo()
                (getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager).getMemoryInfo(mi)
                val availMemory = mi.availMem / 1024 / 1024
                val totalMemory = mi.totalMem / 1024 / 1024
                showToast("页面已重建:$availMemory,$totalMemory")
            } catch (e: Exception) {
                showToast("页面已重建")
            }
        }
    }


    override fun onResume() {
        super.onResume()
        Log.e(
            "guan",
            "configureFlutterEngine onResume setCurrentEngine:" + flutterEngine?.dartExecutor?.binaryMessenger.hashCode()
        )
        com.baidu.flutter_bmflocation.MethodChannelManager.getInstance()
            .setCurrentEngine(flutterEngine)
        com.baidu.mapapi.search.MethodChannelManager.getInstance().setCurrentEngine(flutterEngine)
    }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        com.baidu.flutter_bmflocation.MethodChannelManager.getInstance()
            .setCurrentEngine(flutterEngine)
        com.baidu.mapapi.search.MethodChannelManager.getInstance().setCurrentEngine(flutterEngine)
        flutterEngine
            .platformViewsController
            .registry
            .registerViewFactory("all_goods_view_id", GoodsManagementPlatformViewFactory())
        Log.e(
            "guan",
            "configureFlutterEngine setCurrentEngine:" + flutterEngine.dartExecutor.binaryMessenger.hashCode()
        )
    }

    private fun checkIntentEvent() {
        val hasReset = intent.getBooleanExtra("xyy_reset_engine", false)
        if (hasReset) {
            UserBehaviorTrackingUtils.track("xyy-flutter-reset-engine")
        }
    }

    override fun onDestroy() {
        UserBehaviorTrackingUtils.track("CustomFlutterActivity-onDestroy")
        var originIds: String? = null
        originIds = try {
            ContainerRuntime.engineManager.getEngineCacheIds().let {
                it.joinToString()
            }
        } catch (e: Exception) {
            UserBehaviorTrackingUtils.track(
                "xyy-flutter-destroy-clean-error1",
                mapOf("error" to e.toString())
            )
            null
        }

        super.onDestroy()

        try {
            val newIds = ContainerRuntime.engineManager.getEngineCacheIds().let {
                it.joinToString()
            }

            if (originIds != null && originIds != newIds) {
                UserBehaviorTrackingUtils.track(
                    "xyy-flutter-destroy-clean",
                    mapOf("old" to originIds, "new" to newIds)
                )
            }
        } catch (e: Exception) {
            UserBehaviorTrackingUtils.track(
                "xyy-flutter-destroy-clean-error2",
                mapOf("error" to e.toString())
            )
        }
        mRxManager.unSubscribe()
        if (isRegistered) {
            unregisterReceiver(netBroadcastReceiver)
        }
        AppManager.getAppManager().removeActivity(this)
    }

    override fun showToast(msg: String?) {
        ToastUtils.showLongSafe(msg ?: "")
    }

    override fun showWaitDialog(waitMsg: String?) {
    }

    override fun hideWaitDialog() {
    }

    override fun hideKeyboard() {
    }

    override fun initPresenter(): BasePresenter<*, *> {
        return object : BasePresenter<Any?, Any?>() {
            override fun getModel(): Any? {
                return null
            }
        }
    }

    override fun showNetError() {
    }

    override fun back() {
    }

    private fun initMainPage() {
        registerNetWorkReceiver()
        addPushAlias()
        handleCallRecord("start")
    }

    /**
     * canary - 应用升级系统对接
     */
    @SuppressLint("CheckResult")
    private fun checkCanaryUpdate() {
        RxPermissions(this).requestEach(Manifest.permission.ACCESS_FINE_LOCATION)
            .subscribe { permission ->
                if (permission.granted) {
                    LocationClient.setAgreePrivacy(true)
                    //声明LocationClient类实例并配置定位参数
                    val locationOption = LocationClientOption()
                    //可选，默认高精度，设置定位模式，高精度，低功耗，仅设备
                    locationOption.locationMode = LocationClientOption.LocationMode.Hight_Accuracy
                    //可选，默认gcj02，设置返回的定位结果坐标系，如果配合百度地图使用，建议设置为bd09ll;
                    locationOption.setCoorType("bd09ll")
                    //可选，默认0，即仅定位一次，设置发起连续定位请求的间隔需要大于等于1000ms才是有效的
                    //locationOption.setScanSpan(1000);
                    //可选，设置是否需要地址信息，默认不需要
                    locationOption.setIsNeedAddress(true)
                    //可选，设置是否需要地址描述
                    locationOption.setIsNeedLocationDescribe(false)
                    //可选，设置是否需要设备方向结果
                    locationOption.setNeedDeviceDirect(false)
                    //可选，默认false，设置是否当gps有效时按照1S1次频率输出GPS结果
                    locationOption.isLocationNotify = false
                    //可选，默认true，定位SDK内部是一个SERVICE，并放到了独立进程，设置是否在stop的时候杀死这个进程，默认不杀死
                    locationOption.setIgnoreKillProcess(true)
                    //可选，默认false，设置是否需要位置语义化结果，可以在BDLocation.getLocationDescribe里得到，结果类似于“在北京天安门附近”
                    locationOption.setIsNeedLocationDescribe(false)
                    //可选，默认false，设置是否需要POI结果，可以在BDLocation.getPoiList里得到
                    locationOption.setIsNeedLocationPoiList(false)
                    //可选，默认false，设置是否收集CRASH信息，默认收集
                    locationOption.SetIgnoreCacheException(false)
                    //可选，默认false，设置是否开启Gps定位
                    locationOption.isOpenGps = true
                    //可选，默认false，设置定位时是否需要海拔信息，默认不需要，除基础定位版本都可用
                    locationOption.setIsNeedAltitude(false)
                    //设置打开自动回调位置模式，该开关打开后，期间只要定位SDK检测到位置变化就会主动回调给开发者
                    locationOption.setOpenAutoNotifyMode(
                        3000,
                        1,
                        LocationClientOption.LOC_SENSITIVITY_HIGHT
                    )

                    //定位服务的客户端。宿主程序在客户端声明此类，并调用，目前只支持在主线程中启动
                    val locationClient = LocationClient(applicationContext)
                    locationClient.locOption = locationOption
                    //注册监听函数
                    locationClient.registerLocationListener(object : BDAbstractLocationListener() {
                        override fun onReceiveLocation(bdLocation: BDLocation) {
                            var lat = ""
                            var lng = ""
                            if (null != bdLocation) {
                                lat = bdLocation.latitude.toString()
                                lng = bdLocation.longitude.toString()
                            }
                            XyyApmCly.getInstance()
                                .updateLocation(bdLocation.country, bdLocation.city, lat, lng)
                            locationClient.stop()
                            AppUpdate.getInstance()
                                .setAppUserId(SharedPrefManager.getInstance().userInfo.sysUserId)
                                .setAppUserToken(SharedPrefManager.getInstance().userInfo.token)
                                .start(lat, lng, false)
                        }
                    })

                    //开始定位
                    locationClient.start()
                } else if (permission.shouldShowRequestPermissionRationale) {
                    ToastUtils.showShort(resources.getString(R.string.please_open_location_permission))
                } else {
                    PermissionUtil.showPermissionDialog(
                        this,
                        resources.getString(R.string.location_permission_name),
                        true,
                        null
                    )
                }
            }
    }

    fun onNetWorkConnection(isConnect: Boolean) {
        if (isConnect) {
            checkCanaryUpdate()
        }
    }

    private fun registerNetWorkReceiver() {
        //注册网络状态监听广播
        netBroadcastReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                onNetWorkConnection(NetworkUtils.isAvailable(context))
            }
        }
        val filter = IntentFilter()
        filter.addAction(ConnectivityManager.CONNECTIVITY_ACTION)
        registerReceiver(netBroadcastReceiver, filter)
        isRegistered = true
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        UMShareAPI.get(this).onActivityResult(requestCode,resultCode,data);
    }

}

