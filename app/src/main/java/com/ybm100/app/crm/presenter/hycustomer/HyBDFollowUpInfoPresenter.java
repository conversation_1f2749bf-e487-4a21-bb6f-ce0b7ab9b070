package com.ybm100.app.crm.presenter.hycustomer;

import com.xyy.utilslibrary.base.BasePresenter;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.hycustomer.HyBDFollowUpInfoBean;
import com.ybm100.app.crm.contract.hycustomer.HyBDFollowUpInfoContract;
import com.ybm100.app.crm.model.hycustomer.HyBDFollowUpInfoModel;
import com.ybm100.app.crm.net.helper.SimpleErrorConsumer;
import com.ybm100.app.crm.net.helper.SimpleSuccessConsumer;

import java.util.HashMap;
import java.util.List;

/**
 * 荷叶健康bd跟进信息presenter
 */
public class HyBDFollowUpInfoPresenter extends BasePresenter<HyBDFollowUpInfoContract.IHyBDFollowUpInfoModel, HyBDFollowUpInfoContract.IHyBDFollowUpInfoView> {
    int pageSize = 10;
    int pageNo = 0;

    public static HyBDFollowUpInfoPresenter newInstance() {
        return new HyBDFollowUpInfoPresenter();
    }

    @Override
    protected HyBDFollowUpInfoModel getModel() {
        return HyBDFollowUpInfoModel.newInstance();
    }

    public void getBDFollowUpInfoList(boolean refresh, HashMap<String, String> map) {
        if (mIView == null || mIModel == null) return;
        if (refresh) {
            pageNo = 0;
            mIView.enableLoadMore(false);
        }
        map.put("offset", String.valueOf(pageNo));
        map.put("limit", String.valueOf(pageSize));
        mRxManager.register(mIModel.getBDFollowUpInfoList(map).subscribe(new SimpleSuccessConsumer<RequestBaseBean<List<HyBDFollowUpInfoBean>>>(mIView) {
            @Override
            public void onSuccess(RequestBaseBean<List<HyBDFollowUpInfoBean>> listRequestBaseBean) {
                if (mIView == null) return;
                if (listRequestBaseBean != null && listRequestBaseBean.getData() != null) {
//                    if (listRequestBaseBean.getData().isLastPage()) {
//                        mIView.loadMoreComplete();//超出一页没有更多的数据
//                    }
                    mIView.getBDFollowUpInfoListSuccess(refresh, listRequestBaseBean);
                }
                pageNo++;
            }

            @Override
            public void onFailure(int errorCode) {
                super.onFailure(errorCode);
                mIView.getBDFollowUpInfoListFail();
            }
        }, new SimpleErrorConsumer(mIView) {
            @Override
            protected void onError(Throwable throwable, String msg) {
                super.onError(throwable, msg);
                mIView.showNetError();
            }
        }));
    }
}
