package com.ybm100.app.crm.goodsmanagement.adapter

import androidx.core.content.ContextCompat
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.xyy.common.widget.RoundTextView
import com.ybm100.app.crm.R
import com.ybm100.app.crm.goodsmanagement.bean.GoodsManagementListBean


class ItemLabelAdapter(data: List<GoodsManagementListBean.Row.Tag?>?) : BaseQuickAdapter<GoodsManagementListBean.Row.Tag, BaseViewHolder>(R.layout.item_label, data) {

    override fun convert(helper: BaseViewHolder, item: GoodsManagementListBean.Row.Tag?) {
        val view = helper.getView<RoundTextView>(R.id.rtv_content)
        item?.run {
            view.apply {
                text = name ?: ""
                if (uiType == 1) {
                    setStrokeColor(ContextCompat.getColor(mContext, R.color.text_color_FF7200))
                    setTextColor(ContextCompat.getColor(mContext, R.color.text_color_FF7200))
                } else if (uiType == 2) {
                    setStrokeColor(ContextCompat.getColor(mContext, R.color.color_FF4741))
                    setTextColor(ContextCompat.getColor(mContext, R.color.color_FF4741))
                }
            }
        }

    }

}