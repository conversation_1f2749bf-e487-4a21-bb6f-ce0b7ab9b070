package com.ybm100.app.crm.goodsmanagement.adapter

import androidx.core.content.ContextCompat
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.google.android.flexbox.AlignItems
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.FlexboxLayoutManager
import com.xyy.common.widget.RoundTextView
import com.xyy.utilslibrary.utils.GlideLoadUtils
import com.ybm100.app.crm.R
import com.ybm100.app.crm.api.ApiUrl
import com.ybm100.app.crm.constant.Constants
import com.ybm100.app.crm.goodsmanagement.adapter.GoodsManagementAdapter.CustomerViewHolder
import com.ybm100.app.crm.goodsmanagement.bean.GoodsManagementListBean
import com.ybm100.app.crm.utils.SpannableStringUtils
import kotlinx.android.synthetic.main.item_goods_management.view.*


class GoodsManagementAdapter(private val type: Int) : BaseQuickAdapter<GoodsManagementListBean.Row, CustomerViewHolder>(R.layout.item_goods_management) {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CustomerViewHolder {
        super.onCreateViewHolder(parent, viewType)
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_goods_management, parent, false)
        view.apply {
            when (type) {
                Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION -> {
                    tv_collect.visibility = View.GONE
                    easy_swipe_menu_layout.isCanRightSwipe = true
                    iv_radio.visibility = View.GONE

                    tv_delete.text = "取消收藏"
                }
                Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION_SEARCH -> {
                    tv_collect.visibility = View.GONE
                    easy_swipe_menu_layout.isCanRightSwipe = true
                    iv_radio.visibility = View.GONE

                    tv_delete.text = "取消收藏"
                }
                Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION_RECOMMENDATION -> {
                    iv_radio.visibility = View.VISIBLE
                    easy_swipe_menu_layout.isCanRightSwipe = false
                    tv_collect.visibility = View.GONE
                }
                Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_MY_COLLECTION_RECOMMENDATION_SEARCH -> {
                    iv_radio.visibility = View.VISIBLE
                    easy_swipe_menu_layout.isCanRightSwipe = false
                    tv_collect.visibility = View.GONE
                }
                Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_ALL_GOODS -> {
                    iv_radio.visibility = View.GONE
                    easy_swipe_menu_layout.isCanRightSwipe = false
                    tv_collect.visibility = View.VISIBLE
                }
                Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_ALL_GOODS_SEARCH -> {
                    iv_radio.visibility = View.GONE
                    easy_swipe_menu_layout.isCanRightSwipe = false
                    tv_collect.visibility = View.VISIBLE
                }
                Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS -> {
                    iv_radio.visibility = View.GONE
                    easy_swipe_menu_layout.isCanRightSwipe = false
                    tv_collect.visibility = View.VISIBLE
                }
                Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_SEARCH -> {
                    iv_radio.visibility = View.GONE
                    easy_swipe_menu_layout.isCanRightSwipe = false
                    tv_collect.visibility = View.VISIBLE

                }
                Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_RECOMMENDATION -> {
                    iv_radio.visibility = View.VISIBLE
                    easy_swipe_menu_layout.isCanRightSwipe = false
                    tv_collect.visibility = View.GONE
                }
                Constants.GoodsManagement.CONSTANT_FRAGMENT_TYPE_CUSTOMER_ALL_GOODS_RECOMMENDATION_SEARCH -> {
                    iv_radio.visibility = View.VISIBLE
                    easy_swipe_menu_layout.isCanRightSwipe = false
                    tv_collect.visibility = View.GONE
                }
                Constants.GoodsManagement.CONSTANT_ADAPTER_CART -> {
                    tv_collect.visibility = View.GONE
                    easy_swipe_menu_layout.isCanRightSwipe = true
                    iv_radio.visibility = View.GONE

                    tv_delete.text = "删除"
                }
            }
        }
        val customerViewHolder = CustomerViewHolder(view)
        customerViewHolder.setAdapter(this)
        return customerViewHolder
    }


    override fun convert(helper: CustomerViewHolder, item: GoodsManagementListBean.Row?) {
        item?.run {
            helper.addOnClickListener(R.id.cl_content)
            helper.addOnClickListener(R.id.tv_collect)
            helper.addOnClickListener(R.id.tv_delete)

            GlideLoadUtils.loadImgWithUrl(mContext, "${ApiUrl.CDN_URL}/ybm/product/${imageUrl}", helper.getView<ImageView>(R.id.iv_cover), R.drawable.icon_load_failed)
            helper.setText(R.id.tv_retail_price, "零售价 ¥${suggestPrice ?: ""}")
                    .setText(R.id.tv_name, showName ?: "")
                    .setText(R.id.tv_specification, spec ?: "")
                    .setText(R.id.tv_final_price_and_gross_rate, SpannableStringUtils.highlightStart(mContext, R.color.color_FF2121, "¥${fob ?: ""}", "（终端毛利率${grossMargin ?: ""}）"))

            /**
             * 选中状态
             */
            if (isSelected == true) {
                helper.setImageResource(R.id.iv_radio, R.drawable.icon_radio_btn_selected)
            } else {
                helper.setImageResource(R.id.iv_radio, R.drawable.icon_radio_btn_normal)
            }

            /**
             * 收藏状态
             */
            if (collect == 1) {
                helper.getView<TextView>(R.id.tv_collect).apply {
                    setCompoundDrawablesWithIntrinsicBounds(0, R.drawable.ic_collect_green, 0, 0)
                    text = "已收藏"
                }
            } else {
                helper.getView<TextView>(R.id.tv_collect).apply {
                    setCompoundDrawablesWithIntrinsicBounds(0, R.drawable.ic_collect, 0, 0)
                    text = "收藏"
                }
            }

            /**
             * 活动、效期标签
             */
            val labelRecyclerView = helper.getView<RecyclerView>(R.id.recycler_view_label)

            labelRecyclerView.apply {
                layoutManager = FlexboxLayoutManager(context).apply {
                    flexWrap = FlexWrap.WRAP
                    flexDirection = FlexDirection.ROW
                    alignItems = AlignItems.STRETCH
                }
                adapter = ItemLabelAdapter(tagList)
            }
            if (tagList?.size ?: 0 > 0) {
                labelRecyclerView.visibility = View.VISIBLE
            } else {
                labelRecyclerView.visibility = View.GONE
            }

            /**
             * 商品标签（已加购）
             */
            val tags = arrayOf(helper.getView<TextView>(R.id.rtv_customer_tag1), helper.getView<TextView>(R.id.rtv_customer_tag2))
            repeat(2) {
                if (it < productLabelList?.size ?: 0) {
                    tags.getOrNull(it)?.let { tag ->
                        val tagText = productLabelList?.getOrNull(it)?.tagName ?: ""
                        tag.visibility = if (tagText.isEmpty()) {
                            View.GONE
                        } else {
                            View.VISIBLE
                        }
                        tags.getOrNull(it)?.text = tagText
                    }
                } else {
                    tags.getOrNull(it)?.visibility = View.GONE
                }
            }
            val tagLineView = helper.getView<View>(R.id.v_customer_tag_line)
            tagLineView.visibility = if (tags[0].visibility == View.VISIBLE && tags[1].visibility == View.VISIBLE) {
                View.VISIBLE
            } else {
                View.GONE
            }

            /**
             * 预估到手价
             */
            if (!discountPrice.isNullOrEmpty()) {
                helper.setGone(R.id.tv_estimated_price, true)
                helper.setText(R.id.tv_estimated_price, SpannableStringUtils.estimatedPrice(mContext, discountPrice))
            } else {
                helper.setGone(R.id.tv_estimated_price, false)
            }

            /**
             * 专区标签
             */
            val zoneTagsView = helper.getView<RecyclerView>(R.id.recycler_view_zone_label)
            if (zoneList != null && zoneList.isNotEmpty()) {
                zoneTagsView.visibility = View.VISIBLE
                zoneTagsView.layoutManager = FlexboxLayoutManager(zoneTagsView.context)
                zoneTagsView.adapter = GoodsManagementZoneListAdapter(zoneList)
            } else {
                zoneTagsView.visibility = View.GONE
            }

            /**
             * 店铺名
             */
            if (!shopName.isNullOrEmpty()) {
                helper.setGone(R.id.tv_shop_name, true)
                helper.setText(R.id.tv_shop_name, shopName)
            } else {
                helper.setGone(R.id.tv_shop_name, false)
            }
        }
    }

    class CustomerViewHolder(view: View) : BaseViewHolder(view) {

        public override fun setAdapter(adapter: BaseQuickAdapter<*, *>?): BaseViewHolder {
            return super.setAdapter(adapter)
        }
    }
}
