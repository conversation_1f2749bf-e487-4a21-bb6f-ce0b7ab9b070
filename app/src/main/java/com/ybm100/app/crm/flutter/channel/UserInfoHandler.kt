package com.ybm100.app.crm.flutter.channel

import androidx.fragment.app.FragmentActivity
import com.google.gson.Gson
import com.xyy.flutter.container.container.bridge.BaseHandler
import com.ybm100.app.crm.utils.SharedPrefManager

class UserInfoHandler : BaseHandler() {
    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {
        val userInfo = SharedPrefManager.getInstance().userInfo
        if (userInfo != null) {
            success(Gson().toJson(userInfo))
        } else {
            success("{}")
        }
    }
}