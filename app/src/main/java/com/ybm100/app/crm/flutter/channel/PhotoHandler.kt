package com.ybm100.app.crm.flutter.channel

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.tbruyelle.rxpermissions2.RxPermissions
import com.xyy.flutter.container.container.bridge.BaseHandler
import com.ybm100.app.crm.flutter.ErrorCode
import com.ybm100.app.crm.order.activity.AlbumActivity
import com.ybm100.app.crm.order.photo.PhotoBean
import com.ybm100.app.crm.utils.AppFileUtils
import com.ybm100.app.crm.utils.CameraUtils
import com.ybm100.app.crm.utils.SharedPrefManager
import io.reactivex.disposables.Disposable
import java.io.File

class PhotoHandler : BaseHandler() {
    companion object {
        private const val FRAGMENT_TAG = "photo_handler_fragment"
    }

    private var permissionDispose: Disposable? = null


    @SuppressLint("CheckResult")
    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {

        //添加权限 打开相机
        permissionDispose = RxPermissions(activity).requestEach(
                Manifest.permission.CAMERA,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
        ).subscribe { permission ->
            if (Manifest.permission.CAMERA == permission.name) {
                if (!permission.granted) {
                    error(
                            ErrorCode.NO_CAMERA_PERMISSION.errorCode,
                            ErrorCode.NO_CAMERA_PERMISSION.errorMsg
                    )
                    permissionDispose?.dispose()
                }
            } else {
                if (!permission.granted) {
                    error(
                            ErrorCode.NO_STORAGE_PERMISSION.errorCode,
                            ErrorCode.NO_STORAGE_PERMISSION.errorMsg
                    )
                } else {
                    //打开相机或者相册
                    val emptyFragment = EmptyFragment()
                    emptyFragment.setParams(this, params)
                    activity.supportFragmentManager.beginTransaction()
                            .add(emptyFragment, FRAGMENT_TAG).commitAllowingStateLoss()
                }
            }
        }
    }

    class EmptyFragment : Fragment() {

        private var isFirstSelectImg = true

        private var handler: PhotoHandler? = null

        private var photoParams: Map<String, Any?>? = null

        fun setParams(handler: PhotoHandler, params: Map<String, Any?>) {
            photoParams = params
            this.handler = handler
        }

        override fun onCreate(savedInstanceState: Bundle?) {
            super.onCreate(savedInstanceState)
            val type = photoParams?.get("type")?.toString() ?: ""
            val count = photoParams?.get("imageCount")?.toString() ?: ""
            activity?.let {
                when (type) {
                    "Camera" -> {
                        openCamera(it)
                    }
                    "Album" -> {
                        openAlbum(count.toIntOrNull() ?: 1, it)
                    }

                }
            }
        }

        private fun openAlbum(count: Int, activity: FragmentActivity) {
            val intent = Intent(activity, AlbumActivity::class.java)
            //设置最大选择数量
            intent.putExtra(AlbumActivity.MAX_PIECE, count)
            startActivityForResult(intent, CameraUtils.REQUEST_GALLERY)
        }

        private fun openCamera(activity: FragmentActivity) {
            if (isFirstSelectImg) {
                AppFileUtils.getCompressTempImgFile()
                AppFileUtils.clearCompressTempImg()
                isFirstSelectImg = false
            }
            CameraUtils.openCamera(this)
        }

        private fun buildResult(fileList: List<String>) {
            handler?.result?.success(fileList)
            parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
        }

        override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
            super.onActivityResult(requestCode, resultCode, data)
            if (resultCode == Activity.RESULT_OK) {
                when (requestCode) {
                    CameraUtils.REQUEST_GALLERY -> {
                        handleGalleryResult(data)
                    }
                    CameraUtils.REQUEST_CAMERA -> {
                        handleCameraResult()
                    }
                }
            } else {
                handler?.error(ErrorCode.NO_RESULT.errorCode, ErrorCode.NO_RESULT.errorMsg)
                parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
            }
        }

        private fun handleCameraResult() {
            val file = File(SharedPrefManager.getInstance().cameraImaFilePath)
            if (!file.exists()) {
                handler?.error(ErrorCode.NO_EXIST.errorCode, ErrorCode.NO_EXIST.errorMsg)
                parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
                return
            }
            if (file.length() <= 0) {
                handler?.error(ErrorCode.FILE_CORRUPTED.errorCode, ErrorCode.FILE_CORRUPTED.errorMsg)
                parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
                return
            }
            buildResult(listOf(file.absolutePath))
        }

        private fun handleGalleryResult(data: Intent?) {
            val result: List<PhotoBean?>? =
                    data?.getParcelableArrayListExtra(AlbumActivity.EXTRA_RESULT)
            if (result.isNullOrEmpty()) {
                handler?.error(
                        ErrorCode.RESULT_IS_EMPTY.errorCode,
                        ErrorCode.RESULT_IS_EMPTY.errorMsg
                )
                parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
                return
            }
            val resultList = ArrayList<String>()

            result.forEach {
                if (it == null) {
                    handler?.error(ErrorCode.NO_EXIST.errorCode, ErrorCode.NO_EXIST.errorMsg)
                    parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
                    return
                }
                val urlFile = File(it.path)
                val uri = Uri.fromFile(urlFile)
                val file = CameraUtils.handleImageOn19(uri, activity)
                if (file == null || !file.exists()) {
                    handler?.error(ErrorCode.NO_EXIST.errorCode, ErrorCode.NO_EXIST.errorMsg)
                    parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
                    return
                }
                if (file.length() <= 0) {
                    handler?.error(
                            ErrorCode.FILE_CORRUPTED.errorCode,
                            ErrorCode.FILE_CORRUPTED.errorMsg
                    )
                    parentFragmentManager.beginTransaction().remove(this).commitNowAllowingStateLoss()
                    return
                }
                resultList.add(file.absolutePath)
//                val sourceType = data.getIntExtra(AlbumActivity.SOURCE_TYPE, 0)
            }
            buildResult(resultList)
        }
    }


}