package com.ybm100.app.crm.flutter.channel

import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import androidx.fragment.app.FragmentActivity
import com.xyy.common.util.ToastUtils
import com.xyy.flutter.container.container.bridge.BaseHandler
import com.ybm100.app.crm.bean.message.RemarkData
import com.ybm100.app.crm.constant.Constants
import com.ybm100.app.crm.goodsmanagement.activity.PrivateCustomerGoodsManagementActivity.Companion.startActivity
import com.ybm100.app.crm.ui.activity.drugstore.PrivateCustomerDetailActivity
import com.ybm100.app.crm.utils.GsonUtils

class PurchasedJumpHandler : BaseHandler() {

    override fun handle(activity: FragmentActivity, params: Map<String, Any?>) {
        val remark = (params["remark"] ?: "") as String
        val convertedMerchantId: String = convertRemarkToMerchantId(remark)


        if (!TextUtils.isEmpty(convertedMerchantId)) {
            //跳转客户详情
            val bundle = Bundle()
            bundle.putString("merchantId", convertedMerchantId)
            val intent = Intent()
            intent.setClass(activity, PrivateCustomerDetailActivity::class.java)
            intent.putExtras(bundle)
            activity.startActivity(intent)
            startActivity(
                    activity, convertedMerchantId,
                    Constants.GoodsManagement.CONSTANT_CUSTOMER_GOODS_MANAGEMENT_TAB_SHOPPING_CART_GOODS)
        } else {
            ToastUtils.showShort("merchantId解析失败")
        }
    }

    private fun convertRemarkToMerchantId(remark: String): String {
        try {
            val remarkData = GsonUtils.fromJson(remark, RemarkData::class.java)
            if (remarkData != null && remarkData.data != null) {
                return remarkData.data.merchantId
            }
        } catch (ignored: Exception) {
        }
        return ""
    }

}
