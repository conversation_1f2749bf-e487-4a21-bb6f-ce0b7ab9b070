package com.ybm100.app.crm.contract.hycustomer;

import com.xyy.utilslibrary.base.IBaseActivity;
import com.xyy.utilslibrary.base.IBaseModel;
import com.xyy.utilslibrary.base.bean.RequestBaseBean;
import com.ybm100.app.crm.bean.hycustomer.HyPublicListBean;

import java.util.HashMap;

import io.reactivex.Observable;

/**
 * 荷叶健康公海客户接口
 */
public interface HyPublicListContract{

    interface IHyPublicListModel extends IBaseModel {
        //公海列表
        Observable<RequestBaseBean<HyPublicListBean>> searchOpenSea(HashMap<String, String> map);

        //认领药店
        Observable<RequestBaseBean> receive(String id);
    }

    interface IHyPublicListView extends IBaseActivity {
        void searchOpenSeaSuccess(boolean refresh, RequestBaseBean<HyPublicListBean> baseBean);

        void receiveSuccess(RequestBaseBean baseBean);

        void enableLoadMore(boolean b);

        void loadMoreComplete();

        void showEmpty();
    }

}
