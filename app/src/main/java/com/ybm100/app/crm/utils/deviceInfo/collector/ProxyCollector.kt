package com.ybm100.app.crm.utils.deviceInfo.collector

import android.content.Context
import java.net.NetworkInterface

class ProxyCollector : BaseCollector() {
    override fun internalCollect(context: Context): String? {
        return isProxy() + "," + isVPN()
    }

    private fun isProxy(): String? {
        return try {
            val result = System.getProperty("http.proxyHost")
            val proxy = System.getProperty("http.proxyPort")
            val port: Int = proxy?.toIntOrNull() ?: "-1".toInt()
            if (result != null && port != -1) "1" else "0"
        } catch (var3: Throwable) {
            "0"
        }
    }

    private fun isVPN(): String? {
        return try {
            val interfaces = NetworkInterface.getNetworkInterfaces()
            if (interfaces == null) {
                "0"
            } else {
                val it = interfaces.iterator()
                var ni: NetworkInterface
                do {
                    if (!it.hasNext()) {
                        return "0"
                    }
                    ni = it.next() as NetworkInterface
                } while ((!ni.isUp || ni.interfaceAddresses.size == 0 || "tun0" != ni.name) && "ppp0" != ni.name)
                "1"
            }
        } catch (throwable: Throwable) {
            "0"
        }
    }

}
